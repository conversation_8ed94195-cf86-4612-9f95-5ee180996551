import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';

class HomeTasksSectionShimmer extends StatelessWidget {
  const HomeTasksSectionShimmer();
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tasks'.tr,
            style: Theme.of(context).textTheme.titleMedium!.copyWith(
                color: AppStyle.mediumBlackTextColor,
                fontWeight: FontWeight.w900
            ),
          ),
          const SizedBox(height: 12,),
          Wrap(
            spacing: 10,
            runSpacing: 10,
            crossAxisAlignment: WrapCrossAlignment.start,
            children: [
              SkeletonWidget(
                width: 175,
                height: 45,
                radius: 20,
              ),
              SkeletonWidget(
                width: 155,
                height: 45,
                radius: 20,
              ),
              SkeletonWidget(
                width: 240,
                height: 45,
                radius: 20,
              ),
              SkeletonWidget(
                width: 155,
                height: 45,
                radius: 20,
              ),
              SkeletonWidget(
                width: 180,
                height: 45,
                radius: 20,
              ),
            ],
          ),
        ],
      ),
    );
  }
}
