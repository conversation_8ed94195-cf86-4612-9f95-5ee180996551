name: all_in_one
description: A new Flutter project.
publish_to: "none"

version: 1.0.7+8
environment:
  sdk: ">=3.3.4 <4.0.0"

dependencies:
  animated_flip_counter: ^0.3.4
  another_stepper: ^1.2.2
  background_downloader:
    path: ./packages/background_downloader.
  blur: ^4.0.0
  cached_network_image: ^3.3.1
  carousel_slider: ^5.0.0
  chewie: ^1.8.1
  convert: ^3.1.2
  country_pickers: ^3.0.1
  cupertino_icons: ^1.0.2
  device_info_plus: ^11.0.0
  dio: ^5.4.3+1
  dotted_border: ^2.0.0
  dropdown_button2: ^2.3.9
  dropdown_search: ^6.0.0
  encrypt: ^5.0.3
  firebase_core: ^3.2.0
  firebase_messaging: ^15.0.3
  flutter:
    sdk: flutter
  flutter_cached_pdfview: ^0.4.2
  flutter_calendar_carousel: ^2.4.3
  flutter_gen: null
  flutter_image_compress: ^2.2.0
  flutter_inappwebview: ^6.1.0
  flutter_launcher_icons: any
  flutter_local_notifications: ^17.1.1
  flutter_localizations:
    sdk: flutter
  flutter_rating_bar: ^4.0.1
  flutter_staggered_animations: ^1.1.1
  flutter_staggered_grid_view: ^0.7.0
  flutter_svg: null
  fluttertoast: ^8.2.5
  gesture_zoom_box: ^0.0.4
  get: ^4.1.4
  get_storage: null
  google_fonts: ^6.2.1
  hive: null
  hive_flutter: null
  image_picker: null
  in_app_update: ^4.2.2
  intl: ^0.19.0
  just_audio: ^0.9.29
  lottie: ^3.1.0
  package_info_plus: ^8.0.0
  path: null
  path_provider: ^2.0.15
  percent_indicator: ^4.2.3
  permission_handler: ^11.3.1
  pinput: ^5.0.0
  pointycastle: ^3.7.0
  rename: ^3.0.2
  safe_device: ^1.1.9
  screen_protector: ^1.4.2
  secure_application: ^4.1.0
  share_plus: ^10.1.4
  shimmer: null
  skeletonizer: ^1.4.2
  smooth_page_indicator: ^1.0.1
  story: ^1.1.0
  url_launcher: ^6.1.5
  vibration: ^2.0.1
  video_player: ^2.7.2
  wakelock_plus: ^1.2.5
  ffmpeg_kit_flutter_new: ^2.0.0
  webview_flutter: ^4.7.0
  youtube_player_flutter:
    path: ./youtube_player_flutter

dev_dependencies:
  build_runner: null
  flutter_gen_runner: null
  flutter_test:
    sdk: flutter
  hive_generator: null

flutter_gen:
  output: lib/core/style/
  integrations:
    flutter_svg: true
    lottie: true

flutter:
  uses-material-design: true
  fonts:
    - family: NotoSans
      fonts:
        - asset: assets/fonts/NotoSansArabic.ttf

    - family: LamaSans
      fonts:
        - asset: assets/fonts/LamaSans-Thin.ttf
          weight: 100
        - asset: assets/fonts/LamaSans-ExtraLight.ttf
          weight: 200
        - asset: assets/fonts/LamaSans-Light.ttf
          weight: 300
        - asset: assets/fonts/LamaSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/LamaSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/LamaSans-Bold.ttf
          weight: 600
        - asset: assets/fonts/LamaSans-SemiBold.ttf
          weight: 700
        - asset: assets/fonts/LamaSans-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/LamaSans-Black.ttf
          weight: 900

  assets:
    - assets/
    - assets/images/
    - assets/icons/
    - assets/gif/
    - assets/fonts/
    - assets/lottie/
    - assets/voices/
    - assets/videos/
# dependency_overrides:
#       win32: ^5.5.4
