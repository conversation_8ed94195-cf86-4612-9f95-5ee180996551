class HomeSubjectModel {
  int subjectId;
  String title;
  int sectionsCnt;
  String teacherName;
  int teacherId;
  String yearName;
  String media;

  HomeSubjectModel(
      {required this.title,
      required this.subjectId,
      required this.sectionsCnt,
      required this.teacherName,
      required this.yearName,
      required this.teacherId,
      required this.media});

  factory HomeSubjectModel.fromJson(Map<String, dynamic> json) =>
      HomeSubjectModel(
          subjectId: json['id'],
          title: json['title'],
          sectionsCnt: json['sections_cnt'] ?? 1,
          teacherName: json['teacher_name'],
          yearName: json['year_name'],
          teacherId: json['teacher_id'],
          media: json['main_image']?['original_url'] ?? '');
}
