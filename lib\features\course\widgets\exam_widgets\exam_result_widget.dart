
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';

import 'animated_number_widget.dart';

class ExamResultWidget extends StatelessWidget {
  const ExamResultWidget({
    Key? key,
    required this.totalQuestionsNumber,
    this.showExamResult = false,
    this.fullMark = 00,
    this.deservedMark = 00,
    this.correctAnswersNumber = 0,
    this.wrongAnswersNumber = 0,
    required this.examTitle,
  }) : super(key: key);

  final bool showExamResult;
  final int totalQuestionsNumber;
  final int fullMark;
  final int deservedMark;
  final int correctAnswersNumber;
  final int wrongAnswersNumber;
  final String examTitle;

  @override
  Widget build(BuildContext context) {
    final totalQuestionsNumber = this.totalQuestionsNumber;

    return Container(
      padding: const EdgeInsets.all(16),
      margin: EdgeInsets.symmetric(horizontal: 15,vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              blurRadius: 4,
              spreadRadius: 3,
              offset: const Offset(-2, 1) // changes position of shadow
          ),
        ],
      ),
      child: Row(
        children: [

            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Number of questions'.tr,
                  style: TextStyle(
                    color: AppStyle.primaryColor,
                  ),
                ),
                Text(
                  totalQuestionsNumber.toString(),
                  style:  TextStyle(
                    fontSize: 25,
                    fontWeight: FontWeight.w700,
                    color: AppStyle.primaryColor,
                  ),
                ),
              ],
            ),
            const Spacer(),
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Mark due'.tr,
                  style: TextStyle(
                    color: AppStyle.primaryColor,
                  ),
                ),
                Row(
                  children: [
                    AnimatedNumberWidget(number: fullMark),
                    const SizedBox(width: 2),
                     Text('/', style: TextStyle(color: AppStyle.primaryColor)),
                    const SizedBox(width: 2),
                    AnimatedNumberWidget(number: deservedMark),
                  ],
                ),
                const SizedBox(height: 10),
                FutureBuilder<bool>(
                  initialData: true,
                  future: Future.delayed(
                      const Duration(milliseconds: 1300), () => false),
                  builder: (context, snapshot) {
                    final data = snapshot.data ?? true;
                    Widget child;

                    if (data) {
                      child = const SizedBox();
                    } else {
                      child = Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 5, horizontal: 10),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(50),
                          border: Border.all(color: AppStyle.primaryColor),
                        ),
                        child: RichText(
                          text: TextSpan(
                            text: 'Correct'.tr+' $correctAnswersNumber',
                            style:
                                const TextStyle(color: Colors.green),
                            children: [
                              const TextSpan(text: '   '),
                              TextSpan(
                                text: 'Wrong'.tr+' $wrongAnswersNumber',
                                style: const TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                      );
                    }

                    return AnimatedSwitcher(
                      duration: const Duration(milliseconds: 700),
                      transitionBuilder: (child, animation) {
                        return SizeTransition(
                          sizeFactor: animation,
                          child: child,
                        );
                      },
                      child: child,
                    );
                  },
                ),
              ],
            ),
        ],
      ),
    );
  }
}
