import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_player/video_player.dart';
import 'dart:developer';

class CustomVideoPlayer extends StatelessWidget {
  final List<String> videos;

  const CustomVideoPlayer({super.key, required this.videos});

  @override
  Widget build(BuildContext context) {
    MyCustomVideoController controller =
        Get.put(MyCustomVideoController(videos: videos));

    return Obx(() => Visibility(
        visible: !controller.isLoading,
        replacement: const Center(
          child: CircularProgressIndicator(),
        ),
        child: VideoPlayer(controller.videoPlayerController)));
  }
}

class MyCustomVideoController extends GetxController {
  final List<String> videos;
  MyCustomVideoController({required this.videos});
  late final VideoPlayerController videoPlayerController =
      VideoPlayerController.networkUrl(Uri.parse(videos.last));

  RxBool _isLoading = true.obs;
  bool get isLoading => _isLoading.value;
  @override
  onInit() {
    try {
      videoPlayerController.initialize().then((value) {
        playVideo();
        _isLoading(false);
      }).onError((error, stackTrace) {
        log(
          "Video error $error $stackTrace",
        );
      });
    } catch (e, stackTrace) {
      log("Error  $e , $stackTrace ");
    }

    super.onInit();
  }

  playVideo() async {
    videoPlayerController.play();
  }

  pauseVideo() async {
    videoPlayerController.pause();
  }

  @override
  void onClose() {
    // TODO: implement onClose
    videoPlayerController.dispose();
    super.onClose();
  }
}
