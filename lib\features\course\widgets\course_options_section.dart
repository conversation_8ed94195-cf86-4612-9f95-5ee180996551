import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import '../contollers/course_controller.dart';

class CourseOptionsSection extends StatelessWidget {
  const CourseOptionsSection();

  @override
  Widget build(BuildContext context) {
    CourseController controller = Get.find();
    return Column(
      children: [
        Divider(
          color: AppStyle.lightGreyColor,
          thickness: 2,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: SizedBox(
            width: Get.width,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                for(int index=0;index< controller.options.length;index++)
                  Obx(
                      ()=> InkWell(
                        onTap: ()=> controller.optionOnTap(index),
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 12,horizontal: 8),
                          height: 50,
                          decoration: BoxDecoration(
                            border: controller.selectedOptions==index?
                            Border(
                                bottom: BorderSide(color: AppStyle.primaryColor,width: 5)
                            ):null,
                          ),
                          child: Text(
                            controller.options[index],
                            style: Theme.of(context).textTheme.titleMedium!.copyWith(
                                color: AppStyle.lightBlackColor,
                                fontWeight: FontWeight.w700
                            ),
                          ),
                        ),
                      )
                  )
              ],
            ),
          ),
        ),
        Divider(
          height: 0,
          thickness: 2,
          color: AppStyle.lightGreyColor,
        ),
      ],
    );
  }
}
