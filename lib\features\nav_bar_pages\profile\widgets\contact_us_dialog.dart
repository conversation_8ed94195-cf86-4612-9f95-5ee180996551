import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/url_launcher.dart';

class ContactUsDialog extends StatelessWidget {
  const ContactUsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final AppController appController = Get.find();
    return IntrinsicHeight(
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "contact information".tr,
                  style: Get.textTheme.titleLarge?.copyWith(
                      color: AppStyle.blackColor,
                      fontSize: 22,
                      fontWeight: AppFontWeight.bold),
                ),
                const SizedBox(height: 16),
                Text(
                  "you can contact us or follow us on social media".tr,
                  textAlign: TextAlign.center,
                  style: Get.textTheme.bodyMedium?.copyWith(
                    color: AppStyle.blackTextColor,
                    fontSize: 18,
                  ),
                ),
                const SizedBox(height: 32),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    ContactUsButton(
                        icon: Assets.icons.whatsapp.path,
                        url:
                            "https://wa.me/${appController.generalSettings["whats"]}"),
                    ContactUsButton(
                        icon: Assets.icons.facebook.path,
                        url: "${appController.generalSettings["facebook"]}"),
                    ContactUsButton(
                        icon: Assets.icons.youtube.path,
                        url: "${appController.generalSettings["youtube"]}"),
                    ContactUsButton(
                        icon: Assets.icons.insta.path,
                        url: "${appController.generalSettings["instagram"]}"),
                    ContactUsButton(
                        icon: Assets.icons.telegramIcon.path,
                        url: "${appController.generalSettings["telegram"]}"),
                  ],
                )
              ],
            ),
          ),
          Align(
            alignment: AlignmentDirectional.topEnd,
            child: IconButton(
              onPressed: Get.back,
              icon: Assets.icons.cancelDialog.svg(
                  colorFilter:
                      ColorFilter.mode(AppStyle.blackColor, BlendMode.srcIn)),
            ),
          )
        ],
      ),
    );
  }
}

class ContactUsButton extends StatelessWidget {
  final String icon;
  final String url;
  const ContactUsButton({super.key, required this.icon, required this.url});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsetsDirectional.only(end: 8),
      child: GestureDetector(
        onTap: () async => await UrlLauncher.openLauncher(url),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
              shape: BoxShape.circle, color: AppStyle.primaryColor),
          child: SvgPicture.asset(
            icon,
            height: 24,
            colorFilter: ColorFilter.mode(AppStyle.whiteColor, BlendMode.srcIn),
          ),
        ),
      ),
    );
  }
}
