import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/validator.dart';
import 'package:all_in_one/core/widgets/app_date_picker.dart';
import 'package:all_in_one/core/widgets/app_drop_down.dart';
import 'package:all_in_one/core/widgets/app_drop_down_search.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/date_picker.dart';
import 'package:all_in_one/core/widgets/image.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';
import 'package:all_in_one/features/auth/login/controller.dart';
import 'package:all_in_one/features/auth/signup/controller.dart';
import 'package:all_in_one/features/auth/widgets/auth_page_switcher_widget.dart';
import 'package:all_in_one/features/auth/widgets/privacy_aggrement.dart';

class FirstStep extends StatelessWidget {
  const FirstStep({super.key});

  @override
  Widget build(BuildContext context) {
    SignUpPageController controller = Get.find();
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              InkWell(
                onTap: () => controller.imgFromGallery(),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Obx(
                      () => CircleAvatar(
                        radius: 30,
                        backgroundColor: AppStyle.primaryColor,
                        child: controller.imagePath.isEmpty
                            ? CircleAvatar(
                                radius: 30,
                                // backgroundImage: AssetImage(
                                //     Assets.images.authImage.path),
                                child: Icon(Icons.person),
                                // child: Assets.images.authImage.image(),
                              )
                            : AppImage(
                                path: controller.imagePath,
                                type: ImageType.File,
                                width: 120,
                                height: 120,
                                boxShape: BoxShape.circle,
                              ),
                      ),
                    ),
                    // PositionedDirectional(
                    //   bottom: 10,
                    //   start: 90,
                    //   end: 0,
                    //   child: CircleAvatar(
                    //     radius: 16,
                    //     backgroundColor: AppStyle.whiteColor,
                    //     child: Icon(
                    //       Icons.camera_alt_outlined,
                    //       color: AppStyle.primaryColor,
                    //       size: 16,
                    //     ),
                    //   ),
                    // )
                  ],
                ),
              ),
              SizedBox(
                width: 10,
              ),
              InkWell(
                  onTap: () => controller.imgFromGallery(),
                  child: Text('Add an image profile'.tr,
                      style: TextStyle(
                        decoration: TextDecoration.underline,
                      ))),
            ],
          ),
          SizedBox(
            height: 10,
          ),
          TextFormField(
            controller: controller.name,
            style: Theme.of(context)
                .textTheme
                .titleMedium
                ?.copyWith(color: AppStyle.lightBlackColor),
            validator: Validator.notNullValidation,
            decoration: InputDecoration(
              hintText: "Full Name".tr,
            ),
          ),
          const SizedBox(
            height: 12,
          ),
          Obx(
            () => controller.schoolsLoading
                ? SkeletonWidget(
                    height: 60,
                  )
                : AppDropDownSearchWidget(
                    hint: "select a university".tr,
                    items: controller.schools.map((e) => e.title).toList(),
                    chosenValue: controller.schoolController.text,
                    selectedIndex: (index) {
                      controller.schoolId = controller.schools[index].id;
                    },
                    isChanged: true,
                    validator: Validator.notNullValidation,
                  ),
          ),
          const SizedBox(
            height: 12,
          ),
          Obx(
            () => controller.majorsLoading
                ? SkeletonWidget(
                    height: 60,
                  )
                : AppDropDownSearchWidget(
                    hint: "select a major".tr,
                    items: controller.majors.map((e) => e.title).toList(),
                    chosenValue: controller.majorController.text,
                    selectedIndex: (index) {
                      controller.majorId = controller.majors[index].id;
                      controller.loadYearsData();
                    },
                    isChanged: true,
                    validator: Validator.notNullValidation,
                  ),
          ),
          const SizedBox(
            height: 12,
          ),
          Obx(
            () => controller.yearsLoading
                ? SkeletonWidget(
                    height: 60,
                  )
                : AppDropDownWidget(
                    listValue: controller.years.map((e) => e.title).toList(),
                    hintText: 'Classroom'.tr,
                    selectedIndex: (index) {
                      controller.yearsId = controller.years[index].id;
                    },
                    validator: Validator.notNullValidation,
                  ),
          ),
          const SizedBox(height: 12),
          Center(
              child: (Obx(() => PrivacyAgreement(
                    isActive: controller.isPrivacyAccepted.value,
                    onChange: (val) => controller.isPrivacyAccepted(val),
                  )))),
          const SizedBox(
            height: 25,
          ),
          AppButton(
            text: 'Sign Up'.tr,
            height: 60,
            radius: 10,
            withLoading: true,
            margin: EdgeInsets.zero,
            style: Get.textTheme.titleMedium!.copyWith(
                color: AppStyle.whiteColor, fontWeight: FontWeight.bold),
            onTap: () => controller.signUp(context),
          ),
          const SizedBox(
            height: 30,
          ),
        ],
      ),
    );
  }
}
