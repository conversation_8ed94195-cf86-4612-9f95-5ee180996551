// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subject_section_adapter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SubjectSectionAdapterAdapter extends TypeAdapter<SubjectSectionAdapter> {
  @override
  final int typeId = 1;

  @override
  SubjectSectionAdapter read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SubjectSectionAdapter(
      id: fields[0] as int,
      title: fields[1] as String,
      lessons: (fields[2] as List).cast<LessonAdapter>(),
    );
  }

  @override
  void write(BinaryWriter writer, SubjectSectionAdapter obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.lessons);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SubjectSectionAdapterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
