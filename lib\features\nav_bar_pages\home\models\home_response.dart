import 'package:all_in_one/features/course/models/subject_model.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_banner_model.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/models/task_model.dart';

import 'home_new_model.dart';
import 'home_year_model.dart';


class HomeResponse {
  List<HomeNewModel> news;
  List<Major> majors;
  List<TaskModel> tasks;
  List<HomeBannerModel> banners;
  HomeYearModel year;

  HomeResponse({required this.news,required this.tasks,required this.majors,required this.banners,required this.year});

  factory HomeResponse.fromJson(Map<String, dynamic> json) => HomeResponse(
    news: List<HomeNewModel>.from(json["news"].map((x) => HomeNewModel.fromJson(x))),
    majors: List<Major>.from(json["majors"].map((x) => Major.fromJson(x))),
    tasks: List<TaskModel>.from(json["tasks"].map((x) => TaskModel.fromJson(x))),
    banners: List<HomeBannerModel>.from(json["banners"].map((x) => HomeBannerModel.fromJson(x))),
    year: HomeYearModel.fromJson(json["year"])
  );


}







