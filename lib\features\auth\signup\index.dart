import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/widgets/app_date_picker.dart';
import 'package:all_in_one/core/widgets/app_drop_down.dart';
import 'package:all_in_one/core/widgets/app_drop_down_search.dart';
import 'package:all_in_one/core/widgets/date_picker.dart';
import 'package:all_in_one/core/widgets/image.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';
import 'package:all_in_one/features/auth/signup/first_step.dart';
import 'package:all_in_one/features/auth/signup/second_step.dart';
import 'package:all_in_one/features/auth/widgets/app_auth_page.dart';
import 'package:all_in_one/features/auth/widgets/auth_page_switcher_widget.dart';
import 'package:all_in_one/features/auth/widgets/privacy_aggrement.dart';

import '../../../core/style/style.dart';
import '../../../core/utils/validator.dart';
import '../../../core/widgets/button.dart';
import 'controller.dart';

class SignUpPage extends StatelessWidget {
  const SignUpPage({super.key});

  @override
  Widget build(BuildContext context) {
    SignUpPageController controller = Get.put(SignUpPageController());
    return Scaffold(
      extendBody: true,
      extendBodyBehindAppBar: true,
      key: controller.scaffoldKey,
      body: AppAuthPage(
          title: 'Sign Up'.tr,
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              height: Get.height * .75,
              width: Get.width,
              decoration: BoxDecoration(
                  color: AppStyle.whiteColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                  )),
              padding:
                  EdgeInsets.only(left: 20, right: 20, top: 20, bottom: 10),
              child: Form(
                key: controller.formKey,
                child: PageView(
                  controller: controller.pageController,
                  children: [
                    SecondStep(),
                    FirstStep()
                  ],
                )
              ),
            ),
          )),
    );
  }
}
