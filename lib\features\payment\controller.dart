import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/core/widgets/loading.dart';
import 'package:all_in_one/core/widgets/loading_indecator_widget.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/course/widgets/dialogs/check_code_dialog_body_widget.dart';
import 'package:all_in_one/features/course/widgets/dialogs/subscription_status_dialog_body_widget.dart';

class PaymentMethodsController extends GetxController {
  final String type;
  final int? id;
  final int? index;
  final bool fromCourse;
  PaymentMethodsController(
      {required this.type, this.id, this.index, this.fromCourse = false});

  Map<String, dynamic> get params =>
      {"type": type, "id": id, "fromCourse": fromCourse, "index": index};

  AppController appController = Get.find();
  DataController dataController = Get.find();
  onMtnCallBack() async {
    Nav.replacement(Pages.mtnCheckPhone, arguments: params);
  }

  onFatoraCallBack() async {
    Loading.overlayLoading(Get.overlayContext!);
    var response = await dataController.postData(
        url: API.subscribeFatoryApi, body: params);
    Get.back();
    if (response.success) {
      Nav.replacement(Pages.onlinePayment, arguments: {
        "type": type,
        "from_course": fromCourse,
        "index": index,
        "url": response.data['Data']['url'],
      });
    } else if (response.code == ErrorCode.VALIDATION_ERROR ||
        response.errors != null ||
        response.errors!.isNotEmpty) {
      Get.back();
      DialogHelper.showDialog(
        dialogBody: SubscriptionStatusDialogBodyWidget(
          status: SubscriptionStatus.Failed,
        ),
      );
    } else {
      appController.showToast(Get.context!,
          message: response.message!, status: ToastStatus.fail);
    }
  }

  onCodeCallBack() async {
    Get.back();
    await DialogHelper.showDialog(dialogBody: CheckCodeDialogBodyWidget());
  }
}
