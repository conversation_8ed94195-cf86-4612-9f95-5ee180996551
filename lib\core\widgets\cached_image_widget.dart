import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:lottie/lottie.dart';

class CachedImageWidget extends StatelessWidget {
  double? height;
  double? loadingHeight;
  double? width;
  String imageUrl;
  BorderRadiusGeometry borderRadius;
  bool isCircular;
  BoxFit? fit;

  CachedImageWidget({
    this.width,
    this.height,
    this.loadingHeight,
    this.fit,
    required this.imageUrl,
    this.borderRadius = const BorderRadius.all(Radius.circular(0)),
    this.isCircular =false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      decoration: BoxDecoration(
          // color: Colors.white,
          borderRadius: isCircular?null:borderRadius,
          shape: isCircular? BoxShape.circle : BoxShape.rectangle,
      ),
      clipBehavior: Clip.antiAlias,
      child: imageUrl != ""
          ? imageUrl.endsWith('svg')?
            SvgPicture.network(
              imageUrl,
              placeholderBuilder: (context) => Image.asset(
                'assets/gif/loading.gif',
                height: height,
                width: width,
                fit: BoxFit.fill,
              ),
            )
          : CachedNetworkImage(
              imageUrl: imageUrl,
              placeholder: (context, url) => Image.asset(
                'assets/gif/loading.gif',
                height: loadingHeight,
                width: width,
                fit: fit,
              ),
              height: height,
              width: width,
              fit: fit,
              errorWidget: (context, url, error){
                return  Image.asset(
                  'assets/gif/loading.gif',
                  height: loadingHeight,
                  width: width,
                  fit: fit,
                );
              },
            )
          : Lottie.asset(
              'assets/lottie/imageLoading.json',
              height: height,
              width: width,
              fit: fit,
            ),
    );
  }
}
