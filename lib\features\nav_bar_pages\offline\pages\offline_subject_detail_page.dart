import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/hive/adapters/subject_adapter.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/app_animation/item_animation.dart';
import 'package:all_in_one/core/widgets/cached_image_widget.dart';
import 'package:all_in_one/features/nav_bar_pages/offline/offline_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/offline/widgets/lesson_offline_list_item_widget.dart';

class OfflineSubjectDetailPage extends StatefulWidget {
  final SubjectAdapter subject;
  const OfflineSubjectDetailPage({required this.subject});

  @override
  _OfflineSubjectDetailPageState createState() =>
      _OfflineSubjectDetailPageState();
}

class _OfflineSubjectDetailPageState extends State<OfflineSubjectDetailPage> {
  OfflineController controller = Get.find();

  @override
  void initState() {
    controller.initialSubjectDetailData(widget.subject);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: NestedScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return <Widget>[
              SliverOverlapAbsorber(
                handle:
                    NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                sliver: SliverAppBar(
                  backgroundColor: AppStyle.primaryColor,
                  leading: InkWell(
                    onTap: () => Get.back(),
                    child: Padding(
                      padding: EdgeInsets.all(20),
                      child: Icon(
                        Icons.arrow_back,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  centerTitle: true,
                  expandedHeight: 375,
                  pinned: true,
                  snap: true,
                  floating: true,
                  title: Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      widget.subject.title,
                      style: Theme.of(context).textTheme.titleLarge!.copyWith(
                          color: AppStyle.whiteColor,
                          fontWeight: AppFontWeight.bold),
                    ),
                  ),
                  flexibleSpace: FlexibleSpaceBar(
                    background: Container(
                      color: Colors.white,
                      margin: EdgeInsets.only(top: 65),
                      child: Column(
                        children: [
                          CachedImageWidget(
                            imageUrl: widget.subject.banner ?? '',
                            width: Get.width,
                            height: 220,
                            borderRadius: BorderRadius.circular(0),
                            fit: BoxFit.cover,
                          ),
                          const SizedBox(
                            height: 15,
                          ),
                          SizedBox(
                            width: Get.width,
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 15),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.subject.title,
                                    style: Theme.of(context)
                                        .textTheme
                                        .headlineSmall!
                                        .copyWith(
                                          color: AppStyle.blackTextColor,
                                        ),
                                  ),
                                  const SizedBox(
                                    height: 4,
                                  ),
                                  Text(
                                    widget.subject.teacherName,
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium!
                                        .copyWith(
                                            color: AppStyle.lightBlackColor,
                                            fontWeight: FontWeight.w300),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 8,
                          ),
                        ],
                      ),
                    ),
                  ),
                  automaticallyImplyLeading: false,
                ),
              ),
            ];
          },
          body: Padding(
            padding: const EdgeInsets.only(top: 70),
            child: AnimationLimiter(
              child: Obx(
                () => ListView.builder(
                  itemCount: controller.sections.length,
                  padding: EdgeInsets.zero,
                  itemBuilder: (context, index) {
                    return ItemAnimation(
                      index: index,
                      elementCount: 5,
                      child: Container(
                        margin:
                            EdgeInsets.only(left: 15, right: 15, bottom: 10),
                        decoration: BoxDecoration(
                          color: AppStyle.whiteBackgroundColor,
                          borderRadius: BorderRadius.all(Radius.circular(10)),
                          boxShadow: [
                            BoxShadow(
                                color: Colors.grey.withOpacity(0.2),
                                blurRadius: 1.0,
                                spreadRadius: 1,
                                offset: const Offset(
                                    -2, 1) // changes position of shadow
                                ),
                          ],
                        ),
                        child: ListTileTheme(
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 20, vertical: 4),
                          dense: true,
                          child: ExpansionTile(
                            title: Text(
                              controller.sections[index].title,
                              softWrap: true,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleMedium!
                                  .copyWith(color: AppStyle.primaryColor),
                            ),
                            expandedCrossAxisAlignment:
                                CrossAxisAlignment.start,
                            childrenPadding:
                                EdgeInsets.symmetric(horizontal: 20),
                            children: [
                              Container(
                                height: 5,
                                color: AppStyle.whiteColor,
                              ),
                              Obx(
                                () => ListView.separated(
                                  itemCount: controller.lessons[index].length,
                                  padding: EdgeInsets.zero,
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  itemBuilder: (context, indexx) {
                                    return LessonOfflineListItemWidget(
                                      subjectId: widget.subject.id,
                                      sectionId: controller.sections[index].id,
                                      lessonId:
                                          controller.lessons[index][indexx].id,
                                      lessonName: controller
                                          .lessons[index][indexx].title,
                                      lessonSubTitle: controller
                                          .lessons[index][indexx].subTitle,
                                      lessonIndex: indexx,
                                      sectionIndex: index,
                                    );
                                  },
                                  separatorBuilder: (context, indexx) {
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 28),
                                      child: Divider(
                                        color: AppStyle.lightGreyColor
                                            .withOpacity(0.5),
                                        thickness: 2,
                                        height: 0,
                                      ),
                                    );
                                  },
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
