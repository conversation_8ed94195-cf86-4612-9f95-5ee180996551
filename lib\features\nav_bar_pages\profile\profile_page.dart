import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
// import 'package:share_plus/share_plus.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/cached_image_widget.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/features/nav_bar_pages/nav_bar/controller.dart';
import 'package:all_in_one/features/nav_bar_pages/offline/pages/offline_subject_page.dart';
import 'package:all_in_one/features/nav_bar_pages/profile/widgets/contact_us_dialog.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';
import 'package:share_plus/share_plus.dart';

import 'widgets/profile_dialog_body_widget.dart';
import 'widgets/profile_item_widget.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage();

  @override
  Widget build(BuildContext context) {
    NavBarController controller = Get.put(NavBarController());
    AppController appController = Get.find();
    return Scaffold(
      body: Column(
        children: [
          AppBarWidget(
            centerWidget: Text(
              'Profile'.tr,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
            ),
            trailingIcon: SizedBox(
              width: 35,
              height: 35,
            ),
            leadingIcon: SizedBox(
              width: 35,
              height: 35,
            ),
            trailingOnTap: () {},
            leadingOnTap: () {},
          ),
          const SizedBox(
            height: 35,
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Obx(
                    () => CachedImageWidget(
                      imageUrl: controller.appController.user!.avatar.value,
                      width: 150,
                      height: 150,
                      isCircular: true,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const SizedBox(
                    height: 15,
                  ),
                  Obx(
                    () => Text(
                      controller.appController.user!.name.value,
                      style: Theme.of(context)
                          .textTheme
                          .headlineSmall!
                          .copyWith(
                              color: AppStyle.lightBlackColor,
                              fontWeight: FontWeight.bold),
                    ),
                  ),
                  const SizedBox(
                    height: 6,
                  ),
                  Obx(
                    () => Text(
                      controller.appController.user!.year.value,
                      style: Theme.of(context).textTheme.titleSmall!.copyWith(
                            color: AppStyle.lightBlackColor,
                          ),
                    ),
                  ),
                  const SizedBox(
                    height: 40,
                  ),
                  ProfileItemWidget(
                    text: 'Edit Profile'.tr,
                    icon: Assets.icons.userOutlineIcon.svg(
                        width: 20,
                        height: 20,
                        fit: BoxFit.fill,
                        color: AppStyle.primaryColor),
                    onTap: () => Nav.to(Pages.editProfile),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  if (!appController.isInReview)
                    ProfileItemWidget(
                      text: 'My Subscriptions'.tr,
                      icon: Assets.icons.mySubjectIcon.svg(
                          width: 20, height: 20, color: AppStyle.primaryColor),
                      onTap: () => Nav.to(Pages.mySubjects),
                    ),
                  const SizedBox(
                    height: 12,
                  ),
                  ProfileItemWidget(
                      text: 'My Videos'.tr,
                      icon: Assets.icons.videoIcon.svg(
                          width: 20, height: 20, color: AppStyle.primaryColor),
                      onTap: () => Navigator.push(
                            context,
                            CupertinoPageRoute(
                              builder: (context) => OfflineSubjectPage(),
                            ),
                          )
                      //Nav.to(Pages.mySubjects),
                      ),
                  const SizedBox(
                    height: 12,
                  ),
                  ProfileItemWidget(
                    text: 'About Us'.tr,
                    icon: Assets.icons.infoIcon.svg(
                        width: 20, height: 20, color: AppStyle.primaryColor),
                    onTap: () => Nav.to(Pages.aboutUS),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  if (!appController.isInReview)
                    ProfileItemWidget(
                      text: 'contact us'.tr,
                      icon: Assets.icons.contact.svg(
                          width: 20, height: 20, color: AppStyle.primaryColor),
                      onTap: () async => await DialogHelper.showDialog(
                          dialogBody: ContactUsDialog()),
                    ),
                  const SizedBox(
                    height: 12,
                  ),
                  ProfileItemWidget(
                    text: 'Usage Policy'.tr,
                    icon: Assets.icons.privacyIcon.svg(
                        width: 20, height: 20, color: AppStyle.primaryColor),
                    onTap: () => Nav.to(Pages.provisions),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  Builder(builder: (context) {
                    return ProfileItemWidget(
                        text: "share the app".tr,
                        icon: Assets.icons.share.svg(
                            width: 20,
                            height: 20,
                            color: AppStyle.primaryColor),
                        onTap: () async {
                          // _onShare method:
                          final box = context.findRenderObject() as RenderBox?;
                          log(box!.localToGlobal(Offset.zero).toString());
                          log(box.size.toString());

                          await Share.share(
                            '''You can download the All In one app from the following link\n
                      Google Play : https://play.google.com/store/apps/details?id=com.ixcoders.allinone
                      App Store : https://apps.apple.com/us/app/all-in-one-app/id6737469422
                      ''',
                            sharePositionOrigin:
                                box!.localToGlobal(Offset.zero) & box.size,
                          );
                        });
                  }),
                  const SizedBox(height: 12),
                  ProfileItemWidget(
                    text: 'Update Password'.tr,
                    icon: Assets.icons.lockOutline.svg(
                        width: 20,
                        height: 20,
                        fit: BoxFit.fill,
                        color: AppStyle.primaryColor),
                    onTap: () => Nav.to(Pages.updatePassword),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  ProfileItemWidget(
                    text: 'Logout'.tr,
                    icon: Assets.icons.logOutIcon.image(
                        width: 20, height: 20, color: AppStyle.primaryColor),
                    onTap: () {
                      DialogHelper.showDialog(
                          dialogBody: ProfileDialogBodyWidget(
                        title: "Confirm log out".tr,
                        subTitle:
                            "When you log out of your account, all information and videos stored within the application will be deleted"
                                .tr,
                        confirmOnTap: () {
                          Get.back();
                          controller.logOut(context);
                        },
                      ));
                    },
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  ProfileItemWidget(
                    text: 'Delete account'.tr,
                    icon: Assets.icons.deleteAccountIcon.image(
                        width: 25, height: 25, color: AppStyle.primaryColor),
                    onTap: () {
                      DialogHelper.showDialog(
                          dialogBody: ProfileDialogBodyWidget(
                        title: "Confirm Delete account".tr,
                        subTitle:
                            "When you deleting your account, all information and videos stored within the application will be deleted"
                                .tr,
                        confirmOnTap: () {
                          Get.back();
                          controller.deleteAccount(context);
                        },
                      ));
                    },
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
