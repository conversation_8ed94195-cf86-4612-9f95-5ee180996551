
import 'package:all_in_one/core/style/style.dart';
import 'package:flutter/material.dart';

class PriceDiscountWidget extends StatelessWidget {
  final String price;
  final String? discount;
  final bool isBold;
  final double fontSized;
  final bool hasDiscount;
  const PriceDiscountWidget({required this.price,required this.fontSized, this.discount,this.isBold=false,required this.hasDiscount});

  @override
  Widget build(BuildContext context) {
    return Wrap(
      // mainAxisAlignment: MainAxisAlignment.center,
      //
      // mainAxisSize: MainAxisSize.min,
      direction: Axis.horizontal,
      children: [
        Text(
          price,
          style: TextStyle(
              color: AppStyle.lightGreyTextColor,
              fontSize: fontSized,
              fontWeight: isBold?AppFontWeight.bold:AppFontWeight.regular,
              decoration: hasDiscount==true? TextDecoration.lineThrough:null,
              decorationColor: hasDiscount==true?AppStyle.lightGreyTextColor:null,
          ),
        ),
        Visibility(
          visible: discount!=null&&hasDiscount==true,
          child: Text(
            ' - ',
            style: TextStyle(
                color: AppStyle.primaryColor,
                fontSize: fontSized,
                fontWeight: isBold?AppFontWeight.bold:AppFontWeight.regular
            ),
          ),
        ),
        Visibility(
          visible: hasDiscount==true,
          child: Text(
            discount??'',
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                color: AppStyle.primaryColor,
                fontSize: fontSized,
                fontWeight: isBold?AppFontWeight.bold:AppFontWeight.regular
            ),
          ),
        ),
      ],
    );
  }
}
