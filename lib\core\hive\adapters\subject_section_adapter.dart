
import 'package:hive/hive.dart';
import '../../hive/adapters/lesson_adapter.dart';
part 'subject_section_adapter.g.dart';

@HiveType(typeId: 1)
class SubjectSectionAdapter extends HiveObject{
  @HiveField(0)
  int id;
  @HiveField(1)
  String title;
  @HiveField(2)
  List<LessonAdapter> lessons;



  SubjectSectionAdapter({
    required this.id,
    required this.title,
    required this.lessons,
  });


}