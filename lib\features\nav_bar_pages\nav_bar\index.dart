
import 'package:all_in_one/features/nav_bar_pages/nav_bar/widget/side_menu.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/features/nav_bar_pages/nav_bar/widget/animated_bottom_nav.dart';
import 'controller.dart';

class NavBarPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    NavBarController controller = Get.put(NavBarController());
    return WillPopScope(
      // onPopInvoked: (value) => controller.closeApp(context),
      onWillPop: ()async{
        controller.closeApp(context);
        return false;
      },
      child: Obx(
        () => Scaffold(
          key: controller.scaffold<PERSON>ey,
          resizeToAvoidBottomInset: true,
          body: controller.pages[controller.selectedIndex]!,
          bottomNavigationBar: AnimatedBottomNav(
            currentIndex: controller.selectedIndex,
            onChange: (index) => controller.bottomNavigationOnChange(context,index),
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        ),
      ),
    );
  }
}
