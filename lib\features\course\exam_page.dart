import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/no_data_widget.dart';
import 'package:all_in_one/features/course/contollers/exam_controller.dart';
import 'package:all_in_one/features/course/widgets/exam_widgets/exam_result_widget.dart';
import 'widgets/exam_widgets/question_list_item_widget.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';



class ExamPage extends StatelessWidget {
  final String examName;
  const ExamPage({required this.examName});

  @override
  Widget build(BuildContext context) {
    ExamController controller = Get.put(ExamController());

    Widget ExamAppBar(){
      return AppBarWidget(
        centerWidget: Text(
          examName,
          style: Theme.of(context).textTheme.titleLarge!.copyWith(
              color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
        ),
        trailingIcon: SizedBox(
          width: 35,
          height: 35,
        ),
        leadingIcon: Icon(
          Icons.arrow_back,
          color: Colors.white,
        ),
        trailingOnTap: () {},
        leadingOnTap: () {
          Get.back();
        },
      );
    }

    return Scaffold(
      body: Obx(
          ()=> controller.dataLoading?Column(
            children: [
              ExamAppBar(),
              Expanded(
                child: Center(
                  child: CircularProgressIndicator()
                ),
              ),
            ],
          ): controller.hasError?
          Column(
            children: [
              ExamAppBar(),
              Expanded(child: Center(child: NoDataWidget(imageWidth: 200,imageHeight: 200,))),
            ],
          ):
          SingleChildScrollView(
            controller: controller.scrollController,
            child: Column(
              children: [
                ExamAppBar(),
                const SizedBox(
                  height: 10,
                ),
                ValueListenableBuilder(
                  valueListenable: controller.showExamResult,
                  builder: (context,bool showExamResult,_){
                    return Visibility(
                      visible: showExamResult,
                      child: ExamResultWidget(
                        examTitle: examName,
                        totalQuestionsNumber: controller.exam.questions.length,
                        showExamResult: true,
                        fullMark: controller.fullMark,
                        deservedMark: controller.deservedMark,
                        correctAnswersNumber: controller.correctAnswersNumber,
                        wrongAnswersNumber: controller.wrongAnswersNumber,
                      ),
                    );
                  },
                ),
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  itemCount: controller.exam.questions.length,
                  itemBuilder: (context, index) {
                    return QuestionListItemWidget(
                      questionIndex: index,
                      question: controller.exam.questions[index],
                      totalMark: controller.exam.totalMark,
                      successPlayer: controller.successPlayer,
                      wrongPlayer: controller.wrongPlayer,
                      onChoiceSelected: (innerIndex) {
                        controller.setSelectedAnswer(
                          context: context,
                          questionId: controller.exam.questions[index].id,
                          choice: controller.exam.questions[index].choices[innerIndex],
                        );
                      },
                    );
                  },
                  separatorBuilder: (context, index) {
                    // return const Divider(
                    //     color: AppStyle.greyTextColor,
                    //     height: 20
                    // );
                    return const SizedBox(height: 15,);
                  },
                ),
                ValueListenableBuilder(
                  valueListenable: controller.showCorrectionButton,
                  builder: (context,showButton,_){
                    return Visibility(
                      visible: showButton,
                      child: AppButton(
                        text: 'Show Exam Result'.tr,
                        height: 55,
                        radius: 10,
                        withLoading: false,
                        margin: EdgeInsets.symmetric(horizontal: 15,vertical: 15),
                        style: Get.textTheme.titleMedium!.copyWith(color: AppStyle.whiteColor,fontWeight: FontWeight.bold),
                        onTap: ()async{
                          controller.calcExamResult();
                        },
                      ),
                    );
                  },
                )
              ],
            ),
          )
      ),
    );
  }
}
