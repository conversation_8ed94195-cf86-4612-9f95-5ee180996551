import 'dart:io';
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:secure_application/secure_application.dart';
import 'package:all_in_one/core/widgets/zoom_widget.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'controller/downloaded_video_player_controller.dart';

class DownloadedVideoPlayerPage extends StatefulWidget {
  final String videoName;
  const DownloadedVideoPlayerPage({required this.videoName});
  @override
  _DownloadedVideoPlayerPageState createState() =>
      _DownloadedVideoPlayerPageState();
}

class _DownloadedVideoPlayerPageState extends State<DownloadedVideoPlayerPage>
    with WidgetsBindingObserver {
  late final SecureApplicationController? lockController;
  late DownloadedVideoPlayerController controller;

  @override
  void initState() {
    WakelockPlus.enable();
    controller = Get.put(
      DownloadedVideoPlayerController(),
    );
    controller.enterFullScreen();
    WidgetsBinding.instance.addObserver(this);
    controller.initialVideo(context, widget.videoName);
    lockController = SecureApplicationProvider.of(context, listen: false);
    lockController?.secure();
    // protectScreenOn();
    super.initState();
  }

  @override
  void dispose() {
    controller.exitFullScreen();
    WidgetsBinding.instance.removeObserver(this);
    WakelockPlus.disable();
    lockController?.open();
    controller.dispose();
    super.dispose();
  }

  // protectScreenOn() async{
  //   await ScreenProtector.protectDataLeakageWithColor(Colors.white);
  //   await ScreenProtector.preventScreenshotOn();
  // }
  //
  // protectScreenOff() async{
  //   await ScreenProtector.preventScreenshotOff();
  // }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
        if (File('${controller.appController.videosDirectoryPath}/video.mp4')
            .existsSync()) {
          File('${controller.appController.videosDirectoryPath}/video.mp4')
              .delete(recursive: true);
        }
        // Get.back();
        break;
      default:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Stack(
          children: [
            ValueListenableBuilder(
              valueListenable: controller.videoInitial,
              builder: (context, bool initial, child) {
                if (!initial) {
                  return Center(
                    child: CircularProgressIndicator(
                      color: Colors.white,
                    ),
                  );
                }
                return Center(
                  child: Directionality(
                    textDirection: TextDirection.ltr,
                    child: GestureZoomBox(
                      maxScale: 4,
                      duration: const Duration(milliseconds: 200),
                      child: AspectRatio(
                        aspectRatio:
                            controller.videoController.value.aspectRatio,
                        child: Obx(() => controller.isVideoInitd.value
                            ? Chewie(
                                controller: controller.chewieController,
                              )
                            : const SizedBox()),
                      ),
                    ),
                  ),
                );
              },
            ),
            Align(
              alignment: AlignmentDirectional.topStart,
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: GestureDetector(
                  onTap: Get.back,
                  child: CircleAvatar(
                    backgroundColor: Colors.white,
                    radius: 15,
                    child: Icon(
                      Icons.arrow_back,
                      color: Colors.black,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
