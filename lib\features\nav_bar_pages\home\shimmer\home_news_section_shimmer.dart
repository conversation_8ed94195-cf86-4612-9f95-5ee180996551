import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';

class HomeNewsSectionShimmer extends StatelessWidget {
  const HomeNewsSectionShimmer();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: SizedBox(
        height: 110,
        child: ListView.separated(
          itemCount: 8,
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          itemBuilder: (context, index){
            return Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                SkeletonWidget(
                  width: 80,
                  height: 80,
                  isCircular: true,
                ),
                const SizedBox(height: 6,),
                SkeletonWidget(
                  width: 50,
                  height: 12,
                  isCircular: false,
                  margin: EdgeInsetsDirectional.zero,
                ),
              ],
            );
          },
          separatorBuilder: (context, index){
            return const SizedBox(width: 10,);
          },
        ),
      ),
    );
  }
}
