
import 'package:get/get.dart';
import 'package:all_in_one/features/course/models/section_file_model.dart';
import 'package:all_in_one/features/course/models/section_lesson_model.dart';

import 'section_note_model.dart';

class CourseSectionModel {
  int id;
  String title;
  int subjectId;
  String subjectTitle;
  int price;
  Rx<bool> isSubscription;
  bool isFree;
  bool hasQuiz;
  String quizTitle;
  List<SectionLessonModel> lessons;
  List<SectionNoteModel> notes;
  List<SectionFileModel> files;

  CourseSectionModel(
      {required this.id,
        required this.title,
        required this.subjectId,
        required this.isFree,
        required this.subjectTitle,
        required this.isSubscription,
        required this.hasQuiz,
        required this.quizTitle,
        required this.lessons,
        required this.price,
        required this.notes,
        required this.files});

  factory CourseSectionModel.fromJson(Map<String, dynamic> json) => CourseSectionModel(
    id: json['id'],
    title: json['title'],
    subjectId: json['subject_id'],
    subjectTitle: json['subject_title'],
    isFree: json['is_free']==1?true:false,
    price: json['price'],
    hasQuiz: json['has_quiz'],
    quizTitle: json['quiz_title']??'',
    isSubscription: json['is_subscribed']==null?Rx<bool>(false):json['is_subscribed']==1?Rx<bool>(true):Rx<bool>(false),
    lessons: json['lessons'] != null?List<SectionLessonModel>.from(json["lessons"].map((x) => SectionLessonModel.fromJson(x,json['is_free']==1?true:false))):[],
    notes: json['notes'] != null?List<SectionNoteModel>.from(json["notes"].map((x) => SectionNoteModel.fromJson(x))):[],
    files: json['files'] != null?List<SectionFileModel>.from(json["files"].map((x) => SectionFileModel.fromJson(x))):[],
  );

}


