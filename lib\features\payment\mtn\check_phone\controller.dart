import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/routes.dart';

class MtnCheckPhoneController extends GetxController {
  final String type = Get.arguments["type"]!;
  final int? id = Get.arguments["id"];
  final bool fromCourse = Get.arguments['fromCourse'];
  final int? index = Get.arguments["index"];

  final TextEditingController phone = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final Rx<String?> error = Rx<String?>(null);
  final DataController dataController = Get.find();
  checkPhone() async {
    if (formKey.currentState?.validate() != true) {
      Get.back();
      return;
    }
    String phoneNumber = phone.text.substring(1);
    ResponseModel response = await dataController.postData(
        url: API.mtn, body: {"type": type, "id": id, "phone": phoneNumber});
    if (response.success) {
      int callbackId = response.data["callbackId"];
      Get.back();
      Nav.replacement(Pages.mtnCash, arguments: {
        "id": callbackId,
        "phone": phoneNumber,
        "fromCourse": fromCourse,
        "type": type,
        "index": index
      });
    } else {
      Get.back();
      error(response.errors?[1].toString());
      // Get.dialog(widget)
    }
  }
}
