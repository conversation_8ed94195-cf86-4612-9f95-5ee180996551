import 'dart:developer';
import 'dart:io';
import 'dart:isolate';

import 'package:all_in_one/features/course/models/subject_model.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/hive/adapters/lesson_adapter.dart';
import 'package:all_in_one/core/hive/adapters/subject_adapter.dart';
import 'package:all_in_one/core/hive/adapters/subject_section_adapter.dart';
import 'package:all_in_one/core/hive/hive_helper.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/utils/encription_helper.dart';
import 'package:all_in_one/core/widgets/loading.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/video/downloaded_video_player_page.dart';

class OfflineController extends GetxController {
  DataController dataController = Get.find();
  AppController appController = Get.find();

  List<SubjectAdapter> subjects = [];
  RxList<SubjectSectionAdapter> sections = RxList<SubjectSectionAdapter>([]);
  RxList<RxList<LessonAdapter>> lessons = RxList<RxList<LessonAdapter>>();

  initialSubjectDetailData(SubjectAdapter subject) async {
    sections.clear();
    lessons.clear();
    log('sections ${subjects[0].sections.length}');
    List<LessonAdapter> stepLessons = <LessonAdapter>[];
    log(subject.sections.toString(), name: "subjects");
    log(subject.sections.first.lessons.toString(), name: "lessons");

    for (int i = 0; i < subject.sections.length; i++) {
      stepLessons.clear();
      for (int j = 0; j < subject.sections[i].lessons.length; j++) {
        stepLessons.add(subject.sections[i].lessons[j]);
      }
      lessons.add(RxList(stepLessons));
      sections.add(subject.sections[i]);
    }

    //TODO - Cancel deleting cached lessons when lessons is deleted from server
    //TODO - ENAWE

    // ResponseModel response = await dataController
    //     .postData(url: API.subjectApi, body: {"id": subject.id});
    // if (response.success) {
    //   List<int> lessonsIds = [];
    //   SubjectModel subjectModel = SubjectModel.fromJson(response.data);
    //   subjectModel.lectures.forEach((lecture) {
    //     lecture.lessons.forEach((lesson) {
    //       lessonsIds.add(lesson.id);
    //     });
    //   });

    //   if (subject.sectionsCnt == 1) {
    //     if (subject.sections.first.lessons.length == 1 &&
    //         !lessonsIds.contains(subject.sections.first.lessons.first.id)) {
    //       HiveHelper.deleteSubjectFromHive(subject);
    //       HiveHelper.downloads.delete(subject.sections.first.lessons.first.id);

    //       appController.showToast(Get.context!,
    //           message: 'Lesson deleted successfully'.tr,
    //           status: ToastStatus.success);
    //       Get.back();
    //       return;
    //     }
    //   }
    //   sections.forEach((section) {
    //     section.lessons.forEach(
    //       (element) {
    //         if (!lessonsIds.contains(element.id)) {
    //           log("Does not contains");
    //           deleteLessonFromMemory(
    //               subjectId: subject.id,
    //               lessonId: element.id,
    //               sectionId: section.id,
    //               lessonName: "");
    //         }
    //       },
    //     );
    //   });
    // }

    log('sections ${sections.length}');
    log('lessons ${lessons.length}');
  }

  RxBool _dataLoading = true.obs;
  get dataLoading => this._dataLoading.value;
  set dataLoading(value) => this._dataLoading.value = value;

  void loadSubjectsData() async {
    dataLoading = true;
    log("getting");
    subjects = await HiveHelper.getAllSubjects();
    dataLoading = false;
  }

  lessonOnTap(String lessonName) async {
    log("lesson herer");
    String basePath = appController.videosDirectoryPath;
    Loading.overlayLoading(Get.context!);
    final ReceivePort receivePort = ReceivePort();
    await Isolate.spawn((sendPost) {
      decryptFile(basePath, lessonName, sendPost);
    }, receivePort.sendPort);
    receivePort.listen((message) {
      if (message == 'done') {
        Get.back();
        Get.to(
          () => DownloadedVideoPlayerPage(
            videoName: lessonName,
          ),
        );
      }
    });
  }

  deleteLessonFromMemory({
    required int subjectId,
    required int lessonId,
    required int sectionId,
    required String lessonName,
  }) {
    SubjectAdapter subject = HiveHelper.getSubjectById(subjectId);
    HiveHelper.downloads.delete(lessonId);
    if (subject.sections.length > 1 ||
        (subject.sections.length == 1 &&
            subject.sections[0].lessons.length > 1)) {
      int sectionIndex = HiveHelper.getSectionIndex(subjectId, sectionId);
      if (subject.sections[sectionIndex].lessons.length == 1) {
        subject.sections.removeAt(sectionIndex);
        sections.removeAt(sectionIndex);
      } else {
        int lessonIndex =
            HiveHelper.getLessonIndex(subject.sections[sectionIndex], lessonId);
        subject.sections[sectionIndex].lessons.removeAt(lessonIndex);
        lessons[sectionIndex].removeAt(lessonIndex);
      }
      HiveHelper.updateSubject(subject);
    } else {
      HiveHelper.deleteSubjectFromHive(subject);
      Get.back();
    }
    appController.showToast(Get.context!,
        message: 'Lesson deleted successfully'.tr, status: ToastStatus.success);
    File('${appController.videosDirectoryPath}/${lessonId}_${lessonName}.aes')
        .deleteSync(recursive: true);
    loadSubjectsData();
  }

  // checkFromExpiredSubjects() async {
  //   List<SubjectAdapter> subjects = await HiveHelper.getAllSubjects();
  //   for (int i = 0; i < subjects.length; i++) {
  //     checkFromExpiredSubject(subjects[i], i);
  //   }
  // }

  // checkFromExpiredSubject(SubjectAdapter subject, int subjectIndex) async {
  //   ResponseModel response;
  //   response = await dataController.postData(
  //     url: API.checkIfSubjectIsExpire,
  //     body: {
  //       "subject_id": subject.id,
  //     },
  //   );
  //   if (response.success) {
  //     HiveHelper.deleteSubjectFromHive(subject);
  //   }
  // }

  @override
  void onInit() async {
    super.onInit();
    loadSubjectsData();
    // checkFromExpiredSubjects();
  }
}
