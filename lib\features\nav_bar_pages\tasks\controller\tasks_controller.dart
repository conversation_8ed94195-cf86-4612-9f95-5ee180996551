import 'dart:developer';

import 'package:all_in_one/features/nav_bar_pages/tasks/add_task_bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_calendar_carousel/classes/event.dart';
import 'package:flutter_calendar_carousel/flutter_calendar_carousel.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/models/task_date_model.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/models/task_model.dart';

class TasksController extends GetxController {
  DateTime now = DateTime.now();
  late Rx<DateTime> selectedDateTime =
      Rx<DateTime>(DateTime(now.year, now.month, now.day));
  DataController dataController = Get.find();
  AppController appController = Get.find();

  Rx<EventList<Event>>? markedDateMap;
  RxMap<DateTime, List<Event>> dates = RxMap({});
  RxBool rebuild = false.obs;
  RxBool _tasksPageLoading = true.obs;
  get tasksPageLoading => this._tasksPageLoading.value;
  set tasksPageLoading(value) => this._tasksPageLoading.value = value;

  List<TaskModel> tasks = <TaskModel>[];
  RxList<TaskModel> filteredTasks = <TaskModel>[].obs;
  RxBool _tasksLoading = true.obs;
  get tasksLoading => this._tasksLoading.value;
  set tasksLoading(value) => this._tasksLoading.value = value;

  TaskFilter currentFilter = TaskFilter.all;
  DateTime? specificFilterDate;

  Future<void> loadMyTasksData({String? date}) async {
    tasks.clear();
    tasksLoading = true;
    ResponseModel response = await dataController.getData(
      url: API.tasksApi(date),
    );
    if (response.success) {
      for (var element in response.data) {
        tasks.add(TaskModel.fromJson(element as Map<String, dynamic>));
      }

      tasksLoading = false;

      // Update the observable list using .assignAll()
      filterTasks(filter: currentFilter, specificDate: specificFilterDate);
    } else {
      tasksLoading = false;
      appController.showToast(Get.context!,
          message: response.message, status: ToastStatus.fail);
    }
  }

  List<TaskDateModel> tasksDate = [];
  Future<void> loadTaskDatesData() async {
    ResponseModel response;
    response = await dataController.getData(
      url: API.tasksByDateApi,
    );
    dates = RxMap({});
    if (response.success) {
      response.data
          .forEach((element) => tasksDate.add(TaskDateModel.fromJson(element)));
      for (int i = 0; i < tasksDate.length; i++) {
        dates[DateTime(
            tasksDate[i].year, tasksDate[i].month, tasksDate[i].day)] = [
          new Event(
            id: tasksDate[i].taskId,
            date: new DateTime(
                tasksDate[i].year, tasksDate[i].month, tasksDate[i].day),
          ),
        ];
      }

      markedDateMap = (new EventList<Event>(
        events: dates,
      )).obs;
    } else {}
  }

  void eventOnTap(String date) {
    loadMyTasksData();
  }

  void addTaskOnTap() async {
    await Nav.to(Pages.addTask)
        ?.then((value) => value ? initialTasksPage() : null);
  }

  bool checkIfDateIsEvent(DateTime date) {
    if (dates.containsKey(date)) {
      eventOnTap('${date.year}-${date.month}-${date.day}');
      return true;
    }
    return false;
  }

  initialTasksPage() async {
    tasksPageLoading = true;
    await Future.wait([loadMyTasksData(), loadTaskDatesData()]);
    tasksPageLoading = false;
  }

  deleteTask(int id) async {
    bool _isDeleting = false;
    DialogHelper.showDialog(
        dialogBody: Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text("are you sure you want to delete this task ?".tr,
            style: Get.textTheme.titleLarge
                ?.copyWith(color: AppStyle.primaryColor)),
        const SizedBox(height: 32),
        SizedBox(
          height: 46,
          child: Row(
            children: [
              Expanded(
                  child: AppButton(
                text: "delete".tr,
                withLoading: true,
                showLoadingText: false,
                onTap: () async {
                  _isDeleting = true;
                  await dataController.deleteData(
                      url: "${API.addTasksApi}/$id");
                  _isDeleting = false;
                  Get.back();
                  Get.back();

                  // await loadTaskDatesData();
                  tasksPageLoading = true;
                  await loadMyTasksData();
                  List<String> print = [];
                  dates[selectedDateTime.value]
                      ?.removeWhere((element) => element.id == id);
                  markedDateMap?.value.events.forEach((key, value) {
                    value.removeWhere((element) => element.id == id);
                  });
                  markedDateMap?.value.events
                      .removeWhere((key, value) => value.length == 0);
                  markedDateMap?.value.events.forEach((key, value) => print.add(
                      "$key: ${value.map((e) => e.toName).toList().join(" , ")}"));

                  markedDateMap?.refresh();
                  rebuild(!rebuild.value);
                  tasksPageLoading = false;
                },
                borderColor: AppStyle.redColor,
                fillColor: AppStyle.whiteColor,
                radius: 16,
                style:
                    Get.textTheme.bodyLarge?.copyWith(color: AppStyle.redColor),
              )),
              Expanded(
                  child: AppButton(
                text: "cancel".tr,
                withLoading: false,
                onTap: () => Get.back(),
                fillColor: AppStyle.whiteColor,
                radius: 16,
                style: Get.textTheme.bodyLarge
                    ?.copyWith(color: AppStyle.blackColor),
              ))
            ],
          ),
        )
      ],
    ));
  }

  editTask(BuildContext context, TaskModel taskModel) async {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (context) => AddTaskPageBottomSheet(
        taskModel: taskModel,
      ),
    );
  }

  @override
  void onInit() async {
    !await appController.internetChecker();
    initialTasksPage();
    super.onInit();
  }

  void filterTasks({
    required TaskFilter filter,
    DateTime? specificDate,
  }) {
    currentFilter = filter;
    specificFilterDate = specificDate;
    DateTime now = DateTime.now();
    tasksPageLoading = true;
    log("filter $filter");

    switch (filter) {
      case TaskFilter.all:
        filteredTasks.assignAll(tasks);
        break;

      case TaskFilter.thisDay:
        filteredTasks.assignAll(tasks.where((task) {
          return task.taskDateTime.year == now.year &&
              task.taskDateTime.month == now.month &&
              task.taskDateTime.day == now.day;
        }));
        break;

      case TaskFilter.thisWeek:
        // Calculate start of week (Sunday at 00:00:00)
        int daysToSubtract =
            now.weekday % 7; // 0 for Sunday, 1-6 for Monday-Saturday
        DateTime startOfWeek =
            DateTime(now.year, now.month, now.day - daysToSubtract);
        DateTime endOfWeek = startOfWeek.add(const Duration(days: 7));

        // Debug logging
        print('Current time: $now');
        print('Start of week: $startOfWeek (normalized)');
        print('End of week: $endOfWeek (normalized)');

        filteredTasks.assignAll(tasks.where((task) {
          // Compare only the date part, ignore time
          DateTime taskDate = DateTime(task.taskDateTime.year,
              task.taskDateTime.month, task.taskDateTime.day);
          bool isInRange = taskDate
                  .isAfter(startOfWeek.subtract(const Duration(seconds: 1))) &&
              taskDate.isBefore(endOfWeek);

          // Log task date and whether it's included
          if (task.taskDateTime.day == now.day) {
            print(
                'Task at ${task.taskDateTime} (normalized: $taskDate) is ${isInRange ? 'included' : 'excluded'}');
          }

          return isInRange;
        }));
        break;

      case TaskFilter.thisMonth:
        filteredTasks.assignAll(tasks.where((task) {
          return task.taskDateTime.year == now.year &&
              task.taskDateTime.month == now.month;
        }));
        break;

      case TaskFilter.specificDate:
        if (specificDate != null) {
          filteredTasks.assignAll(tasks.where((task) {
            return task.taskDateTime.year == specificDate.year &&
                task.taskDateTime.month == specificDate.month &&
                task.taskDateTime.day == specificDate.day;
          }));
        }
        break;
    }

    tasksPageLoading = false;
  }
}

extension EventToString on Event {
  String get toName {
    return "Event {id:$id title:$title description:$description date:$date}";
  }
}
