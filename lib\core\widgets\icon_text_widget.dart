import 'package:flutter/material.dart';

class IconTextButtonWidget extends StatelessWidget {
  final String text;
  final TextStyle textStyle;
  final Widget icon;
  final double space;
  final double? padding;
  final VoidCallback? onTap;
  IconTextButtonWidget(
      {required this.text,
      required this.textStyle,
      required this.icon,
      this.space = 8,
      this.padding = 5,
      this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: new EdgeInsets.all(padding!),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            icon,
            SizedBox(
              width: space,
            ),
            Expanded(
                child: Text(
              text,
              style: textStyle,
            ))
          ],
        ),
      ),
    );
  }
}
