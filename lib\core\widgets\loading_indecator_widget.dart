import 'package:all_in_one/core/style/style.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';


class LoadingIndicatorWidget extends StatelessWidget {
  final double? height;
  final double? width;
  Color? color;

  LoadingIndicatorWidget({this.height,this.width,Color? color}){
    this.color=color??AppStyle.primaryColor;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width:width?? double.infinity,
      height:height?? MediaQuery.of(context).size.width,
      child: Center(
        child: Lottie.asset(
          "assets/lottie/circular_loading.json",
          width: 100,
          delegates: LottieDelegates(
            values: [
              ValueDelegate.color(
                const ['**'],
                value: color,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
