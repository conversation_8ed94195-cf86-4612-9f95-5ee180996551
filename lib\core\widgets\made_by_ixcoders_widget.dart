import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:url_launcher/url_launcher.dart';

class MadeByIxcoadersWidget extends StatelessWidget {
  final AppController appController = Get.find();

  Future<void> openLauncher(String url, BuildContext context) async {
    try {
      await launchUrl(Uri.parse(url));
    } catch (e) {
      appController.showToast(context,
          message: 'هذا الرابط غير صالح', status: ToastStatus.warning);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (appController.isInReview) {
      return const SizedBox.shrink();
    }
    return InkWell(
      onTap: () {
        openLauncher('https://ixcoders.com/', context);
      },
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              'تم التطوير بواسطة',
              style: Theme.of(context)
                  .textTheme
                  .bodyLarge
                  ?.copyWith(color: Colors.grey),
              // style: TextStyle(fontSize: 14, color: Colors.grey)
            ),
            SizedBox(
              width: 3,
            ),
            Text(
              'IXCoders',
              style: Theme.of(context)
                  .textTheme
                  .bodyLarge
                  ?.copyWith(color: AppStyle.ixCodersColor),
            )
          ],
        ),
      ),
    );
  }
}
