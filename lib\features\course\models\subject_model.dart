import 'package:get/get.dart';
import 'package:all_in_one/features/course/models/section_lesson_model.dart';

import 'course_section_model.dart';

class SubjectModel {
  int id;
  String title;
  int subjectPrice;
  int lecturesCnt;
  int teacherId;
  int yearId;
  int semesterId;
  int orederItem;
  String teacherName;
  String yearName;
  String banner;
  String mainImage;
  Teacher teacher;
   List<CourseSectionModel> lectures;
  Rx<bool> isSubjectSubscription;

  SubjectModel(
      {required this.id,
      required this.title,
      required this.subjectPrice,
      required this.lecturesCnt,
      required this.teacherId,
      required this.teacherName,
      required this.yearName,
      required this.yearId,
      required this.orederItem,
      required this.mainImage,
      required this.semesterId,
      required this.banner,
      required this.teacher,
      required this.lectures,
      required this.isSubjectSubscription});
  factory SubjectModel.fromJson(Map<String, dynamic> json) => SubjectModel(
      id: json['id'],
      title: json['title'],
      subjectPrice: json['price'],
      lecturesCnt: json['lectures_cnt']??5,
      teacherId: json['teacher_id'],
      teacherName: json['teacher_name'],
      semesterId: json['semester_id']??8,
      orederItem: json['order_item'],
      yearName: json['year_name'],
      yearId: json['year_id']??2,
      banner: json['banner']['original_url'],
      teacher: Teacher.fromJson(json['teacher']),
      lectures:json['lectures']!=null?
       List<CourseSectionModel>.from(json['lectures'].map((item)=>CourseSectionModel.fromJson(item))):[],
      isSubjectSubscription: Rx<bool>(json['is_subscribed'] == 1),
      mainImage:json['main_image']['original_url'],
      
      );
}

class Teacher {
  int teacherId;
  int id;
  String name;
  String avatar;
  String about;
  String email;
  String phone;
  String dialCountryCode;
  int cityId;

  Teacher(
      {required this.teacherId,
      required this.id,
      required this.name,
      required this.email,
      required this.phone,
      required this.dialCountryCode,
      required this.avatar,
      required this.about,
      required this.cityId});

  factory Teacher.fromJson(Map<String, dynamic> json) => Teacher(
        teacherId: json['teacher_id'],
        id: json['id'],
        avatar: json['avatar']['original_url'],
        about: json['about'] ?? '',
        name: json['name'],
        email: json['email'],
        phone: json['phone'],
        dialCountryCode: json['dial_country_code'],
        cityId: json['city_id'],
      );
}
