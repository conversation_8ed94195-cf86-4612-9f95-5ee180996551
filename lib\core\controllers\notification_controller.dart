import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';

class NotificationController extends GetxController {
  FirebaseMessaging? messageInstance;
  FlutterLocalNotificationsPlugin? flutterLocalNotificationsPlugin;

  Future<String?> fcmToken() async {
    String? token;
    try {
      token = await FirebaseMessaging.instance.getToken();
    } catch (e) {
      log(e.toString());
    }
    log('FCM_TOKEN: $token');
    return token;
  }

  static void deleteFcmToken() => FirebaseMessaging.instance.deleteToken;

  bool notificationClick(NotificationResponse details) {
    if (details.payload != null) {
      Map<String, dynamic>? data;
      try {
        data = jsonDecode(details.payload!);
      } catch (_) {}
      if (data != null) {
        log('notification data: ${data['item_type']}');
        if (data['item_type'] == 'order_payment_done' ||
            data['item_type'] == 'order_payment_faild') Get.back();
        interactionHandler(data);
      }
    }
    return false;
  }

  Future<void> initNotifications() async {
    messageInstance = FirebaseMessaging.instance;

    RemoteMessage? initialMessage =
        await FirebaseMessaging.instance.getInitialMessage();

    if (initialMessage != null) {
      log('notification data: ${initialMessage.data['item_type_id']}');
      interactionHandler(initialMessage.data);
    }

    initialBackgroundInteractive();
    foregroundListener();

    flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
    if (Platform.isAndroid) {
      flutterLocalNotificationsPlugin!
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()!
          .requestNotificationsPermission();
    }
    var android = AndroidInitializationSettings('@mipmap/ic_launcher');
    var ios = const DarwinInitializationSettings();
    var initSettings = InitializationSettings(android: android, iOS: ios);
    flutterLocalNotificationsPlugin?.initialize(
      initSettings,
      onDidReceiveNotificationResponse: (details) => notificationClick(details),
    );

    NotificationSettings? settings = await messageInstance?.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
  }

  void initialBackgroundInteractive() {
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) async {
      log('notification data: ${message.data['click_action']}');
      interactionHandler(message.data);
    });
  }

  void interactionHandler(Map<String, dynamic> data) {}

  void showNotification({
    String? title,
    String? summaryText,
    String? extraText,
    String? payload,
    String? body,
    int? id,
  }) async {
    var android = AndroidNotificationDetails(
      'gwthani',
      'gwthani_channel',
      importance: Importance.max,
      priority: Priority.max,
      groupKey: 'gwthani_group',
      setAsGroupSummary: true,
      enableVibration: true,
      groupAlertBehavior: GroupAlertBehavior.all,
      styleInformation: InboxStyleInformation([body ?? "", extraText ?? ''],
          contentTitle: title,
          summaryText: summaryText,
          htmlFormatSummaryText: false),
    );
    var ios = const DarwinNotificationDetails();
    var platform = NotificationDetails(android: android, iOS: ios);
    await flutterLocalNotificationsPlugin!
        .show(id ?? 1, title, body, platform, payload: payload);
  }

  void foregroundListener() {
    FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
      showNotification(
          title: message.notification?.title,
          body: message.notification?.body,
          extraText: message.data['url'] ?? null,
          payload: jsonEncode(message.data));
    });
  }
}
