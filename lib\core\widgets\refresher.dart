// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
//
// import '../controllers/data_controller.dart';
// import '../models/general/observable_variable.dart';
// import '../models/general/response_model.dart';
// import 'error_widget.dart';
// import 'loading.dart';
//
// class ListRefresher<T> extends StatefulWidget {
//   final Widget Function(List<T>) child;
//   final T Function(Map<String, dynamic>) fromJson;
//   final Function(ObsList<T>)? onDataFitched;
//   final String api;
//   final bool isFullUrl;
//
//   ListRefresher({
//     Key? key,
//     required this.api,
//     required this.fromJson,
//     required this.child,
//     this.onDataFitched,
//     this.isFullUrl = true,
//   }) : super(key: key);
//
//   @override
//   State<ListRefresher<T>> createState() => _ListRefresherState<T>();
// }
//
// class _ListRefresherState<T> extends State<ListRefresher<T>> {
//   final DataController dataController = Get.find();
//
//   ObsList<T> data = ObsList([]);
//
//   loadData() async {
//     ResponseModel response = await dataController.getData(
//       url: widget.api,
//       isFullURL: widget.isFullUrl,
//     );
//     if (response.success) {
//       List<T> temp = [];
//       response.data.forEach((json) => temp.add(widget.fromJson(json)));
//       data.value = temp;
//       if (widget.onDataFitched != null) {
//         if (widget.onDataFitched != null) widget.onDataFitched!(data);
//       }
//     } else {
//       data.error = response.message;
//     }
//   }
//
//   @override
//   void initState() {
//     loadData();
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Obx(() {
//       if (data.loading) {
//         return LoadingWidget();
//       } else if (data.hasError) {
//         return AppErrorWidget(error: data.error!);
//       } else {
//         data.valueLength;
//         return widget.child(data.value!);
//       }
//     });
//   }
// }
//
// // class ObjectRefresher<T> extends StatelessWidget {
// //   final ObsVar<T> data;
// //   final Widget Function(T) child;
// //   const ObjectRefresher({Key key, this.data, this.child}) : super(key: key);
// //   @override
// //   Widget build(BuildContext context) {
// //     return Obx(() {
// //       if (data.loading) {
// //         return Loading();
// //       } else if (data.hasError) {
// //         return AppErrorWidget(error: data.error);
// //       } else {
// //         return child(data.value);
// //       }
// //     });
// //   }
// // }
