// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'subject_adapter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SubjectAdapterAdapter extends TypeAdapter<SubjectAdapter> {
  @override
  final int typeId = 0;

  @override
  SubjectAdapter read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SubjectAdapter(
      title: fields[1] as String,
      id: fields[0] as int,
      media: fields[2] as String,
      teacherName: fields[3] as String,
      banner: fields[5] as String,
      sectionsCnt: fields[4] as int,
      sections: (fields[6] as List).cast<SubjectSectionAdapter>(),
    );
  }

  @override
  void write(BinaryWriter writer, SubjectAdapter obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.media)
      ..writeByte(3)
      ..write(obj.teacherName)
      ..writeByte(4)
      ..write(obj.sectionsCnt)
      ..writeByte(5)
      ..write(obj.banner)
      ..writeByte(6)
      ..write(obj.sections);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SubjectAdapterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
