import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:all_in_one/core/style/style.dart';


class DatePicker{
  static Future<DateTime?> showPicker(BuildContext context,DateTime? selectedDate,TextEditingController birthDayController) async{
    DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? DateTime(2000),
      firstDate: DateTime(1900),
      lastDate: DateTime(2030),
      cancelText: 'cancel'.tr,
      initialEntryMode: DatePickerEntryMode.calendarOnly,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppStyle.primaryColor,
              onPrimary: AppStyle.whiteColor,
              onSurface: AppStyle.lightBlackColor,
            ),
            
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppStyle.greenColor, // button text color
              ),
            ),
          ),
          child: child!,
        );
      },
    );
    if (pickedDate != null) {
      String formattedDate = DateFormat('yyyy-MM-dd').format(pickedDate);
      selectedDate = pickedDate;
      birthDayController.text = formattedDate;
    }
    return selectedDate;
  }
}