import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:all_in_one/core/style/style.dart';

class BottomNavItem extends StatelessWidget {
  final bool isActive;
  final String? icon;
  final String? title;

  const BottomNavItem({Key? key, this.isActive = false, this.icon, this.title})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      transitionBuilder: (child, animation) {
        return ScaleTransition(scale: animation, child: child);
      },
      duration: Duration(milliseconds: 200),
      reverseDuration: Duration(milliseconds: 100),
      child: Padding(
              padding: const EdgeInsets.only(top: 10),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 18,
                    width: 18,
                    child: Center(
                      child: SvgPicture.asset(
                        icon!,
                        color: isActive
                            ? AppStyle.primaryColor
                            : AppStyle.secondaryColor,
                      ),
                    ),
                  ),
                  SizedBox(height: 4),
                  Expanded(
                    child: Text(
                      title!,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 11,
                        color: isActive
                            ? AppStyle.primaryColor
                            : AppStyle.secondaryColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
