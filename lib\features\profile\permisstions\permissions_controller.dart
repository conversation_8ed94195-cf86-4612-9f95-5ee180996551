import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';

class PermissionsController extends GetxController {

  DataController dataController = Get.find();

  String permissions = '';
  RxBool _permissionsLoading = true.obs;
  get permissionsLoading  => this._permissionsLoading.value;
  set permissionsLoading (value) => this._permissionsLoading.value = value;

  void loadPermissionsData() async {
    permissionsLoading =true;
    ResponseModel response;
    response = await dataController.getData(
      url: API.settingDataApi('permissions'),
    );
    if(response.success){
      permissions = response.data[0];
      permissionsLoading =false;
    }else{
      loadPermissionsData();
    }
  }

  @override
  void onInit() {
    loadPermissionsData();
    super.onInit();
  }
}
