import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/app_animation/item_animation.dart';
import 'package:all_in_one/core/widgets/no_data_widget.dart';
import 'package:all_in_one/features/course/contollers/course_controller.dart';
import 'package:all_in_one/features/course/models/section_note_model.dart';

class CourseNotesSection extends StatelessWidget {
  final List<SectionNoteModel> notes;
  const CourseNotesSection({required this.notes});
  @override
  Widget build(BuildContext context) {
    CourseController controller = Get.find();
    return !controller.areNotesEmpty?AnimationLimiter(
      child: ListView.separated(
        itemCount: notes.length,
        padding: EdgeInsets.zero,
        itemBuilder: (context,index){
          return ItemAnimation(
            index: index,
            elementCount: 5,
            child:Container(
              margin: EdgeInsets.symmetric(horizontal: 15),
              padding: EdgeInsets.symmetric(horizontal: 20,vertical: 16),
              decoration: BoxDecoration(
                color: AppStyle.whiteBackgroundColor,
                borderRadius: BorderRadius.all(Radius.circular(10)),
                boxShadow: [
                  BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      blurRadius: 1.0,
                      spreadRadius: 1,
                      offset: const Offset(-2, 1) // changes position of shadow
                  ),
                ],
              ),
              child: Text(
                notes[index].note+notes[index].id.toString(),
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                    color: AppStyle.lightBlackColor,
                    fontWeight: FontWeight.w500
                ),
              ),
            ),
          ) ;
        },
        separatorBuilder: (context,index){
          return const SizedBox(height: 10,);
        },
      ),
    ) :NoDataWidget(
      imageWidth: 100,
      imageHeight: 100,
    );
  }
}
