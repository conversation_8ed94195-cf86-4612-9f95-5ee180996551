// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:encrypt/encrypt.dart';
import 'package:flutter/widgets.dart' hide Key;
import 'package:flutter_test/flutter_test.dart';
import 'package:path_provider/path_provider.dart';

import 'package:all_in_one/main.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  late EncryptTest encryptTest;
  String content = 'this a clear content';

  setUp(() async {
    String directory = "test";
    String content = 'this a clear content';
    File testFile = new File("$directory/test.txt");
    if (await testFile.exists()) {
      await testFile.delete();
    }
    await testFile.create();
    await testFile.writeAsString(content);
    File encryptedFile = new File("$directory/test.ens");
    File decryptedFile = new File("$directory/decrypted.txt");
    encryptTest = EncryptTest(
        content: content,
        encryptedFile: encryptedFile,
        testFile: testFile,
        decryptedFile: decryptedFile,
        iv: IV.fromLength(16),
        key: Key.fromUtf8("abcdefghgklmnopqrstvwxyz!@#%^&*("));
  });

  test("Encryption Test", () async {
    log("Real Content : $content");
    log("Encrypted Content : ${await encryptTest.encrypt()}");
    log("Decrypted Content : ${await encryptTest.decrypt()}");
  });
}

class EncryptTest {
  File testFile;

  File encryptedFile;
  File decryptedFile;

  final String content;
  Key key;
  IV iv;

  EncryptTest(
      {required this.content,
      required this.encryptedFile,
      required this.testFile,
      required this.decryptedFile,
      required this.iv,
      required this.key});

  Future<String> encrypt() async {
    bool outFileExists = await encryptedFile.exists();

    if (!outFileExists) {
      await encryptedFile.create();
    }

    final videoFileContents = await testFile.readAsStringSync();

    final encrypter = Encrypter(AES(key));

    final encrypted = encrypter.encrypt(videoFileContents, iv: iv);

    File tmp = await encryptedFile.writeAsBytes(encrypted.bytes);
    return await tmp.readAsString();
  }

  Future<String> decrypt() async {
    bool outFileExists = await decryptedFile.exists();

    if (!outFileExists) {
      await decryptedFile.create();
    }

    final videoFileContents = await this.encryptedFile.readAsBytesSync();

    final encrypter = Encrypter(AES(key));

    final encryptedFile = Encrypted(videoFileContents);

    final decrypted = encrypter.decrypt(encryptedFile, iv: iv);

    final decryptedBytes = utf8.encode(decrypted);
    return (await decryptedFile.writeAsBytes(decryptedBytes)).readAsString();
  }
}
