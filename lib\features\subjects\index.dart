import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/features/course/contollers/course_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/course_list_item_widget.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/subject_widget.dart';
import 'package:all_in_one/features/subjects/controller.dart';
import 'package:all_in_one/features/subjects/widgets/subject_item.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';

class SubjectsPage extends StatelessWidget {
  const SubjectsPage({super.key});

  @override
  Widget build(BuildContext context) {
    SubjectsController controller = Get.put(SubjectsController());
    return Scaffold(
      body: Obx(
        () => controller.subjectsLoading.value
            ? Center(child: const CircularProgressIndicator())
            : Column(
                children: [
                  // AppBarWidget(centerWidget: Text(''),leadingIcon: Icon(Icons.arrow_back),),
                  AppBarWidget(
                    centerWidget: Text(
                      'Courses'.tr,
                      style: Theme.of(context).textTheme.titleLarge!.copyWith(
                          color: AppStyle.whiteColor,
                          fontWeight: AppFontWeight.bold),
                    ),
                    trailingIcon: SizedBox(
                      width: 35,
                      height: 35,
                    ),
                    leadingIcon: Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                    ),
                    trailingOnTap: () {},
                    leadingOnTap: () {
                      Get.back();
                    },
                  ),
                  controller.subjects.isEmpty
                      ? Padding(
                          padding: const EdgeInsets.only(top: 64),
                          child: Assets.icons.noDataIcon.svg())
                      : Expanded(
                          child: Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 20, horizontal: 10),
                          child: GridView.builder(
                            gridDelegate:
                                SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 2,
                                    crossAxisSpacing: 2,
                                    childAspectRatio: 1,
                                    mainAxisSpacing: 12),
                            itemCount: controller.subjects.length,
                            itemBuilder: (context, index) => SubjectItem(
                              onTap: () {
                                Nav.to(Pages.course, arguments: {
                                  'title': controller.subjects[index].title,
                                  'id': controller.subjects[index].id,
                                  'from_home': true
                                });
                              },
                              subjectModel: controller.subjects[index],
                            ),
                          ),
                        )),
                ],
              ),
      ),
    );
  }
}
