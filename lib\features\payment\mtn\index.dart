import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/validator.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/features/payment/mtn/controller.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';

class MtnCashPage extends GetView<MtnCashController> {
  const MtnCashPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(120),
        child: AppBarWidget(
          centerWidget: Text(
            "mtn cash".tr,
            style: Theme.of(context).textTheme.titleLarge!.copyWith(
                color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
          ),
          trailingIcon: SizedBox(
            width: 35,
            height: 35,
          ),
          leadingIcon: Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          trailingOnTap: () {},
          leadingOnTap: () {
            Get.back();
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 22.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 32),
            Text("please enter the 6-digit code to verify the payment".tr,
                style: Get.textTheme.titleMedium),
            const SizedBox(height: 16),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 30),
              child: Directionality(
                textDirection: TextDirection.ltr,
                child: Pinput(
                  controller: controller.code,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  length: 6,
                  validator: (input) => Validator.verifyCodeValidation(input!),
                  errorTextStyle: Theme.of(context)
                      .textTheme
                      .bodyLarge!
                      .copyWith(color: AppStyle.redColor),
                  submittedPinTheme: PinTheme(
                    height: 65,
                    width: 65,
                    textStyle: Get.textTheme.titleLarge,
                    decoration: BoxDecoration(
                      color: AppStyle.textFieldColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  defaultPinTheme: PinTheme(
                    height: 65,
                    width: 65,
                    textStyle: Get.textTheme.titleLarge,
                    decoration: BoxDecoration(
                      color: AppStyle.textFieldColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 12),
            Align(
              alignment: Alignment.center,
              child: Obx(() => Text(
                    controller.error.value ?? "",
                    textAlign: TextAlign.center,
                    style: Get.textTheme.titleMedium
                        ?.copyWith(color: AppStyle.redColor),
                  )),
            ),
            const SizedBox(
              height: 64,
            ),
            AppButton(
              text: 'Verify'.tr,
              height: 60,
              radius: 10,
              withLoading: true,
              margin: EdgeInsets.zero,
              style: Get.textTheme.titleMedium!.copyWith(
                  color: AppStyle.whiteColor, fontWeight: FontWeight.bold),
              onTap: controller.onSubmit,
            ),
          ],
        ),
      ),
    );
  }
}
