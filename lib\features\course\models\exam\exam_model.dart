

import 'question_model.dart';

class ExamModel{
  const ExamModel({
    required this.id,
    required this.title,
    required this.totalMark,
    required this.questionCount,
    this.questions = const [],
  });

  final int id;
  final String title;
  final int totalMark;
  final int questionCount;
  final List<QuestionModel> questions;

  factory ExamModel.fromJson(Map<String, dynamic> json) => ExamModel(
    id: json["id"],
    title: json["title"],
    totalMark: json["total_mark"],
    questionCount: json["question_count"],
    questions: json["questions"] != null
        ? List<QuestionModel>.from(
        json["questions"].map((x) => QuestionModel.fromMap(x)))
        : [],
  );

}
