import 'package:flutter/material.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/cached_image_widget.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_new_model.dart';

class NewItemPreviewWidget extends StatelessWidget {
  const NewItemPreviewWidget({
    super.key,
    required this.newModel,
    required this.onTap,
  });

  final HomeNewModel newModel;
  final Function() onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsetsDirectional.only(end: 10),
        child: Column(
          children: [
            Expanded(
              child: SizedBox(
                width: 150,
                child: Card(
                  color: AppStyle.primaryColor,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12)),
                  child: CachedImageWidget(
                    imageUrl: newModel.media,
                    width: 75,
                    borderRadius: BorderRadius.circular(12),
                    // height: 75,
                    isCircular: false,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 6,
            ),
            Text(
              newModel.title,
              style: Theme.of(context).textTheme.labelMedium!.copyWith(
                    color: AppStyle.blackTextColor,
                  ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
