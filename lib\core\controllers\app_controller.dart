import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:path_provider/path_provider.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/controllers/global_controller.dart';
import 'package:all_in_one/core/hive/hive_helper.dart';
import 'package:all_in_one/core/models/app/user.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import '../constants/defaults.dart';
import '../constants/enum.dart';
import '../constants/localization.dart';
import '../constants/role.dart';
import '../constants/storages_names.dart';
import '../utils/connectivity.dart';
import '../widgets/toast.dart';

class AppController extends GetxController {
  GetStorage box = GetStorage(StoragesNames.app);

  FToast fToast = FToast();
  late String baseUrl;

  late Role role = Default.defaultRole;
  UserModel? user;
  String token = '';
  bool hasActiveDownload = false;
  Rx<AppLocalization> _locale = Default.defaultLocale.obs;
  AppLocalization get locale => this._locale.value;
  set locale(AppLocalization value) => this._locale.value = value;
  bool optionalUpdate = false;
  Map<String, String> generalSettings = {};
  bool isInReview = false;
  String dialCountryCode = "";
  RxBool isLoading = false.obs;

  // Bookmarks functionality
  RxList<String> bookmarks = <String>[].obs;

  getBaseUrl() async {
    isLoading(true);
    String? deviceId = await Get.find<GlobalController>().getDeviceId();

    baseUrl = "https://allinone.college/api/v1/";
    Get.put(DataController(withLog: false));
    // if (Platform.isIOS) {
    //   try {
    //     final response = await Dio().get(
    //         "https://allinone-ff18c-default-rtdb.firebaseio.com/inReview.json");
    //     isInReview = response.data;
    //   } catch (error) {
    //     isInReview = true;
    //   }
    // } else {
    //   isInReview = false;
    // }
    isLoading(false);
  }

  updateLocale(String language) {
    var localization = getAppLanguageSymbol(language);
    if (localization == AppLocalization.Ar) {
      Get.updateLocale(AppLocalization.Ar.locale);
      setLocale(AppLocalization.Ar);
    } else if (localization == AppLocalization.En) {
      Get.updateLocale(AppLocalization.En.locale);
      setLocale(AppLocalization.En);
    }
  }

  String getAppLanguageLabel() {
    if (locale == AppLocalization.Ar) {
      return 'Arabic'.tr;
    } else if (locale == AppLocalization.En) {
      return 'English'.tr;
    }
    return '';
  }

  AppLocalization getAppLanguageSymbol(String language) {
    if (language == 'Arabic') {
      return AppLocalization.Ar;
    } else if (language == 'English') {
      return AppLocalization.En;
    } else {
      return AppLocalization.De;
    }
  }

  AppLocalization getAppLanguageFromLanguageSymbol(String language) {
    if (language == 'ar') {
      return AppLocalization.Ar;
    } else if (language == 'en') {
      return AppLocalization.En;
    }
    return AppLocalization.Ar;
  }

  bool get isEnglish => locale == AppLocalization.En;
  bool get isArabic => locale == AppLocalization.Ar;

  RxBool _connectivity = false.obs;
  bool get connectivity => this._connectivity.value;
  set connectivity(value) => this._connectivity.value = value;
  bool firstConnectivityTest = true;

  Future<bool> internetChecker() async {
    await Future.delayed(Duration(seconds: 2));
    bool checker = await MyConnectivity.check;
    if (checker && !connectivity) {
      if (firstConnectivityTest) {
        firstConnectivityTest = false;
      } else {
        showToast(Get.context!,
            message: 'Your internet connection has been restored'.tr,
            status: ToastStatus.success);
      }
      connectivity = !connectivity;
    } else if (!checker) {
      if (firstConnectivityTest) {
        firstConnectivityTest = false;
      }
      connectivity = false;
      showToast(Get.context!,
          message: 'No Internet, Please check your connection'.tr,
          status: ToastStatus.warning);
    }

    return connectivity;
  }

  @override
  void onInit() async {
    await getBaseUrl();

    await loadUserData();
    super.onInit();
  }

  loadUserData() async {
    await box.initStorage;
    if (box.hasData('locale')) {
      locale = AppEnum.getLocaleFromName(box.read('locale'));
      if (locale != Default.defaultLocale) {
        Get.updateLocale(locale.locale);
        setLocale(locale);
      }
    } else {
      locale = getAppLanguageFromLanguageSymbol('en');
      Get.updateLocale(Locale('ar', 'AE'));
    }
    if (box.hasData('token') && box.read('token') != '') {
      this.token = box.read('token');
    }
    if (box.hasData('user')) {
      this.user = UserModel.fromJson(box.read('user'));
      role = AppEnum.getRoleFromName(box.read('role'));
    } else {
      role = Role.guest;
    }
    // Load bookmarks
    if (box.hasData('bookmarks')) {
      List<dynamic> savedBookmarks = box.read('bookmarks');
      bookmarks.value = savedBookmarks.map((item) => item.toString()).toList();
    }
    initialVideoFolderPath();
    getSettings();
  }

  removeUserData() async {
    await box.initStorage;
    if (box.hasData('token') && box.read('token') != '') {
      box.remove('token');
    }
    if (box.hasData('user')) {
      box.remove('user');
    }
    if (box.hasData('role')) {
      box.remove('role');
    }
    if (box.hasData('currency_exchange')) {
      box.remove('currency_exchange');
    }
    deleteVideosFolder();
    role = Role.guest;
  }

  setUserData({
    required String token,
    required Role role,
    required UserModel user,
  }) {
    setToken(token);
    setRole(role);
    setUser(user);
  }

  logData() {}

  setRole(Role role) {
    box.write('role', role.name);
    this.role = role;
  }

  setLocale(AppLocalization locale) {
    box.write('locale', locale.value);
    this.locale = locale;
  }

  setToken(String token) {
    box.write('token', token);
    this.token = token;
  }

  setUser(UserModel user) {
    box.write('user', user.toJson());
    this.user = user;
  }

  removeUser() {
    if (box.hasData('user')) {
      box.remove('user');
    }
  }

  bool isGuestUser() {
    return role == Role.guest;
  }

  //############ TOAST ###############
  showToast(BuildContext context,
      {String? title,
      required String message,
      ToastStatus status = ToastStatus.fail}) {
    fToast.init(Get.overlayContext!);
    try {
      fToast.removeCustomToast();
    } catch (_) {}
    FocusManager.instance.primaryFocus?.unfocus();
    if (title == null) {
      if (status == ToastStatus.success)
        title = 'Success'.tr;
      else if (status == ToastStatus.warning)
        title = 'Warning'.tr;
      else
        title = 'Failure'.tr;
    }
    fToast.showToast(
      child: AppToast(
        title: title,
        message: message,
        locale: this.locale,
        status: status,
      ),
      toastDuration: 2.seconds,
    );
  }

  ///videos directory
  String directoryName = 'Videos';
  late String videosDirectoryPath;
  late String baseDirectoryPath;

  Future<String> getAppDirectoryPath() async {
    Directory appDocDir = await getApplicationDocumentsDirectory();
    baseDirectoryPath = appDocDir.path;
    return baseDirectoryPath;
  }

  initialVideoFolderPath() async {
    String appDocPath = await getAppDirectoryPath();
    videosDirectoryPath = "$appDocPath/$directoryName";
  }

  createVideosFolder() async {
    initialVideoFolderPath();
    await Directory('$videosDirectoryPath').create(recursive: true);
  }

  deleteVideosFolder() {
    Directory dir = Directory(videosDirectoryPath);
    dir.deleteSync(recursive: true);
    HiveHelper.deleteBoxContent();
  }

  getSettings() async {
    DataController dataController = Get.find();
    ResponseModel response = await dataController.getData(url: API.settingApi);
    if (response.success) {
      generalSettings = Map.castFrom(response.data);
    }
  }

  // Bookmark methods
  bool isBookmarked(String id) {
    return bookmarks.contains(id);
  }

  void addBookmark(String id) {
    if (!bookmarks.contains(id)) {
      bookmarks.add(id);
      box.write('bookmarks', bookmarks);
      update();
    }
  }

  void removeBookmark(String id) {
    if (bookmarks.contains(id)) {
      bookmarks.remove(id);
      box.write('bookmarks', bookmarks);
      update();
    }
  }

  // App name and icon methods for review mode
}
