// import 'package:get/get.dart';
//
// import '../constants/api.dart';
// import '../controllers/app_controller.dart';
// import '../controllers/data_controller.dart';
// import '../models/general/response_model.dart';
//
// class AuthRepo {
//   static DataController dataController = Get.find<DataController>();
//   static AppController appController = Get.find<AppController>();
//
//   static Future<ResponseModel> login(
//       String email, String password, String? fcmToken) async {
//     return await dataController.postData(
//       url: API.login,
//       body: {
//         "credential": email,
//         "password": password,
//         "fcm_token": fcmToken,
//       },
//     );
//   }
//
//   // static Future<ResponseModel> sendCodeToEmail(String email) async {
//   //   return await dataController.postData(
//   //     url: API.resendCode,
//   //     body: {"email": email},
//   //   );
//   // }
//   //
//   // static Future<ResponseModel> resetPassrod(
//   //   String password,
//   //   String confirmPassword,
//   // ) async {
//   //   return await dataController.postData(
//   //     url: API.resetPassword,
//   //     body: {
//   //       "password": password,
//   //       "password_confirmation": confirmPassword,
//   //     },
//   //   );
//   // }
//   //
//   // static Future<ResponseModel> verifyCode(
//   //     String email, String code, String? fcmToken) async {
//   //   return await dataController.postData(
//   //     url: API.verified,
//   //     body: {
//   //       "email": email,
//   //       "code": code,
//   //       "fcm_token": fcmToken,
//   //     },
//   //   );
//   // }
//   //
//   // static Future<ResponseModel> signup(data) async {
//   //   return await dataController.postData(
//   //     url: API.signup,
//   //     body: data,
//   //   );
//   // }
// }
