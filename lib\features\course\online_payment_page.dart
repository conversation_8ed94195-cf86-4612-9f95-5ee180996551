import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/features/course/contollers/course_controller.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'widgets/dialogs/subscription_status_dialog_body_widget.dart';

class OnlinePaymentPage extends StatelessWidget {
  const OnlinePaymentPage();

  @override
  Widget build(BuildContext context) {
    AppController controller = Get.find();
    WebViewController webViewController = WebViewController();
    webViewController
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(Get.arguments['url']))
      ..addJavaScriptChannel("print",
          onMessageReceived: (message) =>
              controller.showToast(context, message: message.message))
      ..setNavigationDelegate(NavigationDelegate(
        onNavigationRequest: (NavigationRequest request) async {
          AppController appController = Get.find();
          if (request.url
                  .startsWith('${appController.baseUrl}/payment/cancel') ||
              request.url.startsWith('${appController.baseUrl}/payment/done')) {
            try {
              CourseController courseController = Get.find();
              courseController.loadSubjectDetail();
            } catch (e) {}

            Get.back();
            return NavigationDecision.navigate;
          }
          return NavigationDecision.navigate;
        },
      ));
    return WillPopScope(
      onWillPop: () async {
        Get.back();
        // DialogHelper.showDialog(
        //     dialogBody: SubscriptionStatusDialogBodyWidget(status: SubscriptionStatus.Success)
        // );
        if ((Get.arguments['from_course'] as bool?) == true) {
          // CourseController courseController = Get.find();
          // courseController.afterSubscription(
          //     SubscriptionType.getTypeFromString(Get.arguments['type']),
          //     index: Get.arguments['index']);
        }
        return false;
      },
      child: SafeArea(
        child: Scaffold(body: WebViewWidget(controller: webViewController)),
      ),
    );
  }
}
