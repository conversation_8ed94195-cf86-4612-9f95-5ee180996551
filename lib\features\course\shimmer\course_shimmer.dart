import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';

import 'course_name_section_shimmer.dart';

class CourseShimmer extends StatelessWidget {
  final String title;
  const CourseShimmer({required this.title});
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          AppBarWidget(
            centerWidget: Text(
              title,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
            ),
            trailingIcon: SizedBox(
              width: 35,
              height: 35,
            ),
            leadingIcon: Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            trailingOnTap: () {},
            leadingOnTap: () {
              Get.back();
            },
          ),
          SkeletonWidget(
            radius: 0,
            width: Get.width,
            height: 220,
          ),
          const SizedBox(height: 15,),
          CourseNameSectionShimmer(),
          const SizedBox(height: 15,),
          ListView.separated(
            itemCount: 6,
            shrinkWrap: true,
            padding: EdgeInsets.symmetric(horizontal: 20),
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context,index){
              return SkeletonWidget(height: 65,radius: 10,);
            },
            separatorBuilder: (context,index){
              return const SizedBox(height: 10,);
            },
          )

        ],
      ),
    );
  }
}
