import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

class ItemAnimation extends StatelessWidget {
  Widget child;
  int index;
  int elementCount;
  int? milliseconds;
  ItemAnimation(
      {required this.child,
        required this.index,
        required this.elementCount,
        this.milliseconds = 600});

  @override
  Widget build(BuildContext context) {
    return AnimationConfiguration.staggeredGrid(
      position: index,
      columnCount: elementCount,
      duration: Duration(milliseconds: milliseconds! ),
      child: FadeInAnimation(
        child: SlideAnimation(curve: Curves.linear,horizontalOffset: 20,child: child)),
      );
  }
}