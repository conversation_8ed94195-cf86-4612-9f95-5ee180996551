import 'dart:developer';

import 'package:all_in_one/core/widgets/loading.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/controllers/download_service.dart';
import 'package:all_in_one/core/hive/adapters/lesson_adapter.dart';
import 'package:all_in_one/core/hive/adapters/subject_adapter.dart';
import 'package:all_in_one/core/hive/adapters/subject_section_adapter.dart';
import 'package:all_in_one/core/hive/hive_helper.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/services/youtube_link_generater.dart';
import 'package:all_in_one/core/utils/encription_helper.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/course/contollers/course_controller.dart';
import 'package:all_in_one/features/course/models/course_section_model.dart';
import 'package:all_in_one/features/course/models/section_lesson_model.dart';
import 'package:all_in_one/features/nav_bar_pages/offline/offline_controller.dart';
import 'package:all_in_one/features/video/widgets/exit_player_dialog.dart';
import 'package:all_in_one/core/controllers/download_and_merge_service.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:get/get.dart' hide Response;
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart' as PathProvider;
import "package:encrypt/encrypt.dart" as enc;

class NetworkVideoPlayerController extends GetxController {
  final DownloadAndMergeService _downloadAndMergeService =
      DownloadAndMergeService();
  late InAppWebViewController webViewController;
  late CourseController courseController;
  List<String> videoUrls = [];

  RxString selectedQuality = '720'.obs;
  qualityOnTap(String value) {
    selectedQuality.value = value;
  }

  late String lessonName;
  late String lessonSubTitle;
  late String url;
  late int lessonId;
  late int sectionId;
  late int subjectId;
  late String subjectTitle;
  late String sectionTitle;
  late String teacherName;
  late String subjectBanner;
  late String teacherAvatar;

  initialControllerVariable({
    required String lessonName,
    required String lessonSubTitle,
    required int lessonId,
    required int sectionId,
    required List<String> videoUrls,
    int? subjectId,
    String? sectionTitle,
    String? teacherName,
    String? subjectBanner,
    String? teacherAvatar,
    String? subjectTitle,
  }) async {
    this.sectionId = sectionId;
    this.lessonId = lessonId;
    this.lessonName = lessonName;
    this.lessonSubTitle = lessonSubTitle;
    this.videoUrls = videoUrls;

    this.teacherAvatar =
        teacherAvatar ?? courseController.subjectModel.teacher.avatar;
    this.subjectBanner = subjectBanner ?? courseController.subjectModel.banner;
    this.teacherName = teacherName ?? courseController.subjectModel.teacherName;
    this.subjectTitle = subjectTitle ?? courseController.subjectModel.title;
    this.subjectId = subjectId ?? courseController.subjectId;
    CourseSectionModel? courseSectionModel;
    try {
      courseSectionModel = findSectionById();
    } catch (e) {}
    this.sectionTitle = sectionTitle ?? courseSectionModel?.title ?? '';
    await getDownloadState();
  }

  incrementVideoCount(String quality) async {
    await dataController.getData(
      url: API.lessonCntApi(lessonId, quality),
    );
  }

  @override
  void onInit() async {
    if (Get.isRegistered<CourseController>()) {
      courseController = Get.find<CourseController>();
    }
    super.onInit();
  }

  getDownloadState() async {
    double? value = HiveHelper.downloads.get(lessonId);
    log("$value", name: "download value");
    if (value != null) {
      downloadVideoLoading = (true);
      downloadedValue(value > 0 ? value : 0);
      downloadingComplete = false;

      if (value == 1) {
        downloadingComplete = true;
      }
    }
  }

  Future<String> getVideoUrl(int lessonId, bool isHd) async {
    log("inside get Video Url function", name: "link");
    ResponseModel response = await dataController.postData(
        url: API.lesson,
        body: {"id": lessonId, "quality": isHd ? "720" : "360"});
    String link = response.data['url'];
    link = decryptLink(link);
    // log("Decrypted link is ${decryptLink(link)}", name: 'decrypted link');
    return link;
  }

  @override
  void onClose() {
    super.onClose();
  }

  /////download video section

  DataController dataController = Get.find();
  AppController appController = Get.find();
  CancelToken cancelToken = CancelToken();
  String? downloadTask;
  RxDouble downloadedValue = RxDouble(0);

  RxBool _downloadVideoLoading = false.obs;
  get downloadVideoLoading => this._downloadVideoLoading.value;
  set downloadVideoLoading(value) => this._downloadVideoLoading.value = value;

  RxBool _downloadingComplete = false.obs;
  get downloadingComplete => this._downloadingComplete.value;
  set downloadingComplete(value) => this._downloadingComplete.value = value;

  cancelDownload() async {
    downloadVideoLoading = false;
    downloadedValue(0);
    log("Canceling $downloadTask");
    if (selectedQuality.value == '720') {
      await FileDownloader().cancelTaskWithId("${lessonId}_video");
      await FileDownloader().cancelTaskWithId("${lessonId}_audio");
      HiveHelper.downloads.delete(lessonId);
    } else {
      await dataController.cancelDownload(lessonId);
    }
  }

  downloadVideo({required String videoName, required int lessonId}) async {
    Get.back();

    if (selectedQuality.value == '720') {
      // New logic for 720p
      downloadVideoLoading = true;
      incrementVideoCount(selectedQuality.value);

      final response = await dataController.getData(
        url: API.refresh(lessonId),
      );

      String videoLink = response.data['link_video_720'];
      String audioLink = response.data['link_audio_720'];

      await _downloadAndMergeService.downloadAndMergeVideo(
        videoUrl: videoLink,
        audioUrl: audioLink,
        videoName: videoName,
        lessonId: lessonId,
        onProgress: (val) {
          log("incoming value of $val");
          double progress = (val.toDouble());
          Get.find<NetworkVideoPlayerController>(tag: "$lessonId")
              .downloadedValue(progress > 0 ? progress : 0);
          HiveHelper.downloads.put(lessonId, progress);
        },
        onComplete: () async {
          log("Starting encryption after merge");
          encryptFile(appController.videosDirectoryPath, videoName);
          await saveLessonIntoDB();
          downloadVideoLoading = false;
          downloadingComplete = true;
          try {
            OfflineController controller = Get.find();
            controller.loadSubjectsData();
          } catch (e) {
            downloadVideoLoading = false;
          }
          log("Ending");
        },
        onError: () {
          downloadVideoLoading = false;
          appController.showToast(Get.context!,
              message: 'Something went wrong'.tr, status: ToastStatus.warning);
        },
      );
    } else {
      // Existing logic for 360p
      Loading.overlayLoading(Get.context!);
      String link = await getVideoUrl(lessonId, selectedQuality.value != '360');
      Get.back();

      downloadVideoLoading = true;
      incrementVideoCount(selectedQuality.value);
      try {
        downloadTask = await dataController.downloadVideo(
            url: link,
            lessonId: lessonId,
            videoName: videoName,
            metaData: {
              "lesson_id": lessonId,
              "section_id": sectionId,
              "section_name": sectionTitle,
              "subject_name": subjectTitle,
              "subject_id": subjectId,
              "lesson_name": lessonName,
              "lesson_subtitle": lessonSubTitle,
              "teacher_avatar": teacherAvatar,
              "subject_banner": subjectBanner,
              "teacher_name": teacherName,
              "video_urls": videoUrls,
            },
            onProgress: (val) {
              log("incoming value of $val");
              double progress = (val.toDouble());
              Get.find<NetworkVideoPlayerController>(tag: "$lessonId")
                  .downloadedValue(progress > 0 ? progress : 0);
              HiveHelper.downloads.put(lessonId, progress);
            },
            onComplete: () async {
              log("Starting");
              encryptFile(appController.videosDirectoryPath, videoName);
              await saveLessonIntoDB();
              downloadVideoLoading = false;
              downloadingComplete = true;
              try {
                OfflineController controller = Get.find();
                controller.loadSubjectsData();
              } catch (e) {
                downloadVideoLoading = false;
              }
              log("Ending");
            },
            cancel: cancelToken);
      } catch (e) {
        downloadVideoLoading = false;
        appController.showToast(Get.context!,
            message: 'Something went wrong'.tr, status: ToastStatus.warning);
      }
    }
  }

  saveLessonIntoDB() async {
    if (await checkIfSubjectInsideDB())
      addLessonIfSubjectExistBefore();
    else
      addLessonIfSubjectDoesntExist();
  }

  Future<bool> checkIfSubjectInsideDB() async {
    bool isExist = await HiveHelper.checkIfSubjectExist(subjectId.toString());
    return isExist;
  }

  Future<bool> checkIfSectionInsideDB() async {
    bool isExist = await HiveHelper.checkIfSectionExist(subjectId, sectionId);
    return isExist;
  }

  addLessonIfSubjectExistBefore() async {
    SubjectAdapter subject = HiveHelper.getSubjectById(subjectId);
    if (await checkIfSectionInsideDB()) {
      SubjectSectionAdapter section =
          HiveHelper.getSectionById(subjectId, sectionId)!;
      LessonAdapter lesson = createLessonAdapter();
      List<LessonAdapter> lessons = section.lessons;
      lessons.add(lesson);
      section.lessons = lessons;
    } else {
      SubjectSectionAdapter section = createSectionAdapter();
      subject.sections.add(section);
    }
    HiveHelper.updateSubject(subject);
  }

  ///addLessonIfSubjectDoesntExist
  addLessonIfSubjectDoesntExist() {
    SubjectAdapter subject = createSubjectAdapter();
    HiveHelper.addSubjectToHive(subject);
  }

  SubjectAdapter createSubjectAdapter() {
    SubjectSectionAdapter section = createSectionAdapter();
    SubjectAdapter subject = SubjectAdapter(
        title: subjectTitle,
        id: subjectId,
        media: teacherAvatar,
        banner: subjectBanner,
        teacherName: teacherName,
        sections: <SubjectSectionAdapter>[],
        sectionsCnt: 0);
    subject.sections.add(section);
    subject.sectionsCnt = 1;
    return subject;
  }

  SubjectSectionAdapter createSectionAdapter() {
    LessonAdapter lesson = createLessonAdapter();
    SubjectSectionAdapter section = SubjectSectionAdapter(
        id: sectionId, title: sectionTitle, lessons: <LessonAdapter>[]);
    section.lessons.add(lesson);
    return section;
  }

  LessonAdapter createLessonAdapter() {
    LessonAdapter lesson = LessonAdapter(
      id: lessonId,
      title: lessonName,
      subTitle: lessonSubTitle,
    );
    return lesson;
  }

  CourseSectionModel? findSectionById() {
    for (int i = 0; i < courseController.subjectModel.lecturesCnt; i++) {
      if (courseController.subjectModel.lectures[i].id == sectionId)
        return courseController.subjectModel.lectures[i];
    }
    return null;
  }

  cancelRequest() async {
    cancelToken.cancel();
    downloadVideoLoading = false;
    cancelToken = CancelToken();
  }

  closeVideo() {
    if (isFullScreen) exitFullScreen();
    Get.back();
  }

  RxBool _isFullScreen = false.obs;
  bool get isFullScreen => this._isFullScreen.value;
  set isFullScreen(value) => this._isFullScreen.value = value;

  void enterFullScreen() {
    isFullScreen = true;
    SystemChrome.setPreferredOrientations([]);
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  void exitFullScreen() {
    isFullScreen = false;
    SystemChrome.setPreferredOrientations([]);

    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  }

  refreshVideoLink(String? link, bool isHd, String youtubeUrl) async {
    bool isBroken = true;
    if (link != null) {
      Dio dio = Dio();
      log(link, name: "possible broken link");
      try {
        Response response = await dio.get(link);

        isBroken = response.statusCode == 403;
      } catch (e) {
        isBroken = true;
      }
    }

    if (isBroken) {
      YouTubeVideoResolutions youTubeVideoResolutions =
          YouTubeVideoResolutions();
      Map<String, String>? response =
          await youTubeVideoResolutions.getResolutions(youtubeUrl);
      String? newLink = isHd ? (response?['360']) : (response?['720']);
      log("Going to the new link : ${newLink}");
      await webViewController.loadUrl(
          urlRequest: URLRequest(url: WebUri(newLink!)));

      webViewController.reload();
    }
  }
}
