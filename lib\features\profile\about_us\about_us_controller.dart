import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';

class AboutUsController extends GetxController {
  DataController dataController = Get.find();
  AppController appController = Get.find();

  String aboutUs = '';
  RxBool _aboutUsLoading = true.obs;
  get aboutUsLoading => this._aboutUsLoading.value;
  set aboutUsLoading(value) => this._aboutUsLoading.value = value;

  void loadAboutUsData() async {
    aboutUsLoading = true;
    ResponseModel response;
    response = await dataController.getData(
      url: API.settingDataApi('about_us'),
    );
    if (response.success) {
      aboutUs = response.data[0];
      aboutUsLoading = false;
    } else {
      loadAboutUsData();
    }
  }

  @override
  void onInit() {
    loadAboutUsData();
    super.onInit();
  }
}
