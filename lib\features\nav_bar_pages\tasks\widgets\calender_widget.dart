import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_calendar_carousel/classes/event.dart';
import 'package:flutter_calendar_carousel/flutter_calendar_carousel.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:all_in_one/core/style/style.dart';

class CalenderWidget extends StatelessWidget {
  final EventList<Event>? markedDatesMap;
  final Rx<DateTime>? selectedDateTime;
  final bool rebuild;
  final Function(DateTime, List<Event>)? onDayPressed;
  const CalenderWidget(
      {this.markedDatesMap,
      this.rebuild = false,
      this.selectedDateTime,
      this.onDayPressed});

  @override
  Widget build(BuildContext context) {
    log("rebuild ${markedDatesMap}");
    return Obx(() => CalendarCarousel<Event>(
          todayBorderColor: Colors.transparent,
          todayButtonColor: Colors.transparent,
          todayTextStyle: TextStyle(
            color: AppStyle.lightBlackColor,
          ),

          onDayPressed: onDayPressed,
          daysHaveCircularBorder: false,
          showOnlyCurrentMonthDate: true,
          weekendTextStyle: TextStyle(
            color: AppStyle.lightBlackColor,
          ),
          daysTextStyle: TextStyle(
            color: AppStyle.lightBlackColor,
          ),
          weekdayTextStyle: TextStyle(
            color: AppStyle.primaryColor,
          ),
          thisMonthDayBorderColor: Colors.transparent,
          weekFormat: false,
          markedDatesMap: markedDatesMap,
          height: 400,
          customDayBuilder: (isSelectable, index, isSelectedDay, isToday,
              isPrevMonthDay, textStyle, isNextMonthDay, isThisMonthDay, day) {
            if (isToday || isSelectedDay) {
              return Center(
                child: CircleAvatar(
                  backgroundColor:
                      isToday ? AppStyle.secondaryColor : AppStyle.primaryColor,
                  child: Text(DateFormat("dd").format(day)),
                ),
              );
            }
          },
          selectedDateTime: selectedDateTime?.value,
          customGridViewPhysics: NeverScrollableScrollPhysics(),
          markedDateCustomShapeBorder:
              CircleBorder(side: BorderSide(color: AppStyle.primaryColor)),
          markedDateCustomTextStyle: TextStyle(
            fontSize: 18,
            color: Colors.blue,
          ),
          showHeader: true,
          headerTextStyle: Theme.of(context)
              .textTheme
              .titleLarge
              ?.copyWith(color: AppStyle.primaryColor),
          iconColor: AppStyle.primaryColor,
          selectedDayTextStyle: TextStyle(
            color: Colors.white,
          ),

          selectedDayBorderColor: Colors.transparent,
          selectedDayButtonColor: Colors.transparent,
          //nullable
          // minSelectedDate: tasksController.currentDate.subtract(Duration(days: 360)),
          maxSelectedDate: DateTime.now().add(Duration(days: 360)),
          prevDaysTextStyle: TextStyle(
            fontSize: 16,
            color: Colors.pinkAccent,
          ),
          inactiveDaysTextStyle: TextStyle(
            color: Colors.tealAccent,
            fontSize: 16,
          ),

          // onCalendarChanged: (DateTime date) {
          //
          //   // tasksController.targetDateTime = date;
          //   // tasksController.currentMonth = DateFormat.yMMM().format(tasksController.targetDateTime);
          //
          // },
          // onDayLongPressed: (DateTime date) {
          //   print('long pressed date $date');
          // },
        ));
  }
}
