import 'package:all_in_one/core/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/features/course/widgets/dialogs/fatora_subscription_dialog_body_widget.dart';
import 'bottom_sheet/teacher_details_bottom_sheet.dart';
import '../contollers/course_controller.dart';

class CourseNameSection extends StatelessWidget {
  const CourseNameSection();

  @override
  Widget build(BuildContext context) {
    CourseController controller = Get.find();
    AppController appController = Get.find();
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: SizedBox(
        width: Get.width,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  controller.subjectName,
                  style: Theme.of(context).textTheme.headlineSmall!.copyWith(
                        color: AppStyle.blackTextColor,
                      ),
                ),
                const SizedBox(
                  height: 4,
                ),
                if (!appController.isInReview)
                  Visibility(
                    visible: !controller.appController.isGuestUser(),
                    child: Text(
                      'Subscription price'.trParams({
                        'price': controller.subjectModel.subjectPrice.toString()
                      }),
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: AppStyle.lightBlackColor,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
              ],
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      controller.subjectModel.teacherName,
                      style: Theme.of(context).textTheme.titleMedium!.copyWith(
                          color: AppStyle.lightBlackColor,
                          fontWeight: FontWeight.w300),
                    ),
                    IconButton(
                      padding: EdgeInsets.zero,
                      onPressed: () => showTeacherDetailBottomSheet(
                          context,
                          controller.subjectModel.teacher,
                          controller.canContactWithTeacher),
                      icon: Assets.icons.infoIcon.svg(),
                    ),
                  ],
                ),
                if (!appController.isInReview)
                  Obx(
                    () => Visibility(
                      visible:
                          !controller.subjectModel.isSubjectSubscription.value,
                      child: AppButton(
                        text: 'Subscription'.tr,
                        height: 40,
                        radius: 10,
                        withLoading: false,
                        padding:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                        margin: EdgeInsets.zero,
                        style: Get.textTheme.titleMedium!.copyWith(
                            color: AppStyle.whiteColor,
                            fontWeight: FontWeight.bold),
                        onTap: () async {
                          if (appController.isGuestUser()) {
                            return Nav.offAll(Pages.login);
                          }
                          DialogHelper.showDialog(
                              dialogBody: FatoraSubscriptionDialogBodyWidget(
                            type: SubscriptionType.Subject,
                            name: controller.subjectModel.title,
                            price:
                                controller.subjectModel.subjectPrice.toString(),
                            id: controller.subjectModel.id,
                            fromCourse: true,
                          ));
                          // controller.afterSubscription(SubscriptionType.Subject);
                        },
                      ),
                    ),
                  )
              ],
            )
          ],
        ),
      ),
    );
  }
}
