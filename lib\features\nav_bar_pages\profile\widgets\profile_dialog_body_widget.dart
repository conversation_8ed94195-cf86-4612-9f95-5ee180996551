import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';

class ProfileDialogBodyWidget extends StatelessWidget {
  final String title;
  final String subTitle;
  final VoidCallback confirmOnTap;
  const ProfileDialogBodyWidget({
    required this.title,
    required this.subTitle,
    required this.confirmOnTap
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
            alignment: Alignment.topRight,
            child: Padding(
              padding: const EdgeInsets.only(top: 5 , right: 5),
              child: GestureDetector(
                child: Assets.icons.cancelDialog.svg(),
                onTap: ()=>Get.back(),
              ),
            )
        ),
        Text(
          title,
          textAlign: TextAlign.center,
          style: Get.textTheme.titleLarge!.copyWith(
              fontWeight: FontWeight.w700
          ),
        ),
        SizedBox(height: 16),
        Text(
          subTitle,
          textAlign: TextAlign.center,
          style: Get.textTheme.titleSmall!.copyWith(
            fontWeight: FontWeight.w500
          ),
        ),
        SizedBox(height: 25),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Expanded(
                child: AppButton(
                  height: 50,
                  radius: 15,
                  withLoading: false,
                  margin: EdgeInsets.zero,
                  fillColor: AppStyle.whiteColor,
                  style: Get.textTheme.bodyMedium!.copyWith(color: AppStyle.primaryColor,fontWeight: FontWeight.w700),
                  text: 'Cancel'.tr,
                  onTap: ()async => Get.back(),
                ),
              ),
              const SizedBox(width: 15,),
              Expanded(
                  child: AppButton(
                    height: 50,
                    withLoading: false,
                    radius: 15,
                    margin: EdgeInsets.zero,
                    style: Get.textTheme.bodyMedium!.copyWith(color: AppStyle.whiteColor,fontWeight: FontWeight.w700),
                    text: 'Confirm'.tr,
                    onTap: ()async{
                      confirmOnTap();
                    }
                  ),
              ),
            ],
          ),
        ),
        SizedBox(height: 12),
      ],
    );
  }
}
