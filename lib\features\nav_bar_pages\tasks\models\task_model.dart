import 'dart:core';

import 'package:flutter/material.dart';
import 'package:all_in_one/core/utils/hex_colors.dart';

class TaskModel {
  String title;
  String details;
  Color color;
  String taskDate;
  int id;

  DateTime get taskDateTime => DateTime.parse(taskDate);
  TaskModel(
      {required this.id,
      required this.title,
      required this.details,
      required this.color,
      required this.taskDate});

  factory TaskModel.fromJson(Map<String, dynamic> json) => TaskModel(
      title: json['title'],
      details: json['details'],
      id: json['id'],
      color: HexColor(json['flag']),
      taskDate: json['task_date']);
}
