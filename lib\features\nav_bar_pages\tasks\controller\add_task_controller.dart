import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_calendar_carousel/classes/event.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/controller/tasks_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/models/task_color_model.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/models/task_model.dart';

class AddTaskController extends GetxController {
  DataController dataController = Get.find();
  AppController appController = Get.find();
  late TaskModel? task = Get.arguments?["task"];

  AddTaskController({TaskModel? taskModel}) {
    task = taskModel;
    title.text = taskModel?.title ?? "";
    description.text = taskModel?.details ?? "";
    dateTextController.text = taskModel?.taskDate ?? "";
  }

  bool get editMode => task != null;

  DateTime now = DateTime.now();
  late Rx<DateTime> selectedDateTime =
      Rx<DateTime>(DateTime(now.year, now.month, now.day));

  TextEditingController title = TextEditingController();
  TextEditingController description = TextEditingController();
  TextEditingController dateTextController = TextEditingController();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  bool _taskColorsFetched = false;

  RxnInt selectedColor = RxnInt();
  List<TaskColorModel> tasksColors = [];
  RxBool _colorsLoading = true.obs;
  get colorsLoading => this._colorsLoading.value;
  set colorsLoading(value) => this._colorsLoading.value = value;

  // initTextController() {
  //   title.text = '';
  //   description.text = '';
  //   dateTextController.text = '';
  // }
  void resetTaskFields() {
    title.clear();
    description.clear();
    selectedColor.value = 0;
    dateTextController.clear();
  }

  Future<void> loadTaskColorsData({bool forceRefresh = false}) async {
    if (_taskColorsFetched && !forceRefresh) return;

    try {
      colorsLoading = true;
      final response = await dataController.getData(url: API.taskColorsApi);

      if (response.success) {
        tasksColors.clear(); // Clear previous if any
        for (var element in response.data) {
          tasksColors.add(TaskColorModel.fromJson(element));
        }
        _taskColorsFetched = true;
      } else {
        appController.showToast(Get.context!,
            message: response.message, status: ToastStatus.fail);
      }
    } catch (e) {
      appController.showToast(Get.context!,
          message: 'Failed to load task colors', status: ToastStatus.fail);
    } finally {
      colorsLoading = false;
    }
  }

  void colorOnTap(int id) {
    selectedColor.value = id;
  }

  dayOnTap(DateTime date) {
    selectedDateTime.value = date;
  }

  void addTasks() async {
    if (selectedColor.value == null) {
      appController.showToast(Get.context!,
          message: 'Please select color first'.tr, status: ToastStatus.fail);
      Get.back();
      return;
    }
    if (formKey.currentState!.validate()) {
      ResponseModel response;
      response = await dataController.postData(
        url: API.addTasksApi,
        body: {
          "title": title.text,
          "details": description.text,
          "task_date":
              "${selectedDateTime.value.year}-${selectedDateTime.value.month}-${selectedDateTime.value.day}",
          "color_id": selectedColor.value
        },
      );
      if (response.success) {
        appController.showToast(Get.context!,
            message: response.message, status: ToastStatus.success);
        Get.back();
        Get.back(result: true);
        resetTaskFields();
        Get.find<TasksController>().loadMyTasksData();
      } else {
        Get.back();
        appController.showToast(Get.context!,
            message: response.message!, status: ToastStatus.fail);
      }
    } else {
      Get.back();
    }
  }

  editTask() async {
    try {
      if (selectedColor.value == null) {
        appController.showToast(Get.context!,
            message: 'Please select color first'.tr, status: ToastStatus.fail);
        Get.back();
        return;
      }
      if (formKey.currentState!.validate()) {
        ResponseModel response;
        response = await dataController.putData(
          url: "${API.addTasksApi}/${task!.id}",
          body: {
            "title": title.text,
            "details": description.text,
            "task_date":
                "${selectedDateTime.value.year}-${selectedDateTime.value.month}-${selectedDateTime.value.day}",
            "color_id": selectedColor.value
          },
        );
        if (response.success) {
          TasksController tasksController = Get.find();
          tasksController.markedDateMap?.value
              .add(selectedDateTime.value, Event(date: selectedDateTime.value));
          await tasksController.loadMyTasksData();
          if (Get.isRegistered<HomePageController>()) {
            HomePageController homeController = Get.find();
            await homeController.loadHomeData();
          }
          appController.showToast(Get.context!,
              message: response.message, status: ToastStatus.success);
          Get.back();
          Get.back(result: true);
        } else {
          Get.back();
          appController.showToast(Get.context!,
              message: response.message!, status: ToastStatus.fail);
        }
      } else {
        Get.back();
      }
    } catch (e, stackTrace) {
      log("Erorr", error: e, stackTrace: stackTrace);
    }
  }

  loadTask() {
    if (task != null) {
      title.text = task!.title;
      description.text = task!.details;
      selectedDateTime(DateTime.parse(task!.taskDate));
      int? index = (tasksColors
          .firstWhereOrNull((element) => element.color == task!.color))?.id;
      log("selected Color ${task?.color}");
      log("selected Color ${tasksColors[2].color}");

      if (index != null) {
        selectedColor(index);
      }
    }
  }

  @override
  void onInit() async {
    super.onInit();
    !await appController.internetChecker();
    await loadTaskColorsData();
    loadTask();
  }
}
