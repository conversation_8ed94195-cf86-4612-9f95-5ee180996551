import 'dart:convert';
import 'dart:developer';
import 'package:dio/dio.dart';
import 'package:logger/logger.dart';

class YouTubeVideoResolutions {
  final Dio _dio = Dio();
  final Logger _logger = Logger();
  static const String _baseUrl = 'https://www.youtube.com/youtubei/v1/player';

  Future<Map<String, String>?> getResolutions(String videoUrl) async {
    try {
      final videoId = _extractVideoId(videoUrl);
      if (videoId == null) {
        _logger.e('Invalid video URL: $videoUrl');
        return null;
      }

      final apiKey = "AIzaSyDlvrxBbaDxXU5sw_KVxTF-lwVE9FKGz5w";

      final payload = _createPayload(videoId);
      final response = await _dio.post(
        _baseUrl,
        queryParameters: {"key": apiKey},
        options: Options(headers: {'Content-Type': 'application/json'}),
        data: jsonEncode(payload),
      );

      if (response.statusCode != 200) {
        _logger.e('Failed to fetch video data: ${response.statusCode}');
        return null;
      }

      return _parseResponse(response.data, videoId);
    } on DioError catch (e) {
      _logger.e('Dio error: ${e.message}');
      return null;
    } catch (e) {
      _logger.e('Unexpected error: $e');
      return null;
    }
  }

  String? _extractVideoId(String videoUrl) {
    final regex = RegExp(
      r'(?:youtube(?:-nocookie)?\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})',
    );
    final match = regex.firstMatch(videoUrl);
    return match?.group(1);
  }

  Map<String, dynamic> _createPayload(String videoId) {
    return {
      "videoId": videoId,
      "context": {
        "client": {
          "hl": "en",
          "clientName": "WEB",
          "clientVersion": "2.20210721.00.00",
          "clientFormFactor": "UNKNOWN_FORM_FACTOR",
          "clientScreen": "WATCH",
          "mainAppWebInfo": {"graftUrl": "/watch?v=${videoId}"}
        },
        "user": {"lockedSafetyMode": false},
        "request": {
          "useSsl": true,
          "internalExperimentFlags": [],
          "consistencyTokenJars": []
        }
      },
      "playbackContext": {
        "contentPlaybackContext": {
          "vis": 0,
          "splay": false,
          "autoCaptionsDefaultOn": false,
          "autonavState": "STATE_NONE",
          "html5Preference": "HTML5_PREF_WANTS",
          "lactMilliseconds": "-1"
        }
      },
      "racyCheckOk": false,
      "contentCheckOk": false
    };
  }

  Future<Map<String, String>?> _parseResponse(
      Map<String, dynamic> data, String videoId) async {
    log(data.keys.toList().join(" | "), name: "streamingData");
    final formats = data['streamingData']['formats'] as List<dynamic>;
    String? v360;
    String? v720;

    for (var format in formats) {
      log(format['url'].toString(), name: "url");
      if (format['qualityLabel'] == '360p') v360 = format['url'];
      if (format['qualityLabel'] == '720p') v720 = format['url'];
    }

    final vidLength = int.parse(data['videoDetails']['lengthSeconds']);
    if (await _getStatusCode(v360) == 403 || vidLength < 100) {
      _logger.w(
          'Video with ID $videoId is either too short or has a restricted 360p URL');
      return null;
    }
    log(v360 ?? "opps");
    return {
      '360': v360 ?? '',
      '720': v720 ?? '',
    };
  }

  Future<int> _getStatusCode(String? url) async {
    if (url == null) return 403;
    final response = await _dio.head(url);
    return response.statusCode ?? 403;
  }
}
