import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';

class CourseNameSectionShimmer extends StatelessWidget {
  const CourseNameSectionShimmer();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: SizedBox(
        width: Get.width,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SkeletonWidget(height: 20,radius: 0,width: 250,),
            const SizedBox(
              height: 8,
            ),
            SkeletonWidget(height: 15,radius: 0,width: 135,),
          ],
        ),
      ),
    );
  }
}
