import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/url_launcher.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_controller.dart';

class NewLinkWidget extends StatelessWidget {
  const NewLinkWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final newItem = Get.find<HomePageController>()
        .news[Get.find<HomePageController>().currentIndex];
    return Visibility(
        visible: newItem.link != null,
        child: SafeArea(
          child: Align(
            alignment: AlignmentDirectional.bottomCenter,
            child: Padding(
              padding: const EdgeInsets.only(bottom: 30.0),
              child: GestureDetector(
                onTap: () async {
                  await UrlLauncher.openLauncher(newItem.link ?? "");
                },
                child: Container(
                    margin: const EdgeInsets.all(12),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(32),
                        color: AppStyle.whiteColor),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Assets.icons.link.svg(
                            colorFilter: ColorFilter.mode(
                                AppStyle.primaryColor, BlendMode.srcIn)),
                        const SizedBox(width: 8),
                        Text("go to link".tr, style: Get.textTheme.bodyLarge),
                      ],
                    )),
              ),
            ),
          ),
        ));
  }
}
