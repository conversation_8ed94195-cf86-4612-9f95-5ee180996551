import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/features/course/contollers/course_controller.dart';
import 'package:all_in_one/features/course/widgets/dialogs/subscription_status_dialog_body_widget.dart';

class MtnCashController extends GetxController {
  final int callBackId = Get.arguments["id"]!;
  final String phone = Get.arguments["phone"]!;
  final String type = Get.arguments["type"];
  final bool fromCourse = Get.arguments['fromCourse'];
  final int? index = Get.arguments["index"];
  final TextEditingController code = TextEditingController();
  final DataController dataController = Get.find();
  final Rx<String?> error = Rx<String?>(null);
  onSubmit() async {
    ResponseModel responseModel = await dataController.postData(
        url: API.verifyMtnPayment,
        body: {"code": code.text, "phone": phone, "callback_id": callBackId});
    if (responseModel.success) {
      Get.back();
      DialogHelper.showDialog(
          dialogBody: SubscriptionStatusDialogBodyWidget(
              status: SubscriptionStatus.Success));
      CourseController courseController = Get.find();
      courseController.afterSubscription(
          SubscriptionType.getTypeFromString(Get.arguments['type']),
          index: index);

      Get.back();
    } else {
      Get.back();
      log("message");
      error(responseModel.errorsAsString);
    }
  }
}
