import 'package:all_in_one/features/nav_bar_pages/tasks/models/task_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/validator.dart';
import 'package:all_in_one/core/widgets/app_date_picker.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/date_picker.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';

import 'controller/add_task_controller.dart';

class AddTaskPageBottomSheet extends StatelessWidget {
  final TaskModel? taskModel;
  const AddTaskPageBottomSheet({this.taskModel});

  @override
  Widget build(BuildContext context) {
    AddTaskController controller = Get.put(
        AddTaskController(
          taskModel: taskModel,
        ),
        tag: "${taskModel?.id}");
    return SizedBox(
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.only(
            bottom: MediaQuery.of(context).viewInsets.bottom + 16,
            right: 16.0,
            left: 16,
          ),
          child: Form(
            key: controller.formKey,
            child: Column(
              children: [
                const SizedBox(
                  height: 15,
                ),
                Text(
                  controller.editMode ? "Edit Task".tr : 'Add Task'.tr,
                  style: Theme.of(context).textTheme.titleLarge!.copyWith(
                      color: AppStyle.blackColor,
                      fontWeight: AppFontWeight.bold),
                ),
                const SizedBox(
                  height: 15,
                ),
                Divider(
                  color: AppStyle.primaryColor,
                ),
                const SizedBox(
                  height: 15,
                ),
                Align(
                    alignment: AlignmentDirectional.topStart,
                    child: Obx(
                      () => Wrap(
                        direction: Axis.horizontal,
                        crossAxisAlignment: WrapCrossAlignment.center,
                        children: controller.colorsLoading
                            ? [
                                for (int i = 0; i < 5; i++)
                                  SkeletonWidget(
                                    width: 30,
                                    height: 30,
                                    radius: 0,
                                    margin: EdgeInsetsDirectional.only(
                                        end: 10, bottom: 10),
                                  )
                              ]
                            : [
                                for (int i = 0;
                                    i < controller.tasksColors.length;
                                    i++)
                                  Obx(
                                    () => InkWell(
                                      onTap: () => controller.colorOnTap(
                                          controller.tasksColors[i].id),
                                      child: Container(
                                        width: controller.selectedColor.value ==
                                                controller.tasksColors[i].id
                                            ? 50
                                            : 30,
                                        height:
                                            controller.selectedColor.value ==
                                                    controller.tasksColors[i].id
                                                ? 50
                                                : 30,
                                        margin: EdgeInsetsDirectional.only(
                                            end: 10, bottom: 10),
                                        decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(30),
                                            color:
                                                controller.tasksColors[i].color,
                                            border: controller
                                                        .selectedColor.value ==
                                                    controller.tasksColors[i].id
                                                ? Border.all(
                                                    color: controller
                                                        .tasksColors[i].color,
                                                    width: 12)
                                                : null),
                                      ),
                                    ),
                                  )
                              ],
                      ),
                    )),
                const SizedBox(
                  height: 15,
                ),
                TextFormField(
                  autofocus: true,
                  controller: controller.title,
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(color: AppStyle.lightBlackColor),
                  validator: Validator.notNullValidation,
                  decoration: InputDecoration(
                    hintText: "Title".tr,
                  ),
                ),
                const SizedBox(
                  height: 15,
                ),
                TextFormField(
                  autofocus: true,
                  controller: controller.description,
                  style: Theme.of(context)
                      .textTheme
                      .titleMedium
                      ?.copyWith(color: AppStyle.lightBlackColor),
                  validator: Validator.notNullValidation,
                  decoration: InputDecoration(
                    hintText: "Description".tr,
                  ),
                ),
                const SizedBox(
                  height: 15,
                ),
                AppDatePicker(
                  controller.dateTextController,
                  type: FieldTypeEnum.MainTheme,
                  isEnabled: false,
                  hint: 'التاريخ',
                  onTap: () async {
                    final date = await DatePicker.showPicker(
                      context,
                      DateTime.now(),
                      controller.dateTextController,
                    );
                    if (date != null) {
                      controller.selectedDateTime.value = date;
                      controller.dateTextController.text =
                          '${date.year}-${date.month}-${date.day}';
                    }
                  },
                  onChanged: (input) {
                    //  controller.description.text = input!;
                  },
                ),
                const SizedBox(
                  height: 15,
                ),
                AppButton(
                  text: controller.editMode ? "Edit".tr : 'Add'.tr,
                  height: 60,
                  radius: 10,
                  withLoading: true,
                  margin: EdgeInsets.zero,
                  
                  style: Get.textTheme.titleMedium!.copyWith(
                      color: AppStyle.whiteColor, fontWeight: FontWeight.bold),
                  onTap: () async => controller.editMode
                      ? controller.editTask()
                      : controller.addTasks(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
