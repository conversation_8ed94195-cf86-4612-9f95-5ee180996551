import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/validator.dart';
import 'package:all_in_one/core/widgets/app_date_picker.dart';
import 'package:all_in_one/core/widgets/app_drop_down.dart';
import 'package:all_in_one/core/widgets/app_drop_down_search.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/cached_image_widget.dart';
import 'package:all_in_one/core/widgets/date_picker.dart';
import 'package:all_in_one/core/widgets/image.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';

import 'change_password_controller.dart';

class ChangePasswordPage extends StatelessWidget {
  const ChangePasswordPage();

  // bool checkIfUserTester(){
  //   var service_end_date = '2024-01-27';
  //   DateTime endDate = DateTime.parse(service_end_date);
  //   DateTime now = DateTime.now();
  //   if(endDate.isAfter(now)){
  //     return true;
  //   }
  //   return false;
  // }

  @override
  Widget build(BuildContext context) {
    ChangePasswordController controller = Get.put(ChangePasswordController());
    return Scaffold(
      body: Column(
        children: [
          AppBarWidget(
            centerWidget: Text(
              'Update Password'.tr,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
            ),
            trailingIcon: SizedBox(
              width: 35,
              height: 35,
            ),
            leadingIcon: Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            trailingOnTap: () {},
            leadingOnTap: () => Get.back(),
          ),
          const SizedBox(
            height: 35,
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  const SizedBox(
                    height: 30,
                  ),
                  Form(
                    key: controller.formKey,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextFormField(
                          controller: controller.password,
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium
                              ?.copyWith(color: AppStyle.lightBlackColor),
                          validator: Validator.validatePass,
                          decoration: InputDecoration(
                            hintText: "password".tr,
                          ),
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        TextFormField(
                          controller: controller.passwordConfirmation,
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium
                              ?.copyWith(color: AppStyle.lightBlackColor),
                          validator: (confirmation) =>
                              Validator.passwordAndConfirmEqual(
                                  controller.password.text, confirmation ?? ""),
                          decoration: InputDecoration(
                            hintText: "password confirmation".tr,
                          ),
                        ),
                        const SizedBox(
                          height: 80,
                        ),
                        AppButton(
                          text: 'Save'.tr,
                          height: 60,
                          radius: 10,
                          withLoading: true,
                          margin: EdgeInsets.zero,
                          style: Get.textTheme.titleMedium!.copyWith(
                              color: AppStyle.whiteColor,
                              fontWeight: FontWeight.bold),
                          onTap: () async => controller.changePassword(context),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
