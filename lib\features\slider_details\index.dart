import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/image.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_banner_model.dart';

class SliderDetailsPage extends StatelessWidget {
  final HomeBannerModel homeBannerModel;
  const SliderDetailsPage({super.key, required this.homeBannerModel});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(homeBannerModel.title),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 16),
            AppImage(
              path: homeBannerModel.banner,
              borderRadius: BorderRadius.circular(16),
              type: ImageType.CachedNetwork,
              width: Get.width,
              fit: BoxFit.cover,
              height: 150,
            ),
            const SizedBox(height: 16),
            Text(
              homeBannerModel.text ?? " ",
              style:
                  Get.textTheme.bodyLarge?.copyWith(color: AppStyle.blackColor),
            )
          ],
        ),
      ),
    );
  }
}
