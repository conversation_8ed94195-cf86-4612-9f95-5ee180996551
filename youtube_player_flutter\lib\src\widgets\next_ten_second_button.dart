import 'package:flutter/material.dart';
import '../enums/player_state.dart';
import '../utils/youtube_player_controller.dart';

class NextTenSecondButton extends StatefulWidget {
  /// Overrides the default [YoutubePlayerController].
  final YoutubePlayerController? controller;

  /// Defines placeholder widget to show when player is in buffering state.
  final Widget? bufferIndicator;

  /// Creates [PlayPauseButton] widget.
  NextTenSecondButton({
    this.controller,
    this.bufferIndicator,
  });
  @override
  _NextTenSecondButtonState createState() => _NextTenSecondButtonState();
}

class _NextTenSecondButtonState extends State<NextTenSecondButton>
    with TickerProviderStateMixin{
  late YoutubePlayerController _controller;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final controller = YoutubePlayerController.of(context);
    if (controller == null) {
      assert(
      widget.controller != null,
      '\n\nNo controller could be found in the provided context.\n\n'
          'Try passing the controller explicitly.',
      );
      _controller = widget.controller!;
    } else {
      _controller = controller;
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final _playerState = _controller.value.playerState;
    if ((!_controller.flags.autoPlay  && _controller.value.isReady) ||
        _playerState == PlayerState.playing) {
      return Visibility(
        visible: _playerState == PlayerState.cued ||
            !_controller.value.isPlaying ||
            _controller.value.isControlsVisible,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(50.0),
            onTap: () => _controller.onNextTenButtonPressed(),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 25),
              child: Icon(
                Icons.forward_10,
                color: Colors.white,
                size: 30,
              ),
            ),
          ),
        ),
      );
    }
    if (_controller.value.hasError) return const SizedBox();
    return widget.bufferIndicator ??
        const SizedBox();
  }
}
