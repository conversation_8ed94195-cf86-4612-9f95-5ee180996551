import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';

class AppAuthPage extends StatelessWidget {
  final String title;
  final Widget child;
  const AppAuthPage({required this.child, required this.title});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      height: Get.height,
      decoration: BoxDecoration(
          gradient: LinearGradient(
        colors: [
          Color(0xff003399),
          Color(0xff0048D9),
        ],
        begin: Alignment.centerLeft,
        end: Alignment.centerRight,
      )
          // gradient: BoxGrad
          ),
      //  alignment: Alignment.center,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          //      Spacer(),
          Flexible(
            child: Container(
              alignment: Alignment.center,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 8.0),
                child: Assets.icons.appLogo.image(
                    //width: Get.width,
                    // fit: BoxFit.cover,
                    //    height: Get.height * 0.35,
                    width: 130, // size.height * 0.1,
                    height: 130 // size.height * 0.1,
                    ),
              ),
            ),
          ),
          //    Spacer(),
          Flexible(
            child: child,
            flex: 2,
          )
        ],
      ),
    );
  }
}
