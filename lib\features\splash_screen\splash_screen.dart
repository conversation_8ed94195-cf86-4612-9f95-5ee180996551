import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:video_player/video_player.dart';

import 'splash_controller.dart';

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  VideoPlayerController? _videoController;

  @override
  void initState() {
    super.initState();

    final AppController appController = Get.find<AppController>();

    // Only play video if not in review mode on iOS
    if (!(Platform.isIOS && appController.isInReview)) {
      // Initialize the video controller
      _videoController = VideoPlayerController.asset(Assets.videos.splash)
        ..initialize().then((_) {
          // Ensure the video is playing in a loop
          _videoController?.initialize();
          _videoController?.setLooping(true);
          _videoController?.play();
          setState(() {}); // Update the UI when video is initialized
        });
    }

    Get.put(SplashScreenController());
  }

  @override
  void dispose() {
    _videoController?.dispose(); // Dispose of the video controller
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final AppController appController = Get.find<AppController>();

    // Show review app icon for iOS in review mode
  

    // Show normal splash video for other cases
    if (_videoController != null && _videoController!.value.isInitialized) {
      return AspectRatio(
        aspectRatio: _videoController!.value.aspectRatio,
        child: VideoPlayer(_videoController!),
      );
    } else {
      return SizedBox();
    }
  }
}
