import 'dart:io';
import 'package:flutter/services.dart';

class PlatformUtils {
  static const MethodChannel _channel = MethodChannel('com.allinone.app/platform');

  static Future<void> updateAppName(String appName) async {
    if (Platform.isIOS) {
      try {
        await _channel.invokeMethod('updateAppName', {'appName': appName});
      } catch (e) {
        print('Error updating app name: $e');
      }
    }
  }

  static Future<void> updateAppIcon(String iconName) async {
    if (Platform.isIOS) {
      try {
        await _channel.invokeMethod('updateAppIcon', {'iconName': iconName});
      } catch (e) {
        print('Error updating app icon: $e');
      }
    }
  }
}
