// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/app_controller.dart';
import '../models/general/observable_variable.dart';

class AppDropDown<T> extends StatelessWidget {
  final List<T> values;
  final Function(T value) onChange;
  final T? initialValue;
  final String hint;
  final bool enabled;
  final String Function(T element) builder;
  final Widget? prefix;
  final String? error;

  AppDropDown({
    super.key,
    required this.hint,
    required this.values,
    required this.onChange,
    required this.builder,
    this.initialValue,
    this.enabled = true,
    this.prefix,
    this.error,
  }) {
    selected.value = initialValue;
  }
  AppController appController = Get.find();
  ObsVar<T> selected = ObsVar(null);

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<T>(
      enabled: enabled,
      initialValue: initialValue,
      position: PopupMenuPosition.under,
      padding: EdgeInsets.symmetric(horizontal: 32),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(15),
        side: BorderSide(
          color: Get.theme.primaryColor,
          width: .5,
        ),
      ),
      constraints: BoxConstraints(
        minWidth: Get.width * .45,
        maxWidth: Get.width * .8,
        minHeight: Get.height * .1,
        maxHeight: Get.height * .5,
      ),
      itemBuilder: (context) => values
          .map(
            (e) => PopupMenuItem<T>(
              child: Text(builder(e)),
              onTap: () {
                selected.value = e;
                selected.refresh();
                onChange(e);
              },
            ),
          )
          .toList(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 45,
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 18),
            decoration: BoxDecoration(
              color: Get.theme.inputDecorationTheme.fillColor,
              borderRadius: BorderRadius.circular(45),
              border: error != null
                  ? Border.all(color: Get.theme.colorScheme.error)
                  : null,
            ),
            child: Row(
              children: [
                if (prefix != null) SizedBox(width:19,child: Center(child: prefix!)),
                const SizedBox(width: 12),
                Expanded(
                  child: Obx(
                    () => selected.hasData
                        ? Text(
                            builder(selected.value!),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          )
                        : Text(
                            hint,
                            style: Get.theme.inputDecorationTheme.hintStyle,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                  ),
                ),
                Icon(Icons.keyboard_arrow_down_rounded),
              ],
            ),
          ),
          if (error != null && error!.isNotEmpty)
            Padding(
              padding: EdgeInsets.only(top: 8 ,bottom: 2, left:appController.isEnglish ? 14:0,right:appController.isEnglish ? 0: 14),
              child: Text(
                error!,
                style: TextStyle(color: Get.theme.colorScheme.error,overflow: TextOverflow.ellipsis, fontSize: 12),
              ),
            )
        ],
      ),
    );
  }
}
