import 'dart:developer';

import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/controllers/global_controller.dart';
import 'package:all_in_one/core/controllers/notification_controller.dart';
import 'package:all_in_one/core/models/app/user.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import '../../../core/constants/role.dart';
import '../../../core/controllers/app_controller.dart';

class LoginPageController extends GetxController {
  AppController appController = Get.find();
  DataController dataController = Get.find();
  GlobalController globalController = Get.find();
  NotificationController notificationController = Get.find();

  TextEditingController phone = TextEditingController();
  TextEditingController password = TextEditingController();

  Rx<String> _phoneError = ''.obs;
  String get phoneError => this._phoneError.value;
  set phoneError(String value) => this._phoneError.value = value;

  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  login(context) async {
    String? deviceId = await globalController.getDeviceId();
    bool isTestPhone = phone.text == "7897897899";
    appController.isInReview = isTestPhone;
    if (formKey.currentState!.validate()) {
      ResponseModel response;
      response = await dataController.postData(
        url: API.signIn,
        body: {
          'phone': phone.text,
          "password": password.text,
          'login_type': 'phone',
          "dial_country_code": appController.isInReview ? "001" : "963",
          "device_token": await notificationController.fcmToken(),
          'mobile_id': deviceId
        },
      );
      log(response.success.toString(), name: "response values");
      if (response.success) {
        // appController.createVideosFolder();
        // Get.back();
        try {
          appController.setUserData(
              user: UserModel.fromJson(response.data),
              token: response.data['token'],
              role: Role.user);
          Nav.offAll(Pages.navBar);
        } catch (e, stackTrace) {
          log(e.toString(), stackTrace: stackTrace);
        }
      } else if (response.code == ErrorCode.VALIDATION_ERROR ||
          response.errors != null ||
          (response.errors?.isNotEmpty ?? false)) {
        Get.back();
        appController.showToast(context, message: response.errorsAsString);
      } else {
        Get.back();
        appController.showToast(Get.context!,
            message: response.message!, status: ToastStatus.fail);
      }
    } else {
      Get.back();
    }
  }

  skip() {
    appController.setRole(Role.guest);
    Nav.offAll(Pages.navBar);
  }

  navToRegister() {
    Nav.to(Pages.signUp);
  }

  resetField() {
    phone.clear();
  }

  @override
  void onInit() {
    super.onInit();
  }
}
