import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/features/course/models/subject_model.dart';

class SubjectsController extends GetxController{
  final DataController dataController = Get.find();
  final RxBool subjectsLoading = true.obs;
  final String majorId = Get.parameters["major_id"]!;
  final String yearId = Get.parameters["year_id"]!;
  final String semesterId = Get.parameters["semester_id"]!;

   List<SubjectModel> subjects = [];
  getAllSubjects()async{
    subjectsLoading(true);
    ResponseModel responseModel = await dataController.getData(url: API.subjects,param: {
          "major_id":majorId,
      "year_id":yearId,
      "semester_id":semesterId
    });
    if(responseModel.success){
      subjects = List<SubjectModel>.from(responseModel.data.map((item)=>SubjectModel.fromJson(item)));
    
    }
    subjectsLoading(false);

  }

  @override
  void onInit() {
    getAllSubjects();
    super.onInit();
  }
}