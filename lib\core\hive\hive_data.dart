import 'dart:io';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:path_provider/path_provider.dart';
import 'package:all_in_one/core/hive/adapters/lesson_adapter.dart';
import 'package:all_in_one/core/hive/adapters/subject_adapter.dart';
import 'adapters/subject_section_adapter.dart';
import 'hive_helper.dart';

class HiveData {
  static init() async {
    Directory dir = await getApplicationDocumentsDirectory();

    Hive
      ..init(dir.path)
      ..registerAdapter(SubjectAdapterAdapter())
      ..registerAdapter(SubjectSectionAdapterAdapter())
      ..registerAdapter(LessonAdapterAdapter());
    await HiveHelper.openBox();
  }
}
