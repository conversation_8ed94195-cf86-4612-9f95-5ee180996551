

import 'choice_model.dart';

class QuestionModel {
  QuestionModel({
    required this.id,
    required this.question,
    required this.mark,
    required this.choices,
    required this.rightAnswerExplanation,
    required this.answerImage,
  });

  int id;
  String question;
  int mark;
  List<ChoiceModel> choices;
  String answerImage;
  String? rightAnswerExplanation;

  factory QuestionModel.fromMap(Map<String, dynamic> json) =>
      QuestionModel(
        id: json["id"],
        question: json["question"],
        mark: json["mark"],
        rightAnswerExplanation: json["right_answer_explanation"],
        choices: json["choices"] != null
            ? List<ChoiceModel>.from(
            json["choices"].map((x) => ChoiceModel.fromJson(x)))
            : [],
        answerImage: json["answer_image"]
      );

}