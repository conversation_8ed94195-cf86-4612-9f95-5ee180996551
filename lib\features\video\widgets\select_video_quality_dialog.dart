import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/features/video/controller/network_video_player_controller.dart';

class SelectVideoQualityDialog extends StatelessWidget {
  final List<String> videoUrls;
  final String lessonName;
  final int lessonId;
  const SelectVideoQualityDialog(
      {required this.videoUrls,
      required this.lessonName,
      required this.lessonId});

  @override
  Widget build(BuildContext context) {
    NetworkVideoPlayerController controller = Get.find(tag: "$lessonId");
    List<String> qualities = ['360', '720'];
    if (controller.appController.hasActiveDownload) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "You can only download one video at time".tr,
            style: Get.textTheme.titleMedium
                ?.copyWith(color: AppStyle.primaryColor),
          ),
          const SizedBox(height: 12),
          ElevatedButton(onPressed: Get.back, child: Text("Cancel".tr))
        ],
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
            alignment: Alignment.topRight,
            child: Padding(
              padding: const EdgeInsets.only(top: 5, right: 5),
              child: GestureDetector(
                child: Assets.icons.cancelDialog.svg(),
                onTap: () => Get.back(),
              ),
            )),
        Text(
          "Select the download quality".tr,
          textAlign: TextAlign.start,
          style:
              Get.textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w700),
        ),
        SizedBox(height: 16),
        ListView.builder(
          itemCount: qualities.length,
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (context, index) {
            return Obx(() => InkWell(
                  onTap: () {
                    controller.qualityOnTap(qualities[index]);
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                    child: Row(
                      children: [
                        SizedBox(
                          width: 18,
                          height: 18,
                          child: Radio(
                            value: qualities[index].toString(),
                            groupValue: controller.selectedQuality.value,
                            onChanged: (val) => controller.qualityOnTap,
                            fillColor: WidgetStateColor.resolveWith(
                              (Set<WidgetState> states) {
                                if (states.contains(WidgetState.selected)) {
                                  return AppStyle.primaryColor;
                                }
                                return AppStyle.lightGreyColor;
                              },
                            ),
                          ),
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                        Expanded(
                          child: Text(
                            qualities[index].toString() + "P",
                            textAlign: TextAlign.start,
                            style: Get.textTheme.titleSmall!
                                .copyWith(fontWeight: FontWeight.w500),
                          ),
                        ),
                      ],
                    ),
                  ),
                ));
          },
        ),
        SizedBox(height: 25),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Expanded(
                child: AppButton(
                  height: 50,
                  radius: 18,
                  withLoading: false,
                  margin: EdgeInsets.zero,
                  fillColor: AppStyle.whiteColor,
                  style: Get.textTheme.bodyMedium!.copyWith(
                      color: AppStyle.primaryColor,
                      fontWeight: FontWeight.w700),
                  text: 'Cancel'.tr,
                  onTap: () async => Get.back(),
                ),
              ),
              const SizedBox(
                width: 15,
              ),
              Expanded(
                child: AppButton(
                  height: 50,
                  withLoading: false,
                  radius: 15,
                  margin: EdgeInsets.zero,
                  style: Get.textTheme.bodyMedium!.copyWith(
                      color: AppStyle.whiteColor, fontWeight: FontWeight.w700),
                  text: 'Confirm'.tr,
                  onTap: () async {
                    controller.downloadVideo(
                      videoName: "${lessonId}_$lessonName",
                      lessonId: lessonId,
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 12),
      ],
    );
  }
}
