import 'package:all_in_one/features/profile/change_password/change_password_page.dart';
import 'package:get/get.dart';
import 'package:all_in_one/features/alternative_ui/index.dart';
import 'package:all_in_one/features/course/course_page.dart';
import 'package:all_in_one/features/course/online_payment_page.dart';
import 'package:all_in_one/features/course/pdf_reader_page.dart';
import 'package:all_in_one/features/nav_bar_pages/nav_bar/controller.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/add_task_bottom_sheet.dart';
import 'package:all_in_one/features/payment/mtn/check_phone/controller.dart';
import 'package:all_in_one/features/payment/mtn/check_phone/index.dart';
import 'package:all_in_one/features/payment/mtn/controller.dart';
import 'package:all_in_one/features/payment/mtn/index.dart';
import 'package:all_in_one/features/profile/about_us/about_us_page.dart';
import 'package:all_in_one/features/profile/edit_profile/edit_profile_page.dart';
import 'package:all_in_one/features/profile/my_subjects/my_subjects_page.dart';
import 'package:all_in_one/features/select_year/index.dart';
import 'package:all_in_one/features/slider_details/index.dart';
import 'package:all_in_one/features/splash_screen/splash_screen.dart';
import 'package:all_in_one/features/subjects/index.dart';

import '../features/auth/code_verification/index.dart';
import '../features/auth/login/index.dart';
import '../features/auth/signup/index.dart';
import '../features/nav_bar_pages/nav_bar/index.dart';
import '../features/profile/permisstions/permissions_page.dart';
import '../features/profile/privacy_policy/provisions_page.dart';

abstract class AppRouting {
  static GetPage unknownRoute =
      GetPage(name: Pages.login.value, page: () => LoginPage());
  static List<GetPage<dynamic>> routes() => [
        //############### AUTH ##################
        GetPage(name: Pages.splash.value, page: () => SplashScreen()),
        GetPage(
            name: Pages.alternativeDetails.value,
            page: () => AlternativeUiDetails()),

        GetPage(
            name: Pages.sliderDetails.value,
            page: () => SliderDetailsPage(
                  homeBannerModel: Get.arguments,
                )),
        GetPage(
            name: Pages.mtnCash.value,
            page: () => MtnCashPage(),
            binding: BindingsBuilder.put(() => MtnCashController())),
        GetPage(
            name: Pages.mtnCheckPhone.value,
            binding: BindingsBuilder.put(() => MtnCheckPhoneController()),
            page: () => MtnCheckPhonePage()),
        GetPage(name: Pages.subjectsPage.value, page: () => SubjectsPage()),

        GetPage(name: Pages.permissions.value, page: () => PermissionsPage()),
        GetPage(name: Pages.provisions.value, page: () => ProvisionsPage()),
        GetPage(name: Pages.login.value, page: () => LoginPage()),
        GetPage(name: Pages.signUp.value, page: () => SignUpPage()),
        GetPage(name: Pages.verify.value, page: () => CodeVerificationPage()),
        // //############# NavBar ###################
        GetPage(
            name: Pages.navBar.value,
            page: () => NavBarPage(),
            binding: BindingsBuilder.put(() => NavBarController())),
        GetPage(name: Pages.course.value, page: () => CoursePage()),
        GetPage(
            name: Pages.updatePassword.value, page: () => ChangePasswordPage()),

        GetPage(name: Pages.pdfReader.value, page: () => PdfReaderPage()),
        GetPage(name: Pages.selectYear.value, page: () => SelectYearPage()),

        GetPage(name: Pages.editProfile.value, page: () => EditProfilePage()),
        GetPage(name: Pages.mySubjects.value, page: () => MySubjectsPage()),
        GetPage(name: Pages.aboutUS.value, page: () => AboutUsPage()),
        GetPage(
            name: Pages.addTask.value, page: () => AddTaskPageBottomSheet()),
        GetPage(name: Pages.mainHome.value, page: () => AlternativeUi()),

        GetPage(
            name: Pages.onlinePayment.value, page: () => OnlinePaymentPage()),
      ];
}

enum Pages {
//====== auth ========
  splash,
  mainHome,
  permissions,
  provisions,
  login,
  subjectsPage,
  sliderDetails,
  signUp,
  verify,
  home,
  alternativeDetails,
  course,
  selectYear,
  pdfReader,
  editProfile,
  mySubjects,
  aboutUS,
  mtnCash,
  mtnCheckPhone,
  addTask,
  onlinePayment,
  updatePassword,
  navBar;

  String get value => '/${this.name}';
}

abstract class Nav {
  static Future? to(Pages page,
          {dynamic arguments,
          bool preventDuplicates = true,
          Map<String, String>? params}) =>
      Get.toNamed(page.value,
          parameters: params,
          arguments: arguments,
          preventDuplicates: preventDuplicates);

  static Future? replacement(Pages page,
          {dynamic arguments, Map<String, String>? params}) =>
      Get.offNamed(page.value, arguments: arguments, parameters: params);

  static Future? offAll(Pages page, {dynamic arguments}) =>
      Get.offAllNamed(page.value, arguments: arguments);

  static Future? offNamedUntil(Pages page, {dynamic arguments}) =>
      Get.offNamedUntil(page.value, (route) => false, arguments: arguments);
}
