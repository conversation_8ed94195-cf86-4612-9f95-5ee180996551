
import 'package:flutter/material.dart';
import 'package:all_in_one/core/style/style.dart';

abstract class AppDropDownInputThemes {

  static InputDecoration dropDownDecoration(String hint) {
    return InputDecoration(
      filled: true,
      focusColor: AppStyle.secondaryColor,
      fillColor: AppStyle.textFieldColor.withOpacity(0.2),
      hintText: hint,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
      errorStyle: TextStyle(fontSize: 13, color: AppStyle.redColor,fontWeight: FontWeight.w500),
      hintStyle: TextStyle(fontSize: 15, color: AppStyle.lightBlackColor,fontWeight: FontWeight.w500),
      disabledBorder: OutlineInputBorder(borderSide: BorderSide(width: 1, color: Colors.transparent)),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.primaryColor),
        borderRadius: BorderRadius.circular(10),
      ),
      enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.transparent),
          borderRadius: BorderRadius.circular(10)
      ),
      errorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppStyle.redColor),
          borderRadius: BorderRadius.circular(10)
      ),
      focusedErrorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppStyle.redColor),
          borderRadius: BorderRadius.circular(10)
      ),
    );
  }

}
