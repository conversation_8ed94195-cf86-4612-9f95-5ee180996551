import 'package:all_in_one/features/auth/signup/models/city_model.dart';
import 'package:all_in_one/features/auth/signup/models/class_model.dart';
import 'package:all_in_one/features/auth/signup/models/school_model.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_year_model.dart';

abstract class FakeRepo {
  static List<YearModel> years = [
    YearModel(id: 1, title: "السنة الأولى"),
    YearModel(id: 2, title: "السنة الثانية"),
    YearModel(id: 3, title: "السنة الثالثة"),
    YearModel(id: 4, title: "السنة الرابعة"),
    YearModel(id: 5, title: "السنة الخامسة"),
  ];

  static List<College> colleges = [
    College(id: 1, title: "كلية الهندسة"),
    College(id: 2, title: "كلية العلوم"),
    College(id: 3, title: "كلية الآداب"),
    College(id: 4, title: "كلية الطب"),
    College(id: 5, title: "كلية التجارة"),
    College(id: 6, title: "كلية الحقوق"),
  ];

  static List<SchoolModel> schools = [
    SchoolModel(id: 1, title: "مدرسة الثانوية العامة الأولى"),
    SchoolModel(id: 2, title: "مدرسة الثانوية العامة الثانية"),
    SchoolModel(id: 3, title: "مدرسة الثانوية العامة الثالثة"),
    SchoolModel(id: 4, title: "مدرسة المستقبل"),
    SchoolModel(id: 5, title: "مدرسة الأمل النموذجية"),
  ];

  static List<CityModel> cities = [
    CityModel(id: 1, name: "القاهرة"),
    CityModel(id: 2, name: "الإسكندرية"),
    CityModel(id: 3, name: "الجيزة"),
    CityModel(id: 4, name: "المنصورة"),
    CityModel(id: 5, name: "أسيوط"),
    CityModel(id: 6, name: "طنطا"),
  ];

  static List<Major> majors = [
    Major(
      id: 1,
      title: "علوم الحاسوب",
      college: colleges[0],
      image: "https://allinone.college/storage/majors/cs.jpg",
    ),
    Major(
      id: 2,
      title: "الهندسة الكهربائية",
      college: colleges[0],
      image: "https://allinone.college/storage/majors/electrical.jpg",
    ),
    Major(
      id: 3,
      title: "الفيزياء",
      college: colleges[1],
      image: "https://allinone.college/storage/majors/physics.jpg",
    ),
    Major(
      id: 4,
      title: "الكيمياء",
      college: colleges[1],
      image: "https://allinone.college/storage/majors/chemistry.jpg",
    ),
    Major(
      id: 5,
      title: "اللغة العربية",
      college: colleges[2],
      image: "https://allinone.college/storage/majors/arabic.jpg",
    ),
    Major(
      id: 6,
      title: "التاريخ",
      college: colleges[2],
      image: "https://allinone.college/storage/majors/history.jpg",
    ),
    Major(
      id: 7,
      title: "الطب البشري",
      college: colleges[3],
      image: "https://allinone.college/storage/majors/medicine.jpg",
    ),
    Major(
      id: 8,
      title: "طب الأسنان",
      college: colleges[3],
      image: "https://allinone.college/storage/majors/dentistry.jpg",
    ),
    Major(
      id: 9,
      title: "إدارة الأعمال",
      college: colleges[4],
      image: "https://allinone.college/storage/majors/business.jpg",
    ),
    Major(
      id: 10,
      title: "المحاسبة",
      college: colleges[4],
      image: "https://allinone.college/storage/majors/accounting.jpg",
    ),
    Major(
      id: 11,
      title: "القانون",
      college: colleges[5],
      image: "https://allinone.college/storage/majors/law.jpg",
    ),
  ];
}
