
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/features/course/contollers/subscription_controller.dart';

class SubscriptionStatusDialogBodyWidget extends StatelessWidget {
  final SubscriptionStatus status;
  const SubscriptionStatusDialogBodyWidget({required this.status});

  @override
  Widget build(BuildContext context) {
    SubscriptionController controller = Get.find();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
            alignment: Alignment.topRight,
            child: Padding(
              padding: const EdgeInsets.only(top: 5 , right: 5),
              child: GestureDetector(
                child: Assets.icons.cancelDialog.svg(),
                onTap: ()=>controller.closeDialog(),
              ),
            )
        ),
        Text(
          "Subscription Status".tr,
          textAlign: TextAlign.center,
          style: Get.textTheme.titleLarge!.copyWith(
              fontWeight: FontWeight.w700
          ),
        ),
        SizedBox(height: 20),
        Lottie.asset(
          status==SubscriptionStatus.Success?
            Assets.lottie.successLottie.path:
            Assets.lottie.faildLottie.path,
          width: 150,
          height: 150,
          delegates: LottieDelegates(
            values: [
              ValueDelegate.color(
                const ['**'],
                value: status==SubscriptionStatus.Success?AppStyle.greenColor:AppStyle.redColor,
              ),
            ],
          ),
        ),
        SizedBox(height: 20),
        Text(
          status==SubscriptionStatus.Success?
          'Subscription done successfully'.tr
              :'Subscription failed'.tr,
          style: Get.textTheme.titleSmall!.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        SizedBox(height: 10),
        SizedBox(height: 25),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: AppButton(
            height: 55,
            radius: 15,
            withLoading: false,
            margin: EdgeInsets.zero,
            fillColor: AppStyle.primaryColor,
            style: Get.textTheme.titleMedium!.copyWith(color: AppStyle.whiteColor,fontWeight: FontWeight.w700),
            text: 'Well'.tr,
            onTap: ()async => controller.closeDialog(),
          ),
        ),
        SizedBox(height: 12),
      ],
    );
  }
}
