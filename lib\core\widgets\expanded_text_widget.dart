// import 'package:flutter/material.dart';
// import 'package:flutter_html/flutter_html.dart';
// import 'package:get/get.dart';
//
// import '../style/style.dart';
//
// class ExpandedTextWidget extends StatelessWidget {
//   final String text;
//   final TextStyle textStyle;
//   final int expandedLength;
//   ExpandedTextWidget({required this.text,required this.textStyle,required this.expandedLength});
//
//   RxBool isExpanded = false.obs;
//
//   @override
//   Widget build(BuildContext context) {
//     return Visibility(
//       visible: text.isNotEmpty,
//       child: Obx(() => Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Html(
//                 data: text.length > expandedLength
//                     ? isExpanded.value
//                     ? text
//                     : text.substring(0, expandedLength) + '...'
//                     : text,
//               ),
//               const SizedBox(
//                 height: 4,
//               ),
//               Visibility(
//                 visible: text.length > expandedLength,
//                 child: InkWell(
//                   onTap: () {
//                     isExpanded.value = !isExpanded.value;
//                   },
//                   child: Row(
//                     children: [
//                       Icon(
//                         isExpanded.value
//                             ? Icons.keyboard_arrow_up_rounded
//                             : Icons.keyboard_arrow_down_rounded,
//                         color: AppStyle.primaryColor,
//                         size: 20,
//                       ),
//                       const SizedBox(
//                         width: 3,
//                       ),
//                       Text(
//                         isExpanded.value ? 'read less'.tr : 'read more'.tr,
//                         style: Theme.of(context)
//                             .textTheme
//                             .bodyMedium!
//                             .copyWith(color: AppStyle.primaryColor),
//                       ),
//                     ],
//                   ),
//                 ),
//               )
//             ],
//           )),
//     );
//   }
// }
