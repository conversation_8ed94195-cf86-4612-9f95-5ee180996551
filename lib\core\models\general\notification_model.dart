// import 'dart:convert';
// class NotificationModel {
//   late String body;
//   late String title;
//
//   NotificationModel(
//       {
//       required this.body,
//       required this.title
//       });
//
//   NotificationModel.fromJson(Map<String, dynamic> json) {
//     body = jsonDecode(json['body']);
//     title = json['title'];
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['body'] = this.body;
//     data['title'] = this.title;
//
//     return data;
//   }
// }
