import 'package:all_in_one/core/style/style.dart';
import 'package:country_pickers/country.dart';
import 'package:country_pickers/country_picker_dialog.dart';
import 'package:country_pickers/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


class CountryPickerHelper {
  Function(Country) onCountrySelected;
  CountryPickerHelper({required this.onCountrySelected});


  Widget buildDialogItem(Country country) => Row(
    mainAxisSize: MainAxisSize.min,
    children: <Widget>[
      CountryPickerUtils.getDefaultFlagImage(country),
      const SizedBox(width: 8.0),
      Text("+${country.phoneCode}"),
      const SizedBox(width: 8.0),
      Flexible(child: Text(country.name))
    ],
  );

  Future<void> showCountryPicker(BuildContext context) =>
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => Theme(
          data: ThemeData(dialogBackgroundColor: Colors.white),
          child: CountryPickerDialog(
            titlePadding: const EdgeInsets.all(8.0),
            searchCursorColor: AppStyle.primaryColor,
            searchInputDecoration: InputDecoration(hintText: 'search'.tr,hintStyle: Theme.of(context).textTheme.headlineSmall,contentPadding: const EdgeInsets.symmetric(vertical: 8,horizontal: 10)),
            isSearchable: true,
            title: Text('select your phone code'.tr),
            onValuePicked: (Country country) {
              onCountrySelected(country);
            },
            itemBuilder: buildDialogItem,
            priorityList: [
              CountryPickerUtils.getCountryByIsoCode('TR'),
            ],
          ),
        ),
      )
  ;
}
