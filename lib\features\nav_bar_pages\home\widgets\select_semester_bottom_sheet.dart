import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/courses_page.dart';
import 'package:all_in_one/features/subjects/index.dart';

class SelectSemesterBottomSheet extends StatelessWidget {
  final int yearId;
  final String majorId;
  const SelectSemesterBottomSheet(
      {super.key, required this.majorId, required this.yearId});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(20),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Select a semester'.tr,
            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                fontSize: 18,
                color: AppStyle.primaryColor,
                fontWeight: AppFontWeight.bold),
          ),
          SizedBox(
            height: 10,
          ),
          ...List.generate(
            2,
            (index) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: InkWell(
                onTap: () {
                  Nav.replacement(Pages.subjectsPage, params: {
                    "major_id": majorId.toString(),
                    "year_id": yearId.toString(),
                    "semester_id": (index + 1).toString(),
                  });
                },
                child: ListTile(
                  contentPadding: EdgeInsets.all(15),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(color: Colors.black, width: 1)),
                  minTileHeight: 5,
                  leading: Text(
                    index.isEven ? 'Semester One'.tr : 'Semester Two'.tr,
                    style: Get.textTheme.titleLarge!
                        .copyWith(color: AppStyle.primaryColor),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
