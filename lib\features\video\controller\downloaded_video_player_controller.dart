import 'dart:developer';
import 'dart:io';
import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:video_player/video_player.dart';
import 'dart:io' as io;

class DownloadedVideoPlayerController extends GetxController {
  AppController appController = Get.find();

  late VideoPlayerController videoController;
  late ChewieController chewieController;
  RxBool isVideoInitd = false.obs;
  ValueNotifier<bool> videoInitial = ValueNotifier<bool>(false);

  Future<void> initialVideo(BuildContext context, String videoName) async {
    io.File video = File('${appController.videosDirectoryPath}/video.mp4');
    videoController = VideoPlayerController.file(video);
    await videoController
        .initialize()
        .timeout(const Duration(seconds: 200), onTimeout: () {})
        .onError((error, stackTrace) {
      videoInitial.value = true;
      log("video error", error: error, stackTrace: stackTrace);
    });
    chewieController = ChewieController(
      videoPlayerController: videoController,
      autoInitialize: true,
      aspectRatio: videoController.value.aspectRatio,
      customControls: CupertinoControls(
        backgroundColor: Colors.grey.withOpacity(0.3),
        iconColor: Colors.white,
      ),
      allowedScreenSleep: false,
      // zoomAndPan: true,
      allowFullScreen: false,
      allowPlaybackSpeedChanging: true,
      playbackSpeeds: [0.25, 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2],
      // maxScale: 3.0,
    );
    videoInitial.value = true;
    isVideoInitd(true);
  }

  RxBool _isFullScreen = false.obs;
  bool get isFullScreen => this._isFullScreen.value;
  set isFullScreen(value) => this._isFullScreen.value = value;

  void enterFullScreen() {
    isFullScreen = true;
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  void exitFullScreen() {
    isFullScreen = false;
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  }

  @override
  void onClose() {
    videoController.dispose();
    chewieController.dispose();
    videoInitial.dispose();
    super.onClose();
  }
}
