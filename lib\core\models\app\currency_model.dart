
class CurrencyModel {
  String name;
  String code;
  String symbol;
  double exchangeRate;
  bool currencyDefault;

  CurrencyModel({required this.name,required this.currencyDefault,required this.code, required this.symbol, required this.exchangeRate});

  factory CurrencyModel.fromJson(Map<String, dynamic> json) => CurrencyModel(
    name: json['name'],
    code: json['code'],
    symbol: json['symbol'],
    exchangeRate: double.tryParse(json['exchange_rate'].toString())??0.0,
    currencyDefault: json['default']
  );


  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this.name;
    data['code'] = this.code;
    data['symbol'] = this.symbol;
    data['exchange_rate'] = this.exchangeRate;
    data['default'] = this.currencyDefault;
    return data;
  }
}
