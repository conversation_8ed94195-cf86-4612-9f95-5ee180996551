
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_subject_model.dart';

class MySubjectsController extends GetxController{
  DataController dataController = Get.find();
  AppController appController = Get.find();


  List<HomeSubjectModel> subjects=[];
  RxBool _dataLoading = true.obs;
  get dataLoading  => this._dataLoading.value;
  set dataLoading (value) => this._dataLoading.value = value;

  void loadMySubjectsData() async {
    dataLoading =true;
    ResponseModel response;
    response = await dataController.getData(
      url: API.mySubjectApi,
    );
    if(response.success){
      response.data.forEach((element) => subjects.add(HomeSubjectModel.fromJson(element)));
      dataLoading =false;
    }else{
     if(response.status == 403||response.status == 401){
        appController.removeUserData();
        appController.showToast(Get.context!, message: response.message,status: ToastStatus.fail);
        Nav.offAll(Pages.login);
      }
    }
  }


  @override
  void onInit() async {
    super.onInit();
    !await appController.internetChecker();
    loadMySubjectsData();
  }

}