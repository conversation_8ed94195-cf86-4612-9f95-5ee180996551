
class HomeNewModel {
  String title;
  String? text;
  String? link;
  String media;
  List<String> mediaList;
  NewReleated? releated;

  HomeNewModel({required this.title,required this.text, required this.media,required this.mediaList,this.releated,this.link});

  factory HomeNewModel.fromJson(Map<String, dynamic> json) => HomeNewModel(
    title: json['title'],
    text: json['text'],
    link: json['link'],
    media: json['media']['original_url'],
    mediaList: json["media_list"]==null?[]:List<String>.from(json["media_list"].map((x) => x['original_url'])),
      releated: json['releated']!=null? NewReleated.fromJson(
        {
          "id": 1,
          "title": "علوم",
          "teacher_id": 1,
        }
    ): null
  );

}

class NewReleated{
  int id;
  String title;
  int teacherId;

  NewReleated({
    required this.id,
    required this.title,
    required this.teacherId
  });

  factory NewReleated.fromJson(Map<String, dynamic> json) => NewReleated(
      title: json['title'],
      id: json['id'],
      teacherId: json['teacher_id'],
  );
}

// {
// "id": 1,
// "title": "علوم",
// "teacher_id": 1,
// "year_id": 3,
// "price": 0,
// "created_at": "2023-12-18T15:14:47.000000Z",
// "updated_at": "2023-12-18T15:14:47.000000Z",
// "deleted_at": null,
// "order_item": 0,
// "sections_cnt": 2
// }