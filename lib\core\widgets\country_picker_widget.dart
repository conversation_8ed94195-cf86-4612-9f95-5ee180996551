import 'package:country_pickers/country_pickers.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:flutter/material.dart';
import 'package:country_pickers/country.dart';

import '../utils/country_picker_helper.dart';

class CountryPickerWidget extends StatelessWidget {
  final Function(Country) onChanged;
  Country? selectedCountry;
  final bool hasError;
  final double height;
  CountryPickerWidget(
      {required this.onChanged,
      required this.selectedCountry,
      this.hasError=false,
      required this.height});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(15),
      onTap: () async {
        await CountryPickerHelper(
          onCountrySelected: (country) {
            // this.country = country;
            // this.countryCode.value = country.phoneCode;
            onChanged(country);
          },
        ).showCountryPicker(context);
      },
      child: Container(
        height: height,
        decoration: BoxDecoration(
            border: Border.all(color: hasError?AppStyle.redColor:AppStyle.lightGreyColor),
            borderRadius: BorderRadius.circular(5)),
        padding: EdgeInsets.symmetric(horizontal: 16,),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            selectedCountry == null
                ? Assets.icons.earthPlanetIcon.svg(color: AppStyle.lightBlackColor,width: 25,height: 25)
                : selectedCountry!.isoCode.isEmpty?SizedBox():CircleAvatar(
                  radius: 15,
                  backgroundColor: Colors.transparent,
                  child: CountryPickerUtils.getDefaultFlagImage(selectedCountry!),
                ),
            const SizedBox(
              width: 4,
            ),
            Text(
              '+' + (selectedCountry!=null?selectedCountry!.phoneCode.isNotEmpty?selectedCountry!.phoneCode:'00':'00'),
              style: Theme.of(context)
                  .textTheme
                  .bodyLarge!
                  .copyWith(color: AppStyle.blackColor),
            ),
          ],
        ),
      ),
    );
  }
}