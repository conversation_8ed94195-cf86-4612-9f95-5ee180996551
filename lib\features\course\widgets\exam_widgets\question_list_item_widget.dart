import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/image_zoom.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/course/contollers/exam_controller.dart';
import 'package:all_in_one/features/course/models/exam/question_model.dart';
import 'package:vibration/vibration.dart';
import '../../models/exam/choice_model.dart';

const _additionalSpaceToScroll = 20;

class QuestionListItemWidget extends StatefulWidget {
  QuestionListItemWidget({
    Key? key,
    required this.questionIndex,
    required this.question,
    required this.successPlayer,
    required this.wrongPlayer,
    required this.totalMark,
    required this.onChoiceSelected,
  }) : super(key: key);

  final int questionIndex;
  final int totalMark;
  final QuestionModel question;
  final AudioPlayer successPlayer;
  final AudioPlayer wrongPlayer;
  final void Function(int index) onChoiceSelected;
  @override
  _QuestionListItemWidgetState createState() => _QuestionListItemWidgetState();
}

class _QuestionListItemWidgetState extends State<QuestionListItemWidget> {

  ExamController controller = Get.find();
  double height = 0;
  ValueNotifier<bool> showCorrectAnswer =ValueNotifier<bool>(false);


  @override
  void initState() {

    WidgetsBinding.instance.endOfFrame.then(
          (_) {
        if (mounted) {
          height = context.size?.height ?? 0.0;
        }
      },
    );

    for(int i=0;i< widget.question.choices.length;i++){
      if(widget.question.choices[i].correct){
        controller.questionCorrectChoiceIndex[widget.questionIndex] = i;
        break;
      }
    }


    super.initState();
  }


  @override
  Widget build(BuildContext context) {

    return ValueListenableBuilder(
        valueListenable: controller.showExamResult,
        builder: (context,bool showExamResult,_){
          if(showExamResult)
            showCorrectAnswer.value = true;
        return ValueListenableBuilder(
            valueListenable: showCorrectAnswer,
            builder: (context,bool showCorrect,_) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 16,vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        blurRadius: 4,
                        spreadRadius: 3,
                        offset: const Offset(-2, 1) // changes position of shadow
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: 10),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Text(
                            widget.question.question,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: AppStyle.primaryColor,
                                fontWeight: FontWeight.bold
                            ),
                          ),
                        ),
                        const SizedBox(width: 20,),
                        Text(
                          widget.question.mark.toString()+'/'+widget.totalMark.toString(),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: AppStyle.primaryColor,
                              fontWeight: FontWeight.bold
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 14),
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: widget.question.choices.length,
                      padding: EdgeInsets.zero,
                      itemBuilder: (context, index) {
                        return Obx(
                                ()=> choiceListItemWidget(
                              choiceModel: widget.question.choices[index],
                              isSelected: controller.questionSelectedIndex[widget.questionIndex] == index,
                              showCorrectAnswer: showCorrect,
                              onSelected: showCorrect
                                  ? null
                                  : () async{
                                controller.questionSelectedIndex[widget.questionIndex] = index;
                                widget.onChoiceSelected(index);
                              },
                            )
                        );
                      },
                      separatorBuilder: (context, index) {
                        return const SizedBox(height: 15);
                      },
                    ),
                    const SizedBox(height: 15,),
                    Row(
                      children: [
                        Visibility(
                          visible: !showCorrect,
                          child: Obx(
                              ()=> InkWell(
                                onTap: controller.questionSelectedIndex[widget.questionIndex]!=-1?(){
                                  showCorrectAnswer.value = true;
                                  if(controller.questionSelectedIndex[widget.questionIndex] != controller.questionCorrectChoiceIndex[widget.questionIndex]){
                                    widget.wrongPlayer.play();
                                    Vibration.vibrate();
                                    widget.wrongPlayer.seek(const Duration(seconds: 0));
                                  }else{
                                    widget.successPlayer.play();
                                    widget.successPlayer.seek(const Duration(seconds: 0));
                                  }
                                  controller.scrollController.animateTo(
                                    controller.scrollController.position.pixels +
                                        height +
                                        _additionalSpaceToScroll,
                                    duration: const Duration(milliseconds: 300),
                                    curve: Curves.linear,
                                  );
                                }:()=> controller.appController.showToast(
                                    context,
                                    message: 'Choose a choice to correct'.tr,
                                    status: ToastStatus.warning
                                ),
                                child: Row(
                                  children: [
                                    Text(
                                      'Correct Answer'.tr,
                                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                          color: AppStyle.primaryColor
                                      ),
                                    ),
                                    Padding(
                                      padding: EdgeInsets.all(5),
                                      child: Assets.icons.correctionIcon.image(color: AppStyle.primaryColor,width: 25,height: 25),
                                    ),
                                  ],
                                ),
                              ),
                          )
                        ),
                        Visibility(
                          visible: showCorrect,
                          child: Text(
                            'Mark due'.tr+': ${controller.questionSelectedIndex[widget.questionIndex] == controller.questionCorrectChoiceIndex[widget.questionIndex]
                                ?widget.question.mark:0}/${widget.question.mark}',
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                color: controller.questionSelectedIndex[widget.questionIndex] == controller.questionCorrectChoiceIndex[widget.questionIndex]?
                                Colors.green: Colors.red
                            ),
                          ),
                        ),
                        const Spacer(),
                        Visibility(
                          visible: showCorrect,
                          child: IconButton(
                            padding: EdgeInsets.all(5),
                            onPressed: (){
                              ImageZoom().zoom(widget.question.answerImage);
                            },
                            icon: Assets.icons.infoIcon.svg(),
                          ),
                        ),
                      ],
                    ),
                    // Visibility(
                    //     visible: showCorrect&&widget.question.rightAnswerExplanation!=null,
                    //     child: const SizedBox(height: 5,),
                    // ),
                    Visibility(
                      visible: showCorrect&&widget.question.rightAnswerExplanation!=null,
                      child: Text(
                        widget.question.rightAnswerExplanation??'',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: controller.questionSelectedIndex[widget.questionIndex] == controller.questionCorrectChoiceIndex[widget.questionIndex]?
                            Colors.green: Colors.red
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }
        );
      }
    );
  }
}



class choiceListItemWidget extends StatelessWidget {
  const choiceListItemWidget({
    Key? key,
    required this.choiceModel,
    required this.isSelected,
    required this.showCorrectAnswer,
    this.onSelected,
  }) : super(key: key);

  final ChoiceModel choiceModel;
  final bool isSelected;
  final bool showCorrectAnswer;
  final VoidCallback? onSelected;

  @override
  Widget build(BuildContext context) {
    late final Color textColor;
    late final Color circleBorderColor;
    late final Color circleColor;

    if (showCorrectAnswer && choiceModel.correct) {
      textColor = Colors.green;
      circleBorderColor = Colors.green;
      circleColor = Colors.green;
    } else if (showCorrectAnswer && !choiceModel.correct && isSelected) {
      textColor = Colors.red;
      circleBorderColor = Colors.red;
      circleColor = Colors.red;
    } else if (isSelected) {
      textColor = AppStyle.primaryColor;
      circleBorderColor = AppStyle.primaryColor;
      circleColor = AppStyle.primaryColor;
    } else {
      textColor = AppStyle.blackTextColor;
      circleBorderColor = Colors.black;
      circleColor = Colors.white;
    }
    return Material(
      type: MaterialType.transparency,
      child: InkWell(
        onTap: onSelected,
        child: Row(
          children: [
            SizedBox(
              width: 25,
              height: 25,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Positioned.fill(
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 200),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: circleBorderColor,
                          width: 1.5,
                        ),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                  AnimatedContainer(
                    width: 17,
                    height: 17,
                    curve: Curves.easeOut,
                    duration: const Duration(milliseconds: 850),
                    decoration: BoxDecoration(
                      color: circleColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                choiceModel.answer,
                maxLines: 2,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: textColor
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}