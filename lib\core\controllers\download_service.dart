// import 'dart:developer';
// import 'dart:isolate';
// import 'dart:ui';

// import 'package:flutter_downloader/flutter_downloader.dart';
// import 'package:get/get.dart';
// import 'package:get/get_rx/src/rx_types/rx_types.dart';
// import 'package:all_in_one/core/controllers/app_controller.dart';
// import 'package:all_in_one/core/utils/encription_helper.dart';
// import 'package:all_in_one/features/video/controller/network_video_player_controller.dart';

// abstract class DownLoadService {
//   static ReceivePort _port = ReceivePort();

//   static initDownLoad(String taskId) async {
//     bool tmp = IsolateNameServer.registerPortWithName(
//         _port.sendPort, 'downloader_$taskId');
//     log(tmp ? "true" : "false", name: "registration $taskId");
//     FlutterDownloader.registerCallback(downloadCallback);
//   }

//   static Future<String?> downLoadFile(
//       {required String url,
//       required String videoName,
//       required Function(int) onProgress,
//       Function()? onComplete}) async {
//     AppController appController = Get.find();
//     String? taskId = await FlutterDownloader.enqueue(
//       url: url,
//       fileName: "$videoName.mp4",
//       timeout: 80000,
//       savedDir: appController.videosDirectoryPath,
//       showNotification: true,
//       requiresStorageNotLow: true,
//       openFileFromNotification: true,
//     );
//     DownLoadService.initDownLoad(taskId!);
//     log("big work?");

//     _port.listen((data) {
//       log("task id ${data[0]}");
//       DownloadTaskStatus status = DownloadTaskStatus.fromInt(data[1]);
//       if (status == DownloadTaskStatus.complete) {
//         encryptFile(appController.videosDirectoryPath, videoName);
//         onComplete?.call();
//       } else if (status == DownloadTaskStatus.failed ||
//           status == DownloadTaskStatus.undefined) {
//         FlutterDownloader.retry(taskId: data[0], timeout: 50000);

//         // FlutterDownloader.retry(taskId: data[0]);
//       } else if (status == DownloadTaskStatus.canceled) {
//         if (Get.isRegistered<NetworkVideoPlayerController>()) {
//           NetworkVideoPlayerController networkVideoPlayerController =
//               Get.find();
//           networkVideoPlayerController.downloadVideoLoading = false;
//         }
//       }
//       int progress = data[2];
//       onProgress(progress);
//     });

//     return taskId;
//   }

//   static cancelDownload(String fileName) async {
//     FlutterDownloader.cancel(taskId: taskId);
//     List<DownloadTask>? tasks = await FlutterDownloader.loadTasks();
//     tasks.map((e) => e.)
//   }

//   @pragma('vm:entry-point')
//   static void downloadCallback(String id, int status, int progress) {
//     final SendPort? send = IsolateNameServer.lookupPortByName('downloader_$id');
//     log(send != null ? "that right $id" : "oops $id");
//     send?.send([id, status, progress]);
//   }
// }
