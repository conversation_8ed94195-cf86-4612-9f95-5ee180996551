import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';

class AppBarWidget extends StatelessWidget {
  const AppBarWidget({
    Key? key,
    required this.centerWidget,
    required this.leadingIcon,
    required this.trailingIcon,
    required this.leadingOnTap,
    required this.trailingOnTap,
  }) : super(key: key);
  final Widget centerWidget;
  final Widget leadingIcon;
  final Widget trailingIcon;
  final VoidCallback? leadingOnTap;
  final VoidCallback trailingOnTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: AppStyle.primaryColor,
      padding: EdgeInsets.only(
          top: Get.mediaQuery.viewPadding.top + 15,
          left: 15,
          right: 15,
          bottom: 15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          InkWell(
            onTap: leadingOnTap,
            child: Padding(
              padding: const EdgeInsets.all(4),
              child: leadingIcon,
            ),
          ),
          const SizedBox(width: 6),
          centerWidget,
          const SizedBox(width: 6),
          InkWell(
            onTap: trailingOnTap,
            child: Padding(
              padding: const EdgeInsets.all(4),
              child: trailingIcon,
            ),
          ),
        ],
      ),
    );
  }
}
