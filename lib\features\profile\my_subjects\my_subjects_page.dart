import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/app_animation/item_animation.dart';
import 'package:all_in_one/core/widgets/app_grid_view.dart';
import 'package:all_in_one/core/widgets/no_data_widget.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';
import 'my_subjects_controller.dart';
import 'widgets/subject_grid_item_shimmer.dart';
import 'widgets/subject_grid_item_widget.dart';

class MySubjectsPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    MySubjectsController controller = Get.put(MySubjectsController());
    return Scaffold(
      body: Column(
        children: [
          AppBarWidget(
            centerWidget: Text(
              'My Subscriptions'.tr,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
            ),
            trailingIcon: SizedBox(
              width: 35,
              height: 35,
            ),
            leadingIcon: Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            trailingOnTap: () {},
            leadingOnTap: () {
              Get.back();
            },
          ),
          const SizedBox(
            height: 10,
          ),
          Expanded(
            child: Obx(
                ()=> (!controller.dataLoading&&controller.subjects.isEmpty)?
                NoDataWidget(
                  imageWidth: 200,
                  imageHeight: 200,
                ):
                AppGridView(
                  itemsCount: controller.dataLoading?6:controller.subjects.length,
                  mainAxisSpacing: 12,
                  crossAxisSpacing: 12,
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  itemBuilder: (BuildContext context, int index) {
                    return ItemAnimation(
                      index: index,
                      elementCount: controller.dataLoading?8:controller.subjects.length,
                      child: controller.dataLoading? const SubjectGridItemShimmer():
                      SubjectGridItemWidget(
                        id: controller.subjects[index].subjectId,
                        subjectName: controller.subjects[index].title,
                        teacherName: controller.subjects[index].teacherName,
                        media: controller.subjects[index].media,
                        onTap: ()=> Nav.to(Pages.course,arguments: {
                          'title': controller.subjects[index].title,
                          'id': controller.subjects[index].subjectId,
                          'from_home': false
                        }),
                      ),
                    );
                  },
                ),
            )
          ),
        ],
      ),
    );
  }
}
