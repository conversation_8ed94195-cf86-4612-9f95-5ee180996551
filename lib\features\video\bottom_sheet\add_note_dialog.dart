import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/validator.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/features/course/contollers/note_controller.dart';


Future<void> ShowAddNoteDialog(BuildContext context) async {
  await showDialog(
    context: context,
    barrierDismissible: true,
    builder: (_) {
      return AddNoteDialog();
    },
  );
}

class AddNoteDialog extends StatelessWidget {
  const AddNoteDialog();

  @override
  Widget build(BuildContext context) {
    NoteController controller = Get.find();
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(10)),
      ),
      content: Form(
        key: controller.formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Align(
                alignment: AlignmentDirectional.topEnd,
                child: Padding(
                  padding: const EdgeInsets.only(top: 5 , right: 5),
                  child: GestureDetector(
                    child: Assets.icons.cancelDialog.svg(),
                    onTap: ()=>Get.back(),
                  ),
                )
            ),
            Text(
              'Add note'.tr,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppStyle.blackColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Text(
              'Add a note to the lesson'.tr,
               style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppStyle.lightBlackColor,
                  fontWeight: FontWeight.bold,
               ),
            ),
            const SizedBox(
              height: 15,
            ),
            TextFormField(
              controller: controller.comment,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppStyle.lightBlackColor),
              validator: Validator.notNullValidation,
              keyboardType: TextInputType.text,
              decoration: InputDecoration(
                  hintText: "Note".tr,
              ),
            ),
            const SizedBox(height: 15,),
            AppButton(
                text: 'Add'.tr,
                height: 58,
                radius: 10,
                withLoading: true,
                margin: EdgeInsets.zero,
                style: Get.textTheme.titleMedium!.copyWith(color: AppStyle.whiteColor,fontWeight: FontWeight.bold),
                onTap: ()async=> controller.addNote(context)
            ),
          ],
        ),
      ),
    );
  }
}
