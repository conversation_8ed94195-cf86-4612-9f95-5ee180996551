import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:secure_application/secure_application.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/features/course/models/exam/choice_model.dart';
import 'package:all_in_one/features/course/models/exam/exam_model.dart';
import 'package:all_in_one/features/course/models/exam/question_model.dart';


class ExamController extends GetxController {
  late AudioPlayer successPlayer;
  late AudioPlayer wrongPlayer;
  String successAsset = "assets/voices/correct_answer.mp3";
  String wrongAsset = "assets/voices/wrong_answer.mp3";

  late int sectionId;

  RxBool _dataLoading = true.obs;
  get dataLoading  => this._dataLoading.value;
  set dataLoading (value) => this._dataLoading.value = value;

  RxBool _hasError = false.obs;
  get hasError  => this._hasError.value;
  set hasError (value) => this._hasError.value = value;

  final ScrollController scrollController = ScrollController();
  DataController dataController = Get.find();
  AppController appController = Get.find();
  late final SecureApplicationController? lockController;

  int fullMark = 0;
  int deservedMark = 0;
  int correctAnswersNumber = 0;
  int wrongAnswersNumber = 0;

  late ExamModel exam;



  Map<int, ChoiceModel> selectedAnswerForQuestionIdMap = {};
  RxList<int> questionSelectedIndex = RxList([]);
  List<int> questionCorrectChoiceIndex = [];
  ValueNotifier<bool> showCorrectionButton = ValueNotifier(false);
  ValueNotifier<bool> showExamResult = ValueNotifier(false);



  Future<void> setSelectedAnswer({
    required BuildContext context,
    required int questionId,
    required ChoiceModel choice,
  }) async {
    selectedAnswerForQuestionIdMap[questionId] = choice;
    if (selectedAnswerForQuestionIdMap.length == exam.questions.length)
      showCorrectionButton.value = true;
  }


  calcExamResult(){
    if (selectedAnswerForQuestionIdMap.length == exam.questions.length) {
      showExamResult.value =true;
      showCorrectionButton.value =false;
      for (var oneQuestion in exam.questions) {
        fullMark += oneQuestion.mark;
        final selectedAnswer = selectedAnswerForQuestionIdMap[oneQuestion.id];
        if (selectedAnswer != null) {
          if (selectedAnswer.correct) {
            deservedMark += oneQuestion.mark;
            correctAnswersNumber++;
          } else {
            wrongAnswersNumber++;
          }
        }
      }

      final scrollDuration =
      Duration(milliseconds: exam.questions.length * 150);

      scrollController.animateTo(
        scrollController.position.minScrollExtent,
        duration: scrollDuration,
        curve: Curves.easeInOut,
      );
    }
  }

  setUpVariables() async {
    for(int i=0;i< exam.questions.length;i++){
      questionCorrectChoiceIndex.add(-1);
      questionSelectedIndex.add(-1);
    }
  }

  setUpPlayer() async {
    successPlayer = AudioPlayer();
    wrongPlayer = AudioPlayer();
    await successPlayer.setAsset(successAsset);
    await wrongPlayer.setAsset(wrongAsset);
  }

  getExamQuestions()async{
    dataLoading = true;

    ResponseModel response;
    response = await dataController.getData(
        url: API.examApi(sectionId),
    );
    if(response.success){
      exam = ExamModel.fromJson(response.data[0]);
      setUpVariables();
    }else{
      hasError = true;
    }
    dataLoading = false;
  }


  @override
  void onInit() {
    lockController = SecureApplicationProvider.of(Get.context!, listen: false);
    lockController?.secure();
    sectionId = Get.arguments['id'];
    getExamQuestions();
    setUpPlayer();
    super.onInit();
  }


  @override
  void onClose() {
    lockController?.open();
    scrollController.dispose();
    super.onClose();
  }


}
