name: youtube_player_flutter
description: Flutter plugin for playing or streaming inline YouTube videos using the official iFrame player API. This plugin supports both Android and iOS.
version: 8.1.2
repository: https://github.com/sarbagyastha/youtube_player_flutter
homepage: https://github.com/sarbagyastha/youtube_player_flutter/tree/master/packages/youtube_player_flutter

environment:
  sdk: ">=3.3.4 <4.0.0"
  flutter: ">=3.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_inappwebview: ^6.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  mocktail: ^0.3.0
  zoom_widget: ^2.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/playback_speed.png
    - assets/app_name.png
    - assets/setting_icon.png
