import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/validator.dart';
import 'package:all_in_one/core/widgets/app_date_picker.dart';
import 'package:all_in_one/core/widgets/app_drop_down.dart';
import 'package:all_in_one/core/widgets/app_drop_down_search.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/cached_image_widget.dart';
import 'package:all_in_one/core/widgets/date_picker.dart';
import 'package:all_in_one/core/widgets/image.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';

import 'edit_profile_controller.dart';

class EditProfilePage extends StatelessWidget {
  const EditProfilePage();

  // bool checkIfUserTester(){
  //   var service_end_date = '2024-01-27';
  //   DateTime endDate = DateTime.parse(service_end_date);
  //   DateTime now = DateTime.now();
  //   if(endDate.isAfter(now)){
  //     return true;
  //   }
  //   return false;
  // }

  @override
  Widget build(BuildContext context) {
    EditProfileController controller = Get.put(EditProfileController());
    return Scaffold(
      body: Column(
        children: [
          AppBarWidget(
            centerWidget: Text(
              'Edit Profile'.tr,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
            ),
            trailingIcon: SizedBox(
              width: 35,
              height: 35,
            ),
            leadingIcon: Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            trailingOnTap: () {},
            leadingOnTap: () => Get.back(),
          ),
          const SizedBox(
            height: 35,
          ),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  Stack(
                    alignment: Alignment.center,
                    children: [
                      Obx(() =>
                          controller.imagePath.substring(0, 5) == 'https' ||
                                  controller.imagePath.substring(0, 4) == 'http'
                              ? CachedImageWidget(
                                  imageUrl: controller.imagePath,
                                  width: 160,
                                  height: 160,
                                  isCircular: true,
                                  fit: BoxFit.cover,
                                )
                              : AppImage(
                                  path: controller.imagePath,
                                  type: ImageType.File,
                                  width: 160,
                                  height: 160,
                                  boxShape: BoxShape.circle,
                                  fit: BoxFit.cover,
                                )),
                      PositionedDirectional(
                        bottom: 10,
                        start: 110,
                        end: 0,
                        child: InkWell(
                          onTap: () async => await controller.imgFromGallery(),
                          child: CircleAvatar(
                            radius: 17,
                            backgroundColor: AppStyle.primaryColor,
                            child: Icon(
                              Icons.camera_alt_outlined,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  Form(
                    key: controller.formKey,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextFormField(
                          controller: controller.name,
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium
                              ?.copyWith(color: AppStyle.lightBlackColor),
                          validator: Validator.notNullValidation,
                          decoration: InputDecoration(
                            hintText: "Full Name".tr,
                          ),
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        InkWell(
                          onTap: () => controller.appController.showToast(
                              context,
                              message: 'You Can\'t Edit This Value'.tr),
                          child: TextFormField(
                            controller: controller.phone,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(color: AppStyle.lightBlackColor),
                            validator: Validator.notNullValidation,
                            enabled: false,
                            keyboardType: TextInputType.phone,
                            decoration: InputDecoration(
                              hintText: "Phone".tr,
                            ),
                          ),
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        Obx(
                          () => controller.schoolsLoading
                              ? SkeletonWidget(
                                  height: 60,
                                )
                              : AppDropDownSearchWidget(
                                  hint: "select a university".tr,
                                  items: controller.schools
                                      .map((e) => e.title)
                                      .toList(),
                                  chosenValue: controller.schoolController.text,
                                  selectedIndex: (index) {
                                    controller.schoolId =
                                        controller.schools[index].id;
                                  },
                                  isChanged: true,
                                  validator: Validator.notNullValidation,
                                ),
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        Obx(
                          () => controller.majorsLoading
                              ? SkeletonWidget(
                                  height: 60,
                                )
                              : AppDropDownSearchWidget(
                                  hint: "select a major".tr,
                                  items: controller.majors
                                      .map((e) => e.title)
                                      .toList(),
                                  chosenValue: controller.majorController.text,
                                  selectedIndex: (index) {
                                    controller.majorId =
                                        controller.majors[index].id;
                                    controller.loadYearsData();
                                  },
                                  isChanged: true,
                                  validator: Validator.notNullValidation,
                                ),
                        ),
                        const SizedBox(
                          height: 12,
                        ),
                        Obx(
                          () => controller.yearsLoading
                              ? SkeletonWidget(
                                  height: 60,
                                )
                              : AppDropDownWidget(
                                  listValue: controller.years
                                      .map((e) => e.title)
                                      .toList(),
                                  hintText: 'Classroom'.tr,
                                  selectedIndex: (index) {
                                    controller.yearsId =
                                        controller.years[index].id;
                                  },
                                  validator: Validator.notNullValidation,
                                ),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        AppButton(
                          text: 'Save'.tr,
                          height: 60,
                          radius: 10,
                          withLoading: true,
                          margin: EdgeInsets.zero,
                          style: Get.textTheme.titleMedium!.copyWith(
                              color: AppStyle.whiteColor,
                              fontWeight: FontWeight.bold),
                          onTap: () async => controller.upDateProfile(context),
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
