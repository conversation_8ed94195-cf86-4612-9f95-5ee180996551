import 'package:blur/blur.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/widgets/cached_image_widget.dart';
import 'zoom_widget.dart';

class ImageZoom {


  zoom(String img) {
    Get.dialog(
      WillPopScope(
      onWillPop: () async {
        Get.back();
        return false;
      },
      child: Stack(
        children: [
          Blur(
            blurColor: Colors.black,
            child: SizedBox(),
          ),
          Center(
            child: GestureZoomBox(
              maxScale: 4,
              duration: const Duration(milliseconds: 200),
              child: CachedImageWidget(
                imageUrl: img,
              ),
            ),
          ),
          PositionedDirectional(
            top: 20,
            end: 20,
            child: TextButton(
              child: Text("Close".tr,style: TextStyle(color: Colors.white,fontSize: 18),),
              onPressed: () => Get.back(),
            ),
          ),
        ],
      ),
    ),
    );
  }

}
