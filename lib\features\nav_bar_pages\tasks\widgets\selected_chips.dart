
import 'package:flutter/material.dart';
import 'package:all_in_one/core/style/style.dart';

class SelectedChips extends StatefulWidget {
  const SelectedChips(
      {super.key, required this.onSelectedItem, required this.items});

  final List<String> items;
  final Function(int index) onSelectedItem;

  @override
  State<SelectedChips> createState() => _SelectedChipsState();
}

class _SelectedChipsState extends State<SelectedChips> {
  late int currentIndex;
  @override
  void initState() {
    super.initState();
    currentIndex = 0;
  }

  @override
  Widget build(BuildContext context) {
    return Align(
        alignment: Alignment.center,
        child: Directionality(
          textDirection:
              TextDirection.rtl, // Set text direction to right-to-left
          child: ListView.builder(
            //  reverse: true,
            scrollDirection: Axis.horizontal,
            itemCount: widget.items.length,
            itemBuilder: (context, index) => Padding(
              padding: EdgeInsets.symmetric(horizontal: 5),
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    currentIndex = index;
                    widget.onSelectedItem(index);
                  });
                },
                child: Chip(
                  backgroundColor: currentIndex == index
                      ? AppStyle.primaryColor
                      : AppStyle.lightGreyColor,
                  shape: const RoundedRectangleBorder(
                      side: BorderSide(
                        color: Colors.white,
                      ),
                      borderRadius: BorderRadius.all(Radius.circular(50))),
                  label: Text(widget.items[index],
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: currentIndex == index
                                ? Colors.white
                                : Colors.black,
                          )),
                ),
              ),
            ),
          ),
        ));
  }
}
