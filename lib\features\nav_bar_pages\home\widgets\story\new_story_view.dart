// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:story_view/story_view.dart';
// import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_controller.dart';
// import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_new_model.dart';
// import 'package:all_in_one/features/nav_bar_pages/home/<USER>';

// class NewStoryViewWidget extends StatelessWidget {
//   const NewStoryViewWidget({
//     super.key,
//   });

//   @override
//   Widget build(BuildContext context) {
//     final StoryController controller = StoryController();
//     return StoryView(
//       onVerticalSwipeComplete: _jandleVerticalComplete,
//       indicatorColor: Colors.white,
//       indicatorForegroundColor: Colors.black45,
//       storyItems: _getItems(controller),
//       onStoryShow: (s) {},
//       onComplete: () => _handleOnComplete(),
//       progressPosition: ProgressPosition.top,
//       repeat: false,
//       controller: controller,
//     );
//   }

//   void _jandleVerticalComplete(Direction? direction) {
//     if (direction == Direction.down) {
//       Get.back();
//     }
//   }

//   void _handleOnComplete() {
//     HomePageController homeController = Get.find();

//     if (homeController.currentIndex == (homeController.news.length - 1)) {
//       Get.back();
//     } else {
//       _goToNextStory(homeController);
//     }
//   }

//   void _goToNextStory(HomePageController homeController) {
//     Get.back();
//     Get.to(
//       () => NewPage(),
//       transition: Transition.leftToRight,
//     );

//     homeController.selectNewToShow(
//         homeController.currentIndex + 1, homeController.news);
//   }

//   List<StoryItem> _getItems(StoryController controller) {
//     HomePageController homeController = Get.find();
//     final List<HomeNewModel> news = homeController.news;
//     final int currentIndex = homeController.currentIndex;
//     final HomeNewModel storyItem = news[currentIndex];
//     final List<StoryItem> items = news[currentIndex]
//         .mediaList
//         .map((mediaUrl) => StoryItem.pageImage(
//             url: mediaUrl,
//             caption: storyItem.title + '\n' + (storyItem.text ?? ''),
//             controller: controller,
//             moreWidget: storyItem.releated != null
//                 ? SizedBox(
//                     height: 10,
//                   )
//                 : SizedBox()))
//         .toList();
//     return items;
//     //   return [];
//   }
// }
