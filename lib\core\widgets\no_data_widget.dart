import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';

class NoDataWidget extends StatelessWidget {
  final double? imageWidth;
  final double? imageHeight;

  const NoDataWidget({
    this.imageHeight,
    this.imageWidth,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Assets.icons.noDataIcon.svg(
          width: imageWidth,
          height: imageHeight,
        ),
        const SizedBox(height: 10,),
        Text(
          'No Data'.tr,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: AppStyle.primaryColor,
            fontWeight: FontWeight.bold
          ),
        )
      ],
    );
  }
}
