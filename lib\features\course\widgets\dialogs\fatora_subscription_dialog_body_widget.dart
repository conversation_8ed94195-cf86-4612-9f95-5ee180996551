import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/features/course/contollers/subscription_controller.dart';

class FatoraSubscriptionDialogBodyWidget extends StatelessWidget {
  final SubscriptionType type;
  final bool fromCourse;
  final String name;
  final String price;
  final int? id;
  final int? index;
  const FatoraSubscriptionDialogBodyWidget({
    required this.type,
    required this.name,
    required this.price,
    required this.fromCourse,
    this.id,
    this.index,
  });
  @override
  Widget build(BuildContext context) {
    SubscriptionController controller = Get.find();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
            alignment: Alignment.topRight,
            child: Padding(
              padding: const EdgeInsets.only(top: 5, right: 5),
              child: GestureDetector(
                child: Assets.icons.cancelDialog.svg(),
                onTap: () => controller.closeDialog(),
              ),
            )),
        Text(
          "Confirm Subscription".tr,
          textAlign: TextAlign.center,
          style:
              Get.textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w700),
        ),
        SizedBox(height: 16),
        Text(
          'fatora subscription text'.trParams({'name': name, 'price': price}),
          textAlign: TextAlign.center,
          style: Get.textTheme.titleSmall!.copyWith(
              color: AppStyle.boldGreyTextColor, fontWeight: FontWeight.w500),
        ),
        SizedBox(height: 25),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Expanded(
                child: AppButton(
                  height: 50,
                  radius: 15,
                  withLoading: false,
                  margin: EdgeInsets.zero,
                  fillColor: AppStyle.whiteColor,
                  style: Get.textTheme.bodyMedium!.copyWith(
                      color: AppStyle.primaryColor,
                      fontWeight: FontWeight.w700),
                  text: 'Cancel'.tr,
                  onTap: () async => controller.closeDialog(),
                ),
              ),
              const SizedBox(
                width: 15,
              ),
              Expanded(
                child: AppButton(
                  height: 50,
                  withLoading: false,
                  radius: 15,
                  margin: EdgeInsets.zero,
                  style: Get.textTheme.bodyMedium!.copyWith(
                      color: AppStyle.whiteColor, fontWeight: FontWeight.w700),
                  text: 'Confirm'.tr,
                  onTap: () async => controller.confirmFatoraSubscription(
                      type, fromCourse,
                      index: index, id: id),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 12),
      ],
    );
  }
}
