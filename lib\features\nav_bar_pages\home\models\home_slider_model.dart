
class HomeSliderModel{
  List<String> oneSectionImages = [];
  List<String> twoSectionImages = [];
  List<String> threeSectionImages = [];
  List<String> fourSectionImages = [];

  HomeSliderModel({
    required this.oneSectionImages,
    required this.twoSectionImages,
    required this.threeSectionImages,
    required this.fourSectionImages
  });

  factory HomeSliderModel.fromJson(Map<String, dynamic> json,) =>
      HomeSliderModel(
    oneSectionImages: List<String>.from(json["one"].map((x) => x['img'])),
    twoSectionImages: List<String>.from(json["two"].map((x) => x['img'])),
    threeSectionImages: List<String>.from(json["three"].map((x) => x['img'])),
    fourSectionImages: List<String>.from(json["four"].map((x) => x['img'])),
  );


}