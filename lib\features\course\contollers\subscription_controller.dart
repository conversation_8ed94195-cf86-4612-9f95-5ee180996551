import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/course/contollers/course_controller.dart';
import 'package:all_in_one/features/course/models/code_information_model.dart';
import 'package:all_in_one/features/course/online_payment_page.dart';
import 'package:all_in_one/features/course/widgets/dialogs/check_code_dialog_body_widget.dart';
import 'package:all_in_one/features/course/widgets/dialogs/confirm_subscription_dialog_body_widget.dart';
import 'package:all_in_one/features/course/widgets/dialogs/subscription_status_dialog_body_widget.dart';
import 'package:all_in_one/features/payment/controller.dart';
import 'package:all_in_one/features/payment/index.dart';

class SubscriptionController extends GetxController {
  DataController dataController = Get.find();
  AppController appController = Get.find();

  String subscriptionFirstText = '';
  String subscriptionSecondText = '';

  TextEditingController code = TextEditingController();

  RxnString _codeError = RxnString(null);
  String? get codeError => this._codeError.value;
  set codeError(String? value) => this._codeError.value = value;

  RxBool _checkCodeLoading = false.obs;
  get checkCodeLoading => this._checkCodeLoading.value;
  set checkCodeLoading(value) => this._checkCodeLoading.value = value;

  late CodeInformationModel codeInformation;

  void checkCode() async {
    codeError = null;
    if (code.text.isEmpty) {
      codeError = 'This Filed is required'.tr;
      return;
    }
    checkCodeLoading = true;
    ResponseModel response;
    response = await dataController.getData(
      url: API.checkCodeApi(code.text),
    );
    if (response.success) {
      checkCodeLoading = false;
      codeInformation = CodeInformationModel.fromJson(response.data);
      if (codeInformation.status != 'Unvalid') {
        getSubscriptionText(codeInformation.type, codeInformation.itemTitle);
        closeCheckDialog();
        DialogHelper.showDialog(
          dialogBody: ConfirmSubscriptionDialogBodyWidget(),
        );
      } else {
        codeError = 'This Code is Un valid'.tr;
      }
    } else if (response.code == ErrorCode.VALIDATION_ERROR ||
        response.errors != null ||
        response.errors!.isNotEmpty) {
      checkCodeLoading = false;
      codeError = response.errorsAsString;
    } else {
      checkCodeLoading = false;
      appController.showToast(Get.context!,
          message: response.message!, status: ToastStatus.fail);
    }

  }

  RxBool _confirmSubscriptionLoading = false.obs;
  get confirmSubscriptionLoading => this._confirmSubscriptionLoading.value;
  set confirmSubscriptionLoading(value) =>
      this._confirmSubscriptionLoading.value = value;

  void confirmCodeSubscription() async {
    confirmSubscriptionLoading = true;
    ResponseModel response;
    response = await dataController
        .postData(url: API.subscribeCodeApi, body: {'code': code.text});
    if (response.success) {
      confirmSubscriptionLoading = false;
      Get.back();
      DialogHelper.showDialog(
        dialogBody: SubscriptionStatusDialogBodyWidget(
          status: SubscriptionStatus.Success,
        ));
      if(Get.isRegistered<CourseController>()){
        CourseController controller = Get.find();
        controller.loadSubjectDetail();
      }
    } else if (response.code == ErrorCode.VALIDATION_ERROR ||
        response.errors != null ||
        response.errors!.isNotEmpty) {
      confirmSubscriptionLoading = false;
      codeError = response.errorsAsString;
      Get.back();
      DialogHelper.showDialog(
        dialogBody: SubscriptionStatusDialogBodyWidget(
          status: SubscriptionStatus.Failed,
        ),
      );
    } else {
      confirmSubscriptionLoading = false;
      appController.showToast(Get.context!,
          message: response.message!, status: ToastStatus.fail);
    }
  }

  void confirmFatoraSubscription(SubscriptionType type, bool fromCourse,
      {int? id, int? index}) async {
    confirmSubscriptionLoading = true;
    ResponseModel response;
    Map<String, String> data = {
      'type': type.getSubscriptionType(),
      'id': id.toString()
    };
    // data.removeWhere((key, value) => value == null);
    // Nav.to(Pages.payment, params: data);
   //     PaymentMethodsController controller =
    Get.back();
    await DialogHelper.showDialog(dialogBody: CheckCodeDialogBodyWidget());

    // DialogHelper.showDialog(
    //     dialogBody: PaymentMethodsDialog(
    //   type: type.getSubscriptionType(),
    //   id: id,
    //   fromCourse: fromCourse,
    //   index: index,
    // ));
    // response =
    //     await dataController.postData(url: API.subscribeFatoryApi, body: data);
    // if (response.success) {
    //   confirmSubscriptionLoading = false;
    //   Get.back();
    //   log(response.data["Data"].toString());
    //   Nav.to(Pages.onlinePayment, arguments: {
    //     "from_course": fromCourse,
    //     "type": type.getSubscriptionType(),
    //     "url": response.data['Data']['url'],
    //     "index": index,
    //   });
    // } else if (response.code == ErrorCode.VALIDATION_ERROR ||
    //     response.errors != null ||
    //     response.errors!.isNotEmpty) {
    //   confirmSubscriptionLoading = false;
    //   codeError = response.errors!.first;
    //   Get.back();
    //   DialogHelper.showDialog(
    //     dialogBody: SubscriptionStatusDialogBodyWidget(
    //       status: SubscriptionStatus.Failed,
    //     ),
    //   );
    // } else {
    //   confirmSubscriptionLoading = false;
    //   appController.showToast(Get.context!,
    //       message: response.message!, status: ToastStatus.fail);
    // }
  }

  // onConfirmFatoraSuccess(){
  //   Nav.to(Pages.onlinePayment);
  // }

  getSubscriptionText(String type, String name) {
    if (type == 'subjects') {
      subscriptionFirstText =
          "The code entered is valid. The code opens the subject"
              .trParams({'name': name});
      subscriptionSecondText =
          'When you confirm your subscription to this course, you will be able to view all the research and lessons related to this course'
              .tr;
    } else if (type == 'sections') {
      subscriptionFirstText =
          "The code entered is valid. The code opens the search"
              .trParams({'name': name});
      subscriptionSecondText =
          'When you confirm your subscription to this research, you will be able to view all the lessons related to this research'
              .tr;
    } else if (type == 'years') {
      subscriptionFirstText =
          "The code entered is valid. The code opens the year"
              .trParams({'name': name});
      subscriptionSecondText =
          'When you confirm your subscription for this year, you will be able to view all the materials for this year'
              .tr;
    }
  }

  closeDialog() {
    code.clear();
    codeError = null;
    confirmSubscriptionLoading = false;
    checkCodeLoading = false;
    Get.back();
  }

  closeCheckDialog() {
    codeError = null;
    confirmSubscriptionLoading = false;
    checkCodeLoading = false;
    Get.back();
  }
}
