/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:lottie/lottie.dart' as _lottie;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/LamaSans-Black.ttf
  String get lamaSansBlack => 'assets/fonts/LamaSans-Black.ttf';

  /// File path: assets/fonts/LamaSans-Bold.ttf
  String get lamaSansBold => 'assets/fonts/LamaSans-Bold.ttf';

  /// File path: assets/fonts/LamaSans-ExtraBold.ttf
  String get lamaSansExtraBold => 'assets/fonts/LamaSans-ExtraBold.ttf';

  /// File path: assets/fonts/LamaSans-ExtraLight.ttf
  String get lamaSansExtraLight => 'assets/fonts/LamaSans-ExtraLight.ttf';

  /// File path: assets/fonts/LamaSans-Light.ttf
  String get lamaSansLight => 'assets/fonts/LamaSans-Light.ttf';

  /// File path: assets/fonts/LamaSans-Medium.ttf
  String get lamaSansMedium => 'assets/fonts/LamaSans-Medium.ttf';

  /// File path: assets/fonts/LamaSans-Regular.ttf
  String get lamaSansRegular => 'assets/fonts/LamaSans-Regular.ttf';

  /// File path: assets/fonts/LamaSans-SemiBold.ttf
  String get lamaSansSemiBold => 'assets/fonts/LamaSans-SemiBold.ttf';

  /// File path: assets/fonts/LamaSans-Thin.ttf
  String get lamaSansThin => 'assets/fonts/LamaSans-Thin.ttf';

  /// File path: assets/fonts/NotoSansArabic.ttf
  String get notoSansArabic => 'assets/fonts/NotoSansArabic.ttf';

  /// List of all assets
  List<String> get values => [
    lamaSansBlack,
    lamaSansBold,
    lamaSansExtraBold,
    lamaSansExtraLight,
    lamaSansLight,
    lamaSansMedium,
    lamaSansRegular,
    lamaSansSemiBold,
    lamaSansThin,
    notoSansArabic,
  ];
}

class $AssetsGifGen {
  const $AssetsGifGen();

  /// File path: assets/gif/loading.gif
  AssetGenImage get loading => const AssetGenImage('assets/gif/loading.gif');

  /// List of all assets
  List<AssetGenImage> get values => [loading];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/._app_logo.png
  AssetGenImage get aAppLogo =>
      const AssetGenImage('assets/icons/._app_logo.png');

  /// File path: assets/icons/._book.svg
  SvgGenImage get aBook => const SvgGenImage('assets/icons/._book.svg');

  /// File path: assets/icons/Arrow Left.svg
  SvgGenImage get arrowLeft => const SvgGenImage('assets/icons/Arrow Left.svg');

  /// File path: assets/icons/Notebook_icon.svg
  SvgGenImage get notebookIcon =>
      const SvgGenImage('assets/icons/Notebook_icon.svg');

  /// File path: assets/icons/app_logo.png
  AssetGenImage get appLogo => const AssetGenImage('assets/icons/app_logo.png');

  /// File path: assets/icons/arrowRight.svg
  SvgGenImage get arrowRight =>
      const SvgGenImage('assets/icons/arrowRight.svg');

  /// File path: assets/icons/book.svg
  SvgGenImage get book => const SvgGenImage('assets/icons/book.svg');

  /// File path: assets/icons/buy.svg
  SvgGenImage get buy => const SvgGenImage('assets/icons/buy.svg');

  /// File path: assets/icons/call.svg
  SvgGenImage get call => const SvgGenImage('assets/icons/call.svg');

  /// File path: assets/icons/cancel_dialog.svg
  SvgGenImage get cancelDialog =>
      const SvgGenImage('assets/icons/cancel_dialog.svg');

  /// File path: assets/icons/cash.svg
  SvgGenImage get cash => const SvgGenImage('assets/icons/cash.svg');

  /// File path: assets/icons/check.svg
  SvgGenImage get check => const SvgGenImage('assets/icons/check.svg');

  /// File path: assets/icons/class_icon.svg
  SvgGenImage get classIcon => const SvgGenImage('assets/icons/class_icon.svg');

  /// File path: assets/icons/contact.svg
  SvgGenImage get contact => const SvgGenImage('assets/icons/contact.svg');

  /// File path: assets/icons/correction_icon.png
  AssetGenImage get correctionIcon =>
      const AssetGenImage('assets/icons/correction_icon.png');

  /// File path: assets/icons/darisni_app_name.png
  AssetGenImage get darisniAppName =>
      const AssetGenImage('assets/icons/darisni_app_name.png');

  /// File path: assets/icons/darisni_logo.png
  AssetGenImage get darisniLogo =>
      const AssetGenImage('assets/icons/darisni_logo.png');

  /// File path: assets/icons/darisni_white_logo.png
  AssetGenImage get darisniWhiteLogo =>
      const AssetGenImage('assets/icons/darisni_white_logo.png');

  /// File path: assets/icons/delete.svg
  SvgGenImage get delete => const SvgGenImage('assets/icons/delete.svg');

  /// File path: assets/icons/delete_account_icon.png
  AssetGenImage get deleteAccountIcon =>
      const AssetGenImage('assets/icons/delete_account_icon.png');

  /// File path: assets/icons/earth_planet_icon.svg
  SvgGenImage get earthPlanetIcon =>
      const SvgGenImage('assets/icons/earth_planet_icon.svg');

  /// File path: assets/icons/edit.svg
  SvgGenImage get edit => const SvgGenImage('assets/icons/edit.svg');

  /// File path: assets/icons/eyeWithDiagonalLine.svg
  SvgGenImage get eyeWithDiagonalLine =>
      const SvgGenImage('assets/icons/eyeWithDiagonalLine.svg');

  /// File path: assets/icons/facebook.svg
  SvgGenImage get facebook => const SvgGenImage('assets/icons/facebook.svg');

  /// File path: assets/icons/fatora.svg
  SvgGenImage get fatora => const SvgGenImage('assets/icons/fatora.svg');

  /// File path: assets/icons/gallerysvg.svg
  SvgGenImage get gallerysvg =>
      const SvgGenImage('assets/icons/gallerysvg.svg');

  /// File path: assets/icons/hd.svg
  SvgGenImage get hd => const SvgGenImage('assets/icons/hd.svg');

  /// File path: assets/icons/home_icon.svg
  SvgGenImage get homeIcon => const SvgGenImage('assets/icons/home_icon.svg');

  /// File path: assets/icons/info_icon.svg
  SvgGenImage get infoIcon => const SvgGenImage('assets/icons/info_icon.svg');

  /// File path: assets/icons/insta.svg
  SvgGenImage get insta => const SvgGenImage('assets/icons/insta.svg');

  /// File path: assets/icons/link.svg
  SvgGenImage get link => const SvgGenImage('assets/icons/link.svg');

  /// File path: assets/icons/lockOutline.svg
  SvgGenImage get lockOutline =>
      const SvgGenImage('assets/icons/lockOutline.svg');

  /// File path: assets/icons/lock_icon.svg
  SvgGenImage get lockIcon => const SvgGenImage('assets/icons/lock_icon.svg');

  /// File path: assets/icons/log_out_icon.png
  AssetGenImage get logOutIcon =>
      const AssetGenImage('assets/icons/log_out_icon.png');

  /// File path: assets/icons/logo_with_name.png
  AssetGenImage get logoWithName =>
      const AssetGenImage('assets/icons/logo_with_name.png');

  /// File path: assets/icons/mtn.svg
  SvgGenImage get mtn => const SvgGenImage('assets/icons/mtn.svg');

  /// File path: assets/icons/my_subject_icon.svg
  SvgGenImage get mySubjectIcon =>
      const SvgGenImage('assets/icons/my_subject_icon.svg');

  /// File path: assets/icons/no_data_icon.svg
  SvgGenImage get noDataIcon =>
      const SvgGenImage('assets/icons/no_data_icon.svg');

  /// File path: assets/icons/notification_icon.svg
  SvgGenImage get notificationIcon =>
      const SvgGenImage('assets/icons/notification_icon.svg');

  /// File path: assets/icons/permissions_icon.svg
  SvgGenImage get permissionsIcon =>
      const SvgGenImage('assets/icons/permissions_icon.svg');

  /// File path: assets/icons/play_icon.svg
  SvgGenImage get playIcon => const SvgGenImage('assets/icons/play_icon.svg');

  /// File path: assets/icons/plus.svg
  SvgGenImage get plus => const SvgGenImage('assets/icons/plus.svg');

  /// File path: assets/icons/privacy_icon.svg
  SvgGenImage get privacyIcon =>
      const SvgGenImage('assets/icons/privacy_icon.svg');

  /// File path: assets/icons/profile_icon.svg
  SvgGenImage get profileIcon =>
      const SvgGenImage('assets/icons/profile_icon.svg');

  /// File path: assets/icons/refresh.svg
  SvgGenImage get refresh => const SvgGenImage('assets/icons/refresh.svg');

  /// File path: assets/icons/sd.svg
  SvgGenImage get sd => const SvgGenImage('assets/icons/sd.svg');

  /// File path: assets/icons/share.svg
  SvgGenImage get share => const SvgGenImage('assets/icons/share.svg');

  /// File path: assets/icons/tasks_icon.svg
  SvgGenImage get tasksIcon => const SvgGenImage('assets/icons/tasks_icon.svg');

  /// File path: assets/icons/telegram_icon.svg
  SvgGenImage get telegramIcon =>
      const SvgGenImage('assets/icons/telegram_icon.svg');

  /// File path: assets/icons/user_icon.svg
  SvgGenImage get userIcon => const SvgGenImage('assets/icons/user_icon.svg');

  /// File path: assets/icons/user_outline_icon.svg
  SvgGenImage get userOutlineIcon =>
      const SvgGenImage('assets/icons/user_outline_icon.svg');

  /// File path: assets/icons/video_icon.svg
  SvgGenImage get videoIcon => const SvgGenImage('assets/icons/video_icon.svg');

  /// File path: assets/icons/whats_icon.svg
  SvgGenImage get whatsIcon => const SvgGenImage('assets/icons/whats_icon.svg');

  /// File path: assets/icons/whatsapp.svg
  SvgGenImage get whatsapp => const SvgGenImage('assets/icons/whatsapp.svg');

  /// File path: assets/icons/youtube.svg
  SvgGenImage get youtube => const SvgGenImage('assets/icons/youtube.svg');

  /// List of all assets
  List<dynamic> get values => [
    aAppLogo,
    aBook,
    arrowLeft,
    notebookIcon,
    appLogo,
    arrowRight,
    book,
    buy,
    call,
    cancelDialog,
    cash,
    check,
    classIcon,
    contact,
    correctionIcon,
    darisniAppName,
    darisniLogo,
    darisniWhiteLogo,
    delete,
    deleteAccountIcon,
    earthPlanetIcon,
    edit,
    eyeWithDiagonalLine,
    facebook,
    fatora,
    gallerysvg,
    hd,
    homeIcon,
    infoIcon,
    insta,
    link,
    lockOutline,
    lockIcon,
    logOutIcon,
    logoWithName,
    mtn,
    mySubjectIcon,
    noDataIcon,
    notificationIcon,
    permissionsIcon,
    playIcon,
    plus,
    privacyIcon,
    profileIcon,
    refresh,
    sd,
    share,
    tasksIcon,
    telegramIcon,
    userIcon,
    userOutlineIcon,
    videoIcon,
    whatsIcon,
    whatsapp,
    youtube,
  ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/auth_image.png
  AssetGenImage get authImage =>
      const AssetGenImage('assets/images/auth_image.png');

  /// File path: assets/images/permissions_image.png
  AssetGenImage get permissionsImage =>
      const AssetGenImage('assets/images/permissions_image.png');

  /// File path: assets/images/permisstions_provisions_background.png
  AssetGenImage get permisstionsProvisionsBackground => const AssetGenImage(
    'assets/images/permisstions_provisions_background.png',
  );

  /// File path: assets/images/provisions_image.png
  AssetGenImage get provisionsImage =>
      const AssetGenImage('assets/images/provisions_image.png');

  /// File path: assets/images/splash_background.png
  AssetGenImage get splashBackground =>
      const AssetGenImage('assets/images/splash_background.png');

  /// File path: assets/images/splash_image.svg
  SvgGenImage get splashImage =>
      const SvgGenImage('assets/images/splash_image.svg');

  /// List of all assets
  List<dynamic> get values => [
    authImage,
    permissionsImage,
    permisstionsProvisionsBackground,
    provisionsImage,
    splashBackground,
    splashImage,
  ];
}

class $AssetsLottieGen {
  const $AssetsLottieGen();

  /// File path: assets/lottie/faild_lottie.json
  LottieGenImage get faildLottie =>
      const LottieGenImage('assets/lottie/faild_lottie.json');

  /// File path: assets/lottie/imageLoading.json
  LottieGenImage get imageLoading =>
      const LottieGenImage('assets/lottie/imageLoading.json');

  /// File path: assets/lottie/loading.json
  LottieGenImage get loading =>
      const LottieGenImage('assets/lottie/loading.json');

  /// File path: assets/lottie/success_lottie.json
  LottieGenImage get successLottie =>
      const LottieGenImage('assets/lottie/success_lottie.json');

  /// List of all assets
  List<LottieGenImage> get values => [
    faildLottie,
    imageLoading,
    loading,
    successLottie,
  ];
}

class $AssetsVideosGen {
  const $AssetsVideosGen();

  /// File path: assets/videos/splash.mp4
  String get splash => 'assets/videos/splash.mp4';

  /// List of all assets
  List<String> get values => [splash];
}

class $AssetsVoicesGen {
  const $AssetsVoicesGen();

  /// File path: assets/voices/correct_answer.mp3
  String get correctAnswer => 'assets/voices/correct_answer.mp3';

  /// File path: assets/voices/wrong_answer.mp3
  String get wrongAnswer => 'assets/voices/wrong_answer.mp3';

  /// List of all assets
  List<String> get values => [correctAnswer, wrongAnswer];
}

class Assets {
  const Assets._();

  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsGifGen gif = $AssetsGifGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const LottieGenImage loading = LottieGenImage('assets/loading.json');
  static const $AssetsLottieGen lottie = $AssetsLottieGen();
  static const $AssetsVideosGen videos = $AssetsVideosGen();
  static const $AssetsVoicesGen voices = $AssetsVoicesGen();

  /// List of all assets
  static List<LottieGenImage> get values => [loading];
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class LottieGenImage {
  const LottieGenImage(this._assetName, {this.flavors = const {}});

  final String _assetName;
  final Set<String> flavors;

  _lottie.LottieBuilder lottie({
    Animation<double>? controller,
    bool? animate,
    _lottie.FrameRate? frameRate,
    bool? repeat,
    bool? reverse,
    _lottie.LottieDelegates? delegates,
    _lottie.LottieOptions? options,
    void Function(_lottie.LottieComposition)? onLoaded,
    _lottie.LottieImageProviderFactory? imageProviderFactory,
    Key? key,
    AssetBundle? bundle,
    Widget Function(BuildContext, Widget, _lottie.LottieComposition?)?
    frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    double? width,
    double? height,
    BoxFit? fit,
    AlignmentGeometry? alignment,
    String? package,
    bool? addRepaintBoundary,
    FilterQuality? filterQuality,
    void Function(String)? onWarning,
  }) {
    return _lottie.Lottie.asset(
      _assetName,
      controller: controller,
      animate: animate,
      frameRate: frameRate,
      repeat: repeat,
      reverse: reverse,
      delegates: delegates,
      options: options,
      onLoaded: onLoaded,
      imageProviderFactory: imageProviderFactory,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      package: package,
      addRepaintBoundary: addRepaintBoundary,
      filterQuality: filterQuality,
      onWarning: onWarning,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
