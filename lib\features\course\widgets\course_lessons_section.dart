import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/app_animation/item_animation.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/core/widgets/no_data_widget.dart';
import 'package:all_in_one/features/course/widgets/dialogs/confirm_subscription_dialog_body_widget.dart';
import '../contollers/course_controller.dart';
import 'course_lesson_list_item_widget.dart';
import 'dialogs/fatora_subscription_dialog_body_widget.dart';

class CourseLessonsSection extends StatelessWidget {
  final bool fromHome;
  const CourseLessonsSection({required this.fromHome});

  @override
  Widget build(BuildContext context) {
    CourseController controller = Get.find();
    AppController appController = Get.find();
    return !controller.areLessonsEmpty
        ? AnimationLimiter(
            child: ListView.builder(
              itemCount: controller.subjectModel.lectures.length,
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                return (fromHome &&
                            controller.subjectModel.lectures[index].lessons
                                .isNotEmpty) ||
                        (!fromHome &&
                            controller.subjectModel.lectures[index]
                                .isSubscription.value)
                    ? Obx(() => ItemAnimation(
                          index: index,
                          elementCount: 5,
                          child: Container(
                            margin: EdgeInsets.only(
                                left: 15, right: 15, bottom: 10),
                            decoration: BoxDecoration(
                              color: AppStyle.whiteBackgroundColor,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(10)),
                              boxShadow: [
                                BoxShadow(
                                    color: Colors.grey.withOpacity(0.2),
                                    blurRadius: 1.0,
                                    spreadRadius: 1,
                                    offset: const Offset(
                                        -2, 1) // changes position of shadow
                                    ),
                              ],
                            ),
                            child: ListTileTheme(
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 20, vertical: 4),
                              dense: true,
                              child: ExpansionTile(
                                title: Text(
                                  controller.subjectModel.lectures[index].title,
                                  softWrap: true,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: Theme.of(context)
                                      .textTheme
                                      .titleMedium!
                                      .copyWith(color: AppStyle.primaryColor),
                                ),
                                expandedCrossAxisAlignment:
                                    CrossAxisAlignment.start,
                                childrenPadding:
                                    EdgeInsets.symmetric(horizontal: 20),
                                children: [
                                  Visibility(
                                    visible: !controller.subjectModel
                                        .lectures[index].isSubscription.value,
                                    child: Padding(
                                      padding:
                                          const EdgeInsets.only(bottom: 15),
                                      child: Row(
                                        children: [
                                          if (!appController.isInReview)
                                            Expanded(
                                              child: Text(
                                                'Subscription price'.trParams({
                                                  'price': controller
                                                      .subjectModel
                                                      .lectures[index]
                                                      .price
                                                      .toString()
                                                }),
                                                style: Theme.of(context)
                                                    .textTheme
                                                    .bodyMedium!
                                                    .copyWith(
                                                        color: AppStyle
                                                            .lightBlackColor,
                                                        fontWeight:
                                                            FontWeight.bold),
                                              ),
                                            ),
                                          if (!appController.isInReview)
                                            IconButton(
                                                onPressed: () async {
                                                  DialogHelper.showDialog(
                                                      dialogBody:
                                                          FatoraSubscriptionDialogBodyWidget(
                                                    type: SubscriptionType
                                                        .Section,
                                                    name: controller
                                                        .subjectModel
                                                        .lectures[index]
                                                        .title,
                                                    price: controller
                                                        .subjectModel
                                                        .lectures[index]
                                                        .price
                                                        .toString(),
                                                    fromCourse: true,
                                                    id: controller.subjectModel
                                                        .lectures[index].id,
                                                    index: index,
                                                  ));
                                                  // controller.afterSubscription(SubscriptionType.Section,index: index);
                                                },
                                                icon: Assets.icons.plus
                                                    .svg(width: 32, height: 32))
                                          // AppButton(
                                          //   borderColor: AppStyle.primaryColor,
                                          //   fillColor:
                                          //       AppStyle.whiteBackgroundColor,
                                          //   text: 'Subscription'.tr,
                                          //   height: 40,
                                          //   radius: 10,
                                          //   withLoading: false,
                                          //   padding: EdgeInsets.symmetric(
                                          //       horizontal: 10, vertical: 5),
                                          //   margin: EdgeInsets.zero,
                                          //   style: Get.textTheme.titleMedium!
                                          //       .copyWith(
                                          //           color:
                                          //               AppStyle.primaryColor,
                                          //           fontWeight:
                                          //               FontWeight.bold),
                                          //   onTap: () async {
                                          //     DialogHelper.showDialog(
                                          //         dialogBody:
                                          //             FatoraSubscriptionDialogBodyWidget(
                                          //       type: SubscriptionType.Section,
                                          //       name: controller.subjectModel
                                          //           .sections[index].title,
                                          //       price: controller.subjectModel
                                          //           .sections[index].price
                                          //           .toString(),
                                          //       fromCourse: true,
                                          //       id: controller.subjectModel
                                          //           .sections[index].id,
                                          //       index: index,
                                          //     ));
                                          //     // controller.afterSubscription(SubscriptionType.Section,index: index);
                                          //   },
                                          // ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Container(
                                    height: 5,
                                    color: AppStyle.whiteColor,
                                  ),
                                  ListView.separated(
                                    itemCount: controller.subjectModel
                                        .lectures[index].lessons.length,
                                    padding: EdgeInsets.zero,
                                    physics:
                                        const NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    itemBuilder: (context, indexx) {
                                      return CourseLessonListItemWidget(
                                        lesson: controller.subjectModel
                                            .lectures[index].lessons[indexx],
                                        isSubscription: controller
                                            .subjectModel
                                            .lectures[index]
                                            .isSubscription
                                            .value,
                                      );
                                    },
                                    separatorBuilder: (context, indexx) {
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 28),
                                        child: Divider(
                                          color: AppStyle.lightGreyColor
                                              .withOpacity(0.5),
                                          thickness: 2,
                                          height: 0,
                                        ),
                                      );
                                    },
                                  ),
                                ],
                                onExpansionChanged: (bool isExpanded) {
                                  controller.selectedSection = index;
                                },
                              ),
                            ),
                          ),
                        ))
                    : const SizedBox();
              },
            ),
          )
        : NoDataWidget(
            imageWidth: 100,
            imageHeight: 100,
          );
  }
}
