import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';

class SideMenuItem extends StatelessWidget {
  final String itemName;
  final String icon;
  final TextStyle? titleStyle;
  final Function() onTap;
  final bool isSvg;

  const SideMenuItem(
      {Key? key,
      required this.itemName,
      this.titleStyle,
      required this.onTap,
      this.isSvg = true,
      required this.icon})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: ListTile(
        onTap: onTap,
        minLeadingWidth: 8,
        minVerticalPadding: 0,
        title: Text(
          itemName,
          style: titleStyle ?? Get.textTheme.titleMedium,
        ),
        leading: SizedBox(height: 20, width: 20, child: isSvg?
          SvgPicture.asset(icon,color: AppStyle.primaryColor,):
          Image.asset(icon,color: AppStyle.primaryColor,),
        ),
      ),
    );
  }
}
