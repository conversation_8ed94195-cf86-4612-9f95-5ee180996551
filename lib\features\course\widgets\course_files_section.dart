import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/app_animation/item_animation.dart';
import 'package:all_in_one/core/widgets/no_data_widget.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/course/contollers/course_controller.dart';
import 'package:all_in_one/features/course/contollers/subscription_controller.dart';
import 'package:all_in_one/features/course/models/course_section_model.dart';

class CourseFilesSection extends StatelessWidget {
  const CourseFilesSection();

  @override
  Widget build(BuildContext context) {
    CourseController controller = Get.find();
    return AnimationLimiter(
      child: !controller.areFilesEmpty
          ? ListView.builder(
              itemCount: controller.subjectModel.lectures.length,
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                return controller.subjectModel.lectures[index].files.isNotEmpty
                    ? ItemAnimation(
                        index: index,
                        elementCount: controller.subjectModel.lectures.length,
                        child: Container(
                          margin:
                              EdgeInsets.only(left: 15, right: 15, bottom: 10),
                          decoration: BoxDecoration(
                            color: AppStyle.whiteBackgroundColor,
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                            boxShadow: [
                              BoxShadow(
                                  color: Colors.grey.withOpacity(0.2),
                                  blurRadius: 1.0,
                                  spreadRadius: 1,
                                  offset: const Offset(
                                      -2, 1) // changes position of shadow
                                  ),
                            ],
                          ),
                          child: ListTileTheme(
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 20, vertical: 4),
                            dense: true,
                            child: ExpansionTile(
                              title: Text(
                                controller.subjectModel.lectures[index].title,
                                softWrap: true,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium!
                                    .copyWith(color: AppStyle.primaryColor),
                              ),
                              children: [
                                Container(
                                  height: 5,
                                  color: AppStyle.whiteColor,
                                ),
                                ListView.separated(
                                  itemCount: controller.subjectModel
                                      .lectures[index].files.length,
                                  padding: EdgeInsets.zero,
                                  physics: const NeverScrollableScrollPhysics(),
                                  shrinkWrap: true,
                                  itemBuilder: (context, indexx) {
                                    return Obx(() => InkWell(
                                          onTap: controller
                                                  .subjectModel
                                                  .lectures[index]
                                                  .isSubscription
                                                  .value
                                              ? () => Nav.to(Pages.pdfReader,
                                                      arguments: {
                                                        "url": controller
                                                            .subjectModel
                                                            .lectures[index]
                                                            .files[indexx]
                                                            .originalUrl,
                                                        "link": controller
                                                            .subjectModel
                                                            .lectures[index]
                                                            .files[indexx]
                                                            .supportLink,
                                                        "title": controller
                                                            .subjectModel
                                                            .lectures[index]
                                                            .files[indexx]
                                                            .title
                                                      })
                                              : () {
                                                  controller.appController
                                                      .showToast(context,
                                                          message:
                                                              'Please participate in this research first'
                                                                  .tr,
                                                          status: ToastStatus
                                                              .warning);
                                                },
                                          child: Container(
                                            margin: EdgeInsets.only(bottom: 10),
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 20, vertical: 16),
                                            child: Row(
                                              children: [
                                                Expanded(
                                                  child: Text(
                                                    controller
                                                        .subjectModel
                                                        .lectures[index]
                                                        .files[indexx]
                                                        .title,
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodyLarge!
                                                        .copyWith(
                                                            color: AppStyle
                                                                .lightBlackColor,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w500),
                                                  ),
                                                ),
                                                const SizedBox(
                                                  width: 10,
                                                ),
                                                controller
                                                        .subjectModel
                                                        .lectures[index]
                                                        .isSubscription
                                                        .value
                                                    ? SizedBox()
                                                    : Assets.icons.lockIcon
                                                        .svg()
                                              ],
                                            ),
                                          ),
                                        ));
                                  },
                                  separatorBuilder: (context, indexx) {
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 28),
                                      child: Divider(
                                        color: AppStyle.lightGreyColor
                                            .withOpacity(0.5),
                                        thickness: 2,
                                        height: 0,
                                      ),
                                    );
                                  },
                                ),
                              ],
                              onExpansionChanged: (bool isExpanded) {},
                            ),
                          ),
                        ),
                      )
                    : const SizedBox();
              },
            )
          : NoDataWidget(
              imageWidth: 100,
              imageHeight: 100,
            ),
    );
  }
}
