import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/features/course/contollers/subscription_controller.dart';


class CheckCodeDialogBodyWidget extends StatelessWidget {
  const CheckCodeDialogBodyWidget();


  @override
  Widget build(BuildContext context) {
    SubscriptionController controller = Get.find();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
            alignment: Alignment.topRight,
            child: Padding(
              padding: const EdgeInsets.only(top: 5 , right: 5),
              child: GestureDetector(
                child: Assets.icons.cancelDialog.svg(),
                onTap: ()=>controller.closeDialog(),
              ),
            )
        ),
        Text(
          "Check of subscription code".tr,
          textAlign: TextAlign.center,
          style: Get.textTheme.titleLarge!.copyWith(
              fontWeight: FontWeight.w700
          ),
        ),
        <PERSON><PERSON><PERSON>ox(height: 20),
        <PERSON><PERSON>(
          alignment: AlignmentDirectional.topStart,
          child: Text(
            'Please enter the subscription code to continue'.tr,
            style: Get.textTheme.titleSmall!.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        SizedBox(height: 10),
        Obx(
            ()=> TextFormField(
              controller: controller.code,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppStyle.lightBlackColor),
              keyboardType: TextInputType.text,
              decoration: InputDecoration(
                  hintText: "Subscription code".tr,
                  errorText: controller.codeError
              ),
            ),
        ),
        SizedBox(height: 25),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Expanded(
                child: AppButton(
                  height: 50,
                  radius: 15,
                  withLoading: false,
                  margin: EdgeInsets.zero,
                  fillColor: AppStyle.whiteColor,
                  style: Get.textTheme.bodyMedium!.copyWith(color: AppStyle.primaryColor,fontWeight: FontWeight.w700),
                  text: 'Cancel'.tr,
                  onTap: ()async => controller.closeDialog(),
                ),
              ),
              const SizedBox(width: 15,),
              Expanded(
                child: AppButton(
                  height: 50,
                  withLoading: false,
                  radius: 15,
                  margin: EdgeInsets.zero,
                  style: Get.textTheme.bodyMedium!.copyWith(color: AppStyle.whiteColor,fontWeight: FontWeight.w700),
                  text: 'Check'.tr,
                  onTap: ()async => controller.checkCodeLoading?null:controller.checkCode(),
                  moreWidget: Obx(
                        ()=> controller.checkCodeLoading?SizedBox(
                        width: 20,
                        height: 15,
                        child: Padding(
                          padding: const EdgeInsetsDirectional.only(end: 4),
                          child: CircularProgressIndicator(color: AppStyle.whiteColor,strokeWidth: 2),
                        ),
                    ):SizedBox(),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 12),
      ],
    );
  }
}

