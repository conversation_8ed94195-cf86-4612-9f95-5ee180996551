import 'package:all_in_one/core/style/inputs.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


class AppDropDownWidget extends StatefulWidget {
  String? chosenValue;
  List<String>? listValue;
  String? hintText;
  String? errorText;
  String? labelText;
  bool? isEnabled;
  final Function(String)? afterSelection;
  final Function(int)? selectedIndex;
  Color? focusBorder;
  Color? iconColor;
  double? radius;
  double height;
  double? width;
  double? padding;
  double borderWidth;
  final String? Function(String?)? validator;

  AppDropDownWidget(
      {this.radius = 25,
      this.height = 50,
      this.borderWidth = 1,
      this.focusBorder,
      this.padding = 10,
      this.iconColor,
      this.errorText,
      this.validator,
      this.afterSelection,
      this.selectedIndex,
      this.isEnabled =true,
      this.listValue,
      this.chosenValue,
      this.hintText,
      this.labelText,
      this.width});
  @override
  _dropDownWidgtState createState() => _dropDownWidgtState(Value: chosenValue);
}

class _dropDownWidgtState extends State<AppDropDownWidget> {
  String? Value;
  _dropDownWidgtState({this.Value});

  double getHeight(){
    return (widget.height-15)/2;
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(inputDecorationTheme: AppInputFieldThemes.mainThemeDecoration()),
      child: DropdownButtonFormField(
        isExpanded: true,
        menuMaxHeight: 200,
        isDense: true,
        decoration: InputDecoration(
          hintText: widget.hintText??null,
          label: widget.labelText==null ? null : Text(widget.labelText!),
          errorText: widget.errorText
        ),
        borderRadius: BorderRadius.circular(widget.radius!),
        focusColor: widget.focusBorder,
        value: Value==''||Value==null?null:Value,
        style: TextStyle(color: widget.focusBorder),
        icon: const Icon(Icons.arrow_drop_down),
        iconEnabledColor: widget.iconColor ?? AppStyle.greyColor,
        items: widget.listValue!.map<DropdownMenuItem<String>>((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: Text(
              value,
              overflow: TextOverflow.ellipsis,
              style: Get.theme.textTheme.bodySmall!.copyWith(color: AppStyle.blackColor,),
            ),
          );
        }).toList(),
        onChanged: widget.isEnabled != true
            ? null
            : (String? value) {
                setState(() {
                  Value = value!;
                  if(widget.afterSelection != null){
                    widget.afterSelection!(value);
                  }
                  if(widget.selectedIndex != null){
                    widget.selectedIndex!(widget.listValue!.indexOf(value));
                  }
                });
              },
        validator: widget.validator ?? null,
      ),
    );
  }
}
