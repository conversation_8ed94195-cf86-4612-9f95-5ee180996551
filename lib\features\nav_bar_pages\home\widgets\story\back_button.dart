import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';

class BackButton extends StatelessWidget {
  const BackButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Align(
      alignment: AlignmentDirectional.topStart,
      child: Container(
        margin: const EdgeInsets.only(top: 17, left: 15),
        decoration: BoxDecoration(boxShadow: [
          BoxShadow(color: AppStyle.lightBackgroundColor, blurRadius: 1.0),
        ], shape: BoxShape.circle, color: AppStyle.blackColor),
        child: IconButton(
            onPressed: Get.back,
            icon: Icon(
              Icons.close,
              color: AppStyle.whiteColor,
            )),
      ),
    ));
  }
}
