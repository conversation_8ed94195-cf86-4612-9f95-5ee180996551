import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/validator.dart';
import 'package:all_in_one/core/widgets/app_text_feild.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/features/payment/mtn/check_phone/controller.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';

class MtnCheckPhonePage extends GetView<MtnCheckPhoneController> {
  const MtnCheckPhonePage({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(120),
        child: AppBarWidget(
          centerWidget: Text(
            "mtn cash".tr,
            style: Theme.of(context).textTheme.titleLarge!.copyWith(
                color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
          ),
          trailingIcon: SizedBox(
            width: 35,
            height: 35,
          ),
          leadingIcon: Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          trailingOnTap: () {},
          leadingOnTap: () {
            Get.back();
          },
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 64.0, horizontal: 22),
          child: Form(
            key: controller.formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("mtn phone".tr, style: Get.textTheme.titleMedium),
                const SizedBox(height: 16),
                Obx(() => AppTextField(
                      controller.phone,
                      type: FieldTypeEnum.MainTheme,
                      keyboardType: TextInputType.phone,
                      validator: (val) => Validator.phoneValidation(val!),
                      errorText: controller.error.value,
                    )),
                const Spacer(),
                AppButton(
                  text: "Subscription".tr,
                  onTap: controller.checkPhone,
                  radius: 10,
                  style: Get.textTheme.titleMedium
                      ?.copyWith(color: AppStyle.whiteColor),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
