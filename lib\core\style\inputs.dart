import 'package:all_in_one/core/style/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

abstract class AppInputFieldThemes {

  static InputDecorationTheme mainThemeDecoration() {
    return InputDecorationTheme(
      filled: true,
      focusColor: AppStyle.secondaryColor,
      fillColor: AppStyle.textFieldColor.withOpacity(0.2),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
      errorStyle: TextStyle(fontSize: 13, color: AppStyle.redColor,fontWeight: FontWeight.w500),
      hintStyle: TextStyle(fontSize: 15, color: AppStyle.lightBlackColor,fontWeight: FontWeight.w500),
      disabledBorder: OutlineInputBorder(borderSide: BorderSide(width: 1, color: Colors.transparent)),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.primaryColor),
        borderRadius: BorderRadius.circular(10),
      ),
      enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.transparent),
          borderRadius: BorderRadius.circular(10)
      ),
      errorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppStyle.redColor),
          borderRadius: BorderRadius.circular(10)
      ),
      focusedErrorBorder: OutlineInputBorder(
          borderSide: BorderSide(color: AppStyle.redColor),
          borderRadius: BorderRadius.circular(10)
      ),
    );
  }

  static InputDecorationTheme borderLessDecoration() {
    return InputDecorationTheme(
      focusColor: AppStyle.secondaryColor,
      hintStyle: Get.textTheme.bodyMedium!.copyWith(color: AppStyle.secondaryColor),
      focusedBorder: InputBorder.none,
      enabledBorder: InputBorder.none,
      border: InputBorder.none,
      disabledBorder: InputBorder.none,
      errorBorder: InputBorder.none,
      focusedErrorBorder: InputBorder.none,
      labelStyle: Get.theme.textTheme.bodySmall!.copyWith(color: AppStyle.blackColor,)
    );
  }


  static InputDecorationTheme labelThemeDecoration() {
    return InputDecorationTheme(
      focusColor: AppStyle.secondaryColor,
      errorStyle: TextStyle(height: 0, color: AppStyle.redColor),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
      hintStyle: TextStyle(fontSize: 15, color: AppStyle.secondaryColor),
      labelStyle: Get.theme.textTheme.bodySmall!.copyWith(color: AppStyle.blackColor,),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.lightGreyColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.lightGreyColor),
      ),
      border: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.lightGreyColor),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.lightGreyColor),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.redColor),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.redColor),
      ),
    );
  }

  static InputDecorationTheme dropDownDecoration() {
    return InputDecorationTheme(
      focusColor: AppStyle.secondaryColor,
      errorStyle: TextStyle(height: 0, color: AppStyle.redColor),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 18),
      hintStyle: TextStyle(fontSize: 15, color: AppStyle.secondaryColor),
      labelStyle: Get.theme.textTheme.bodySmall!.copyWith(color: AppStyle.blackColor,),
      focusedBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.lightGreyColor),
      ),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.lightGreyColor),
      ),
      border: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.lightGreyColor),
      ),
      disabledBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.lightGreyColor),
      ),
      errorBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.redColor),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderSide: BorderSide(color: AppStyle.redColor),
      ),
    );
  }

}
