import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';

 
class DialogHelper {
  static Future<void> showDialog({
    required Widget dialogBody,
  }) async {
    Get.dialog(
        Scaffold(
          backgroundColor: Colors.transparent,
          body: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Align(
              alignment: Alignment.center,
              child: Container(
                padding: const EdgeInsets.all(15),
                margin: EdgeInsets.only(left: 30, right: 30, top: Get.height * 0.15),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    color: AppStyle.whiteColor),
                child: dialogBody,
              ),
            ),
          ),
        ),
        barrierDismissible: false,
        useSafeArea: false
    );
  }

}
