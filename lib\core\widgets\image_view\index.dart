import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../style/style.dart';
import '../image.dart';
import 'controller.dart';

class ImageViewPage extends StatelessWidget {
  final String path;
  final ImageType type;

  const ImageViewPage({super.key, required this.path, required this.type});
  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    ImageViewPageController controller = Get.put(ImageViewPageController(path));
    return Dismissible(
      key: Key('dismiss'),
      direction: DismissDirection.vertical,
      onDismissed: (_) => Get.back(),
      child: Scaffold(
        // floatingActionButton: FloatingActionButton(
        //   onPressed: () => controller.saveImage(context),
        //   backgroundColor: AppStyle.primaryColor,
        //   child: Obx(() => controller.loading
        //       ? CircularProgressIndicator(color: AppStyle.whiteColor)
        //       : Icon(
        //           Icons.download,
        //           color: AppStyle.whiteColor,
        //         )),
        // ),
        body: GestureDetector(
          onTap: () => controller.whiteBackground = !controller.whiteBackground,
          child: InteractiveViewer(
            child: Obx(
              () => AnimatedContainer(
                width: Get.width,
                height: Get.height,
                duration: 500.milliseconds,
                decoration: BoxDecoration(
                  color: controller.whiteBackground
                      ? AppStyle.whiteColor
                      : AppStyle.blackColor,
                ),
                child: Center(
                  child: AppImage(
                    path: path,
                    type: type,
                    width: Get.width,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
