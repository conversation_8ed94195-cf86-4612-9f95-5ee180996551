import 'package:all_in_one/core/constants/role.dart';
import 'package:flutter/services.dart';
import 'package:all_in_one/core/style/style.dart';
import 'localization.dart';

abstract class Default {
  static const AppLocalization defaultLocale = AppLocalization.Ar;

  static const Role defaultRole = Role.guest;

  static const String appTitle = 'All In One';
  static const String encryptionKey =
      "pMUh/h7ctDYwfxTfmKnLczl2SqA1dGIyN01K3bnxn4M=";

  static preferredOrientation() {
    SystemChrome.setPreferredOrientations([
      // DeviceOrientation.landscapeRight,
      // DeviceOrientation.landscapeLeft,
      DeviceOrientation.portraitDown,
      DeviceOrientation.portraitUp,
    ]);
  }

  static lightStatusBar() => AppStyle.lightStatusBar();
  static darkStatusBar() => AppStyle.darkStatusBar();
}
