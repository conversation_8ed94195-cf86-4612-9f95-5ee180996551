

import 'package:get/get.dart';

class NewController extends GetxController{

  Duration timerDuration = 4.seconds;

  startTimer() async {
    while (timerDuration.inSeconds > 0) {
      await Future.delayed(1.seconds);
      timerDuration = (timerDuration.inSeconds - 1).seconds;
    }
    if(timerDuration.inSeconds==0)
      Get.back();
  }

  backFromPage(){
    timerDuration = 0.seconds;
    Get.back();
  }

  @override
  void onInit() {
    startTimer();
    super.onInit();
  }
}