import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/cached_image_widget.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/notifications/notifications_page.dart';

class HomeAppBarWidget extends StatelessWidget {
  const HomeAppBarWidget();

  @override
  Widget build(BuildContext context) {
    HomePageController homeController = Get.find();
    return Container(
      color: AppStyle.primaryColor,
      padding: EdgeInsets.only(
          top: Get.mediaQuery.viewPadding.top + 20,
          left: 15,
          right: 15,
          bottom: 20),
      child: Row(
        children: [
          Visibility(
            visible: !homeController.appController.isGuestUser(),
            child: Obx(
              () => CachedImageWidget(
                imageUrl: homeController.appController.user?.avatar.value ?? "",
                width: 35,
                height: 35,
                isCircular: true,
                fit: BoxFit.cover,
              ),
            ),
          ),
          const SizedBox(
            width: 10,
          ),
          Visibility(
            visible: !homeController.appController.isGuestUser(),
            replacement: Text(
              "Welcome".tr,
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                  color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
            ),
            child: Obx(
              () => Text(
                'Welcome Back user'.trParams(
                    {'name': homeController.appController.user!.name.value}),
                style: Theme.of(context).textTheme.titleMedium!.copyWith(
                    color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
              ),
            ),
          ),
          const Spacer(),
          IconButton(
              onPressed: () {
                Navigator.push(
                  context,
                  CupertinoPageRoute(
                    builder: (context) => NotificationsPage(fromNavBar: false),
                  ),
                );
              },
              icon: SvgPicture.asset(
                Assets.icons.notificationIcon.path,
              ))
          // Obx(() => DropdownButtonHideUnderline(
          //       child: DropdownButton2<YearModel>(
          //         customButton: Assets.icons.classIcon.svg(
          //             width: 24,
          //             height: 24,
          //             colorFilter: ColorFilter.mode(
          //                 AppStyle.whiteColor, BlendMode.srcIn)),
          //         onChanged: (value) => homeController.setYear(value),
          //         dropdownStyleData: DropdownStyleData(
          //           width: 350,
          //           padding: const EdgeInsets.symmetric(vertical: 6),
          //           decoration: BoxDecoration(
          //             borderRadius: BorderRadius.circular(4),
          //           ),
          //           offset: const Offset(16, -32),
          //         ),
          //         items: homeController.yearsLoading
          //             ? []
          //             : homeController.years
          //                 .map((e) => DropdownMenuItem<YearModel>(
          //                       child: Padding(
          //                         padding: const EdgeInsets.all(8.0),
          //                         child: Row(
          //                           mainAxisSize: MainAxisSize.min,
          //                           children: [
          //                             if (e.id == homeController.yearsId.value)
          //                               Padding(
          //                                   padding: const EdgeInsetsDirectional
          //                                       .only(end: 8),
          //                                   child: Icon(Icons.check)),
          //                             Text(e.title),
          //                           ],
          //                         ),
          //                       ),
          //                       value: e,
          //                     ))
          //                 .toList(),
          //       ),
          //     )),
        ],
      ),
    );
  }
}
