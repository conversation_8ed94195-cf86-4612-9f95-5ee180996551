import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/no_data_widget.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/add_task_bottom_sheet.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/widgets/tasks_filter.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/widgets/tasks_list_view.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';

import 'controller/tasks_controller.dart';

class TasksPage extends StatelessWidget {
  final bool fromNavBar;
  const TasksPage({required this.fromNavBar});

  @override
  Widget build(BuildContext context) {
    TasksController tasksController = Get.put(TasksController());

    return Scaffold(
      floatingActionButton: CircleAvatar(
        backgroundColor: AppStyle.primaryColor,
        child: IconButton(
            onPressed: () {
              // tasksController.initialTasksPage();
              showModalBottomSheet(
                isScrollControlled: true,
                context: context,
                builder: (context) => AddTaskPageBottomSheet(),
              );
            },
            icon: Icon(Icons.add)),
        // minRadius: 20,
        radius: 30,
      ),
      body: Column(
        children: [
          TasksPageAppBar(fromNavBar: fromNavBar),
          TasksFilter(),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Obx(
                () => tasksController.tasksPageLoading
                    ? TasksListSkelton()
                    : tasksController.filteredTasks.isEmpty
                        ? NoDataWidget(
                            imageWidth: 200,
                            imageHeight: 200,
                          )
                        : TasksListView(tasks: tasksController.filteredTasks),
              ),
            ),
          ),
          const SizedBox(
            height: 12,
          ),
        ],
      ),
    );
  }
}

class TasksListSkelton extends StatelessWidget {
  const TasksListSkelton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: 8,
      itemBuilder: (context, index) => Padding(
        padding: const EdgeInsets.all(8.0),
        child: SkeletonWidget(
          height: 120,
          margin: EdgeInsetsDirectional.symmetric(horizontal: 25),
        ),
      ),
    );
  }
}

class TasksPageAppBar extends StatelessWidget {
  const TasksPageAppBar({
    super.key,
    required this.fromNavBar,
  });

  final bool fromNavBar;

  @override
  Widget build(BuildContext context) {
    return AppBarWidget(
      centerWidget: Text(
        'Tasks'.tr,
        style: Theme.of(context).textTheme.titleLarge!.copyWith(
            color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
      ),
      trailingIcon: SizedBox(
        width: 35,
        height: 35,
      ),
      leadingIcon: fromNavBar
          ? SizedBox(
              width: 35,
              height: 35,
            )
          : Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
      trailingOnTap: () {},
      leadingOnTap: () {
        fromNavBar ? null : Get.back();
      },
    );
  }
}
