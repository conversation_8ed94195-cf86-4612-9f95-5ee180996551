## [0.16.0] - 30 November 2023
- Replace `PaintingBinding` with `ui`

## [0.15.0] - 15 July 2023
- API to change indicator colors.

## [0.14.0] - 17 October 2022
- Ability to hide progress indicators

## [0.13.2] - 22 November 2021
- Upgraded rxdart to latest version
- Remove unnecessary null assertions

## [0.13.1] - 22 April 2021
Fixed null errors.

## [0.12.8] - 4 Feb 2021
Merged #84 - Fix Error if player<PERSON><PERSON>roll<PERSON> has not been loaded and is null

## [0.12.7] - 24 Jan 2021.
Upgraded rxdart to latest version

## [0.12.6] - 17 Dec 2020.
Merged PR #74. Provision Key to store values. More here https://github.com/blackmann/story_view/pull/74

## [0.12.5] - 14 Nov 2020.
Upgraded video_player to latest version

## [0.12.4] - 29 Oct 2020.
Upgraded flutter_cache_manager to latest version

## [0.12.3] - 14 May 2020.
`fontSize` has been replaced with `textStyle`. Minor breaking change.

## [0.12.2] - 26 Apr 2020.

Update dependencies.

## [0.12.1] - 25 Apr 2020.

Fix issue #40 and #41. Refactor interaction events to use controller.

## [0.12.0] - 24 Apr 2020.

Vertical gesture support. Code clean up with minor breaking changes. Promoted the use of story controller.

## [0.11.5] - 23 Apr 2020.

Fix issue with video stories not pausing while loading.

## [0.11.4] - 15 March 2020.

Duration API in shorthand methods. Roundness of inline gifs fixed.

## [0.11.3] - 14 March 2020.

Fix swipe down pause issue on fullscreen stories.

## [0.11.2] - 21 January 2020.

Fix hardcoded `imageFit` for StoryImage.

## [0.11.1] - 21 January 2020.

Bumped rxdart version.

## [0.11.0] - 13 October 2019.

Video media support.

## [0.10.0] - 2 September 2019.

Animated Gifs support.
Ability to wait for images to load before stories proceed.
Comes with controller to manually control playback.

## [0.9.3] - 29 July 2019.

Improvement for issue #1 fix.

## [0.9.2] - 29 July 2019.

Fix issue #1: onTap pause error for last page

## [0.9.1] - 22 June 2019.

Pub library health complain on description length. Fixed.

## [0.9.0] - 22 June 2019.

Initial release
