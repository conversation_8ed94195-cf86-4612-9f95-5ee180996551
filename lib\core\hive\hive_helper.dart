import 'package:hive/hive.dart';
import 'package:all_in_one/core/hive/adapters/subject_section_adapter.dart';
import 'adapters/subject_adapter.dart';

class HiveHelper {
  static String appLabel = "studyloop";
  static String downloadsLabel = "downloads";

  static late Box<SubjectAdapter> appBox;
  static late Box<double> downloads;

  static openBox() async {
    appBox = await Hive.openBox<SubjectAdapter>(appLabel);
    downloads = await Hive.openBox<double>(downloadsLabel);
  }

  // static closeBox() {
  //   Hive.deleteBoxFromDisk(appLabel);
  //   Hive.deleteBoxFromDisk(downloadsLabel);
  // }

  static addSubjectToHive(SubjectAdapter subject) async {
    await appBox.put(subject.id.toString(), subject);
  }

  static Future<List<SubjectAdapter>> getAllSubjects() async {
    return await appBox.values.toList();
  }

  static int itemsCount() {
    return appBox.length;
  }

  // static void removeAllDataFromDB() async {
  //   var keys = appBox.keys.toList();
  //   var downloadKeys = downloads.keys.toList();

  //   await appBox.deleteAll(keys);
  //   await downloads.deleteAll(downloadKeys);
  // }

  static deleteSubjectFromHive(SubjectAdapter subject) async {
    var key = subject.key;
    await appBox.delete(key);
  }

  static updateSubject(SubjectAdapter subject) async {
    await subject.save();
  }

  static Future<bool> checkIfSubjectExist(String key) async {
    return await appBox.containsKey(key);
  }

  static Future<bool> checkIfSectionExist(int subjectId, int sectionId) async {
    SubjectAdapter subject = getSubjectById(subjectId);
    for (int i = 0; i < subject.sections.length; i++) {
      if (subject.sections[i].id == sectionId) return true;
    }
    return false;
  }

  static SubjectAdapter getSubjectById(int id) {
    return appBox.get(id.toString())!;
  }

  static SubjectSectionAdapter? getSectionById(int subjectId, int sectionId) {
    SubjectAdapter subject = getSubjectById(subjectId);
    for (int i = 0; i < subject.sections.length; i++) {
      if (subject.sections[i].id == sectionId) return subject.sections[i];
    }
    return null;
  }

  static int getSectionIndex(int subjectId, int sectionId) {
    SubjectAdapter subject = getSubjectById(subjectId);
    for (int i = 0; i < subject.sections.length; i++) {
      if (subject.sections[i].id == sectionId) return i;
    }
    return 1000;
  }

  static int getLessonIndex(SubjectSectionAdapter section, int lessonId) {
    for (int i = 0; i < section.lessons.length; i++) {
      if (section.lessons[i].id == lessonId) return i;
    }
    return 1000;
  }

  static deleteBoxContent() {
    appBox.clear();
  }
}
