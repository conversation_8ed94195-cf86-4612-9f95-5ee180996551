import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

class AppGridView extends StatelessWidget {
  final int itemsCount;
  final Widget Function(BuildContext, int) itemBuilder;
  final EdgeInsets? padding;
  final bool? isScrollable;
  final int? crossAxisCount;
  final double? mainAxisSpacing;
  final double? crossAxisSpacing;
  final ScrollController? scrollController;
  const AppGridView({this.scrollController,required this.itemsCount,required this.itemBuilder,this.padding = EdgeInsets.zero,this.isScrollable = false, this.crossAxisCount=2, this.crossAxisSpacing =15, this.mainAxisSpacing =15});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      controller: scrollController,
      child: AnimationLimiter(
        child: AlignedGridView.count(
          shrinkWrap: true,
          physics: isScrollable!?const AlwaysScrollableScrollPhysics() :const NeverScrollableScrollPhysics(),
          crossAxisCount: crossAxisCount!,
          mainAxisSpacing: mainAxisSpacing!,
          crossAxisSpacing: crossAxisSpacing!,
          clipBehavior: Clip.none,
          padding: padding!,
          itemCount: itemsCount,
          itemBuilder: itemBuilder,
        ),
      ),
    );
  }
}
