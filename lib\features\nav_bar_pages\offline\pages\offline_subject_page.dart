import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/app_animation/item_animation.dart';
import 'package:all_in_one/core/widgets/app_grid_view.dart';
import 'package:all_in_one/core/widgets/no_data_widget.dart';
import 'package:all_in_one/features/nav_bar_pages/offline/offline_controller.dart';
import 'package:all_in_one/features/profile/my_subjects/widgets/subject_grid_item_shimmer.dart';
import 'package:all_in_one/features/profile/my_subjects/widgets/subject_grid_item_widget.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';

import 'offline_subject_detail_page.dart';

class OfflineSubjectPage extends StatelessWidget {
  const OfflineSubjectPage();

  @override
  Widget build(BuildContext context) {
    OfflineController controller = Get.put(OfflineController());
    return Scaffold(
      body: Column(
        children: [
          AppBarWidget(
            leadingIcon: Icon(
              Icons.arrow_back,
              color: Colors.white,
            ),
            centerWidget: Text(
              'My Videos'.tr,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
            ),
            trailingIcon: SizedBox(
              width: 35,
              height: 35,
            ),
            trailingOnTap: () {},
            leadingOnTap: () {
              Get.back();
            },
          ),
          const SizedBox(
            height: 10,
          ),
          Expanded(
              child: Obx(
            () => (!controller.dataLoading && controller.subjects.isEmpty)
                ? NoDataWidget(
                    imageWidth: 200,
                    imageHeight: 200,
                  )
                : AppGridView(
                    itemsCount:
                        controller.dataLoading ? 6 : controller.subjects.length,
                    mainAxisSpacing: 12,
                    crossAxisSpacing: 12,
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    itemBuilder: (BuildContext context, int index) {
                      return ItemAnimation(
                        index: index,
                        elementCount: controller.dataLoading
                            ? 8
                            : controller.subjects.length,
                        child: controller.dataLoading
                            ? const SubjectGridItemShimmer()
                            : SubjectGridItemWidget(
                                id: controller.subjects[index].id,
                                subjectName: controller.subjects[index].title,
                                teacherName:
                                    controller.subjects[index].teacherName,
                                media: controller.subjects[index].media,
                                onTap: () => Get.to(OfflineSubjectDetailPage(
                                  subject: controller.subjects[index],
                                )),
                              ),
                      );
                    },
                  ),
          )),
        ],
      ),
    );
  }
}
