import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/app_animation/item_animation.dart';
import 'package:all_in_one/core/widgets/no_data_widget.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';
import 'package:all_in_one/features/nav_bar_pages/notifications/notifications_controller.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';

import 'widgets/notification_list_item_widget.dart';

class NotificationsPage extends StatelessWidget {
  final bool fromNavBar;
  const NotificationsPage({required this.fromNavBar});

  @override
  Widget build(BuildContext context) {
    NotificationsController controller = Get.put(NotificationsController());
    return Scaffold(
      body: Column(
        children: [
          AppBarWidget(
            centerWidget: Text(
              'Notifications'.tr,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(
                  color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
            ),
            trailingIcon: SizedBox(
              width: 35,
              height: 35,
            ),
            leadingIcon: fromNavBar
                ? SizedBox(
                    width: 35,
                    height: 35,
                  )
                : Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                  ),
            trailingOnTap: () {},
            leadingOnTap: () {
              if (!fromNavBar) {
                Get.back();
              }
            },
          ),
          Expanded(
            child: RefreshIndicator(
                onRefresh: () async => controller.loadNotificationData(),
                child: Obx(
                  () => (controller.dataLoading || (!controller.dataLoading &&controller.notifications.isNotEmpty))
                      ? SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          child: AnimationLimiter(
                            child: ListView.builder(
                              itemCount: controller.dataLoading
                                  ? 8
                                  : controller.notifications.length,
                              clipBehavior: Clip.none,
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              itemBuilder: (context, index) {
                                return ItemAnimation(
                                    index: index,
                                    elementCount: controller.dataLoading
                                        ? 8
                                        : controller.notifications.length,
                                    child: controller.dataLoading
                                        ? Padding(
                                            padding: EdgeInsets.only(
                                                top: 10,
                                                bottom: 8,
                                                left: 20,
                                                right: 20),
                                            child: SkeletonWidget(
                                              height: 80,
                                              radius: 12,
                                            ),
                                          )
                                        : NotificationListItemWidget(
                                            notificationModel:
                                                controller.notifications[index],
                                          ));
                              },
                            ),
                          ))
                      : (!controller.dataLoading &&
                          controller.notifications.isEmpty)?Center(
                          child: NoDataWidget(
                            imageWidth: 200,
                            imageHeight: 200,
                          ),
                        ):const SizedBox(),
                )),
          )
        ],
      ),
    );
  }
}
