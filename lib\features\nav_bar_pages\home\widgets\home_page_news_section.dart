import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_new_model.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/story/story_preview_widget.dart';

import '../new_page.dart';

class HomePageNewsSection extends StatelessWidget {
  const HomePageNewsSection();

  @override
  Widget build(BuildContext context) {
    HomePageController controller = Get.find();
    return SliverVisibility(
      visible: controller.homeResponse.news.isNotEmpty,
      sliver: SliverToBoxAdapter(
        child: SizedBox(
            width: Get.width,
            height: 120,
            child: Padding(
              padding: const EdgeInsetsDirectional.only(start: 15),
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: controller.homeResponse.news.length,
                itemBuilder: (context, index) => NewItemPreviewWidget(
                    onTap: () => _handleSelectNewToShow(
                        controller.homeResponse.news[index], index),
                    newModel: controller.homeResponse.news[index]),
              ),
            )),
      ),
    );
  }

  _handleSelectNewToShow(HomeNewModel newModel, int index) {
    HomePageController controller = Get.find();
    if (newModel.mediaList.isNotEmpty) {
      Get.to(
        () => NewPage(),
      );
      controller.selectNewToShow(index, controller.homeResponse.news);
    }
  }
}
