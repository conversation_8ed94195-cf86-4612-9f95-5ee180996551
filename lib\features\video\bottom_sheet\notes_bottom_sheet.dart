import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/no_data_widget.dart';
import 'package:all_in_one/features/course/contollers/note_controller.dart';

import 'add_note_dialog.dart';

class ShowNotesBottomSheet {
  static void showBottomSheet(BuildContext context) {
    showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return NotesBottomSheet();
        });
  }
}

class NotesBottomSheet extends StatelessWidget {
  const NotesBottomSheet();

  @override
  Widget build(BuildContext context) {
    NoteController controller = Get.find();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 25, horizontal: 24),
      child: SingleChildScrollView(
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: () => ShowAddNoteDialog(context),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 12,
                    backgroundColor: AppStyle.primaryColor,
                    child: Icon(
                      Icons.add,
                      size: 20,
                    ),
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  Text(
                    'Add note'.tr,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppStyle.primaryColor,
                        fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            Obx(() => controller.notes.isEmpty
                ? Center(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      child: NoDataWidget(
                        imageWidth: 50,
                        imageHeight: 50,
                      ),
                    ),
                  )
                : Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      for (int i = 0; i < controller.notes.length; i++)
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Row(
                            children: [
                              Container(
                                width: 8,
                                height: 8,
                                decoration: BoxDecoration(
                                    color: i % 2 == 0
                                        ? AppStyle.primaryColor
                                        : AppStyle.lightGreyColor,
                                    shape: BoxShape.circle),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                child: Text(
                                  '${controller.notes[i].note}',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyLarge
                                      ?.copyWith(
                                          color: AppStyle.lightBlackColor,
                                          fontWeight: FontWeight.bold),
                                ),
                              ),
                              IconButton(
                                  onPressed: () async => controller
                                      .deleteNote(controller.notes[i].id),
                                  icon: Assets.icons.delete.svg())
                            ],
                          ),
                        ),
                    ],
                  )),
          ],
        ),
      ),
    );
  }
}
