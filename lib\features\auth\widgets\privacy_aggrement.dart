import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';

class PrivacyAgreement extends StatelessWidget {
  final bool isActive;
  final Function(bool?)? onChange;

  const PrivacyAgreement({super.key, this.isActive = false, this.onChange});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Checkbox(value: isActive, onChanged: (val) => onChange?.call(val)),
        const SizedBox(width: 12),
        Expanded(
          child: Text.rich(TextSpan(children: [
            TextSpan(text: "By signing up. you agree to the".tr),
            const TextSpan(text: '  '),
            TextSpan(
                text: "Usage Policy".tr,
                recognizer: TapGestureRecognizer()
                  ..onTap = () => Nav.to(Pages.provisions),
                style:  TextStyle(color: AppStyle.primaryColor)),
            const TextSpan(text: '.'),
          ])),
        )
      ],
    );
  }
}
