import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:get/get.dart';

class UserModel {
  int id;
  RxString name;
  RxString avatar;
  String phone;
  String dialCountryCode;
  String? birthday;
  RxString year;
  String college;
  String major;
  int? cityId;
  int yearId;
  int collegeId;
  int majorId;

  UserModel({
    required this.id,
    required this.phone,
    required this.name,
    required this.avatar,
    required this.birthday,
    required this.cityId,
    required this.yearId,
    required this.year,
    required this.college,
    required this.major,
    required this.majorId,
    required this.collegeId,
    required this.dialCountryCode,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        id: json['id'],
        name: RxString(json['name']),
        dialCountryCode: Get.find<AppController>().isInReview
            ? "001"
            : json['dial_country_code'],
        avatar: RxString(json['avatar']['original_url'] ?? ''),
        phone: json['phone'],
        cityId: json['city_id'] == '' ? null : json['city_id'],
        yearId: json['year_id'],
        college: Get.find<AppController>().isInReview?"": json['college'],
        collegeId: json['college_id'],
        major: json['major'],
        majorId: json['major_id'],
        birthday: json['birthday'],
        year: RxString(json['year']),
      );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name.value;
    data['avatar'] = {'original_url': this.avatar.value};
    data['dial_country_code'] =
        Get.find<AppController>().isInReview ? "001" : this.dialCountryCode;
    data['phone'] = this.phone;
    data['year_id'] = this.yearId;
    data['year'] = this.year.value;
    data['birthday'] = this.birthday;
    data['city_id'] = this.cityId;
    data['college'] = this.college;
    data['college_id'] = this.collegeId;
    data['major'] = this.college;
    data['major_id'] = this.collegeId;

    return data;
  }
}
