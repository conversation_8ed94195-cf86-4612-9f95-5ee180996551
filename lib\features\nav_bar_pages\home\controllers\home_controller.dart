import 'dart:convert';
import 'dart:developer';

import 'package:all_in_one/core/repository/fake_repo.dart';
import 'package:all_in_one/core/utils/encription_helper.dart';
import 'package:all_in_one/features/course/contollers/course_controller.dart';
import 'package:all_in_one/features/video/controller/network_video_player_controller.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/constants/role.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/app/user.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/auth/signup/models/class_model.dart';
import 'package:all_in_one/features/course/contollers/subscription_controller.dart';
import 'package:all_in_one/features/course/models/subject_model.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_new_model.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_response.dart';
import 'package:all_in_one/features/nav_bar_pages/notifications/notifications_page.dart';

class HomePageController extends GetxController {
  DataController dataController = Get.find();
  AppController appController = Get.find();
  List<SubjectModel> subjects = [];
  RxBool subjectsLoading = true.obs;
  FileDownloader fileDownloader = FileDownloader();

  SubscriptionController subscriptionController =
      Get.put(SubscriptionController());

  List<HomeNewModel> news = [];
  int currentIndex = -1;

  late HomeResponse homeResponse;
  RxBool _dataLoading = true.obs;
  get dataLoading => this._dataLoading.value;
  set dataLoading(value) => this._dataLoading.value = value;

  checkAllDownloads() async {
    // TODO - FIXME -
    log('hello');
    final records = await fileDownloader.database.allRecords();
    log('checkAllDownloads');
    for (var value in records) {
      if (value.task.metaData.isNotEmpty) {
        final metaData = json.decode(value.task.metaData);
        log(metaData.toString());
        log(value.status.toString());

        if (value.status == TaskStatus.complete) {
          log('lesson1${metaData['lesson_id'].toString()}');

          final controller = Get.put(NetworkVideoPlayerController(),
              tag: "${metaData['lesson_id']}");
          log('lesson2${metaData['lesson_id'].toString()}');

          controller.initialControllerVariable(
            lessonName: metaData['lesson_name'],
            lessonSubTitle: metaData['lesson_subtitle'],
            lessonId: metaData['lesson_id'],
            sectionId: metaData['section_id'],
            teacherAvatar: metaData['teacher_avatar'],
            subjectId: metaData['subject_id'],
            subjectBanner: metaData['subject_banner'],
            teacherName: metaData['teacher_name'],
            subjectTitle: metaData['subject_name'],
            sectionTitle: metaData['section_name'],
            videoUrls: List<String>.from(metaData['video_urls'] ?? []),
          );
          log(controller.lessonId.toString());
          await encryptFile(appController.videosDirectoryPath,
              "${metaData['lesson_id']}_${metaData['lesson_name']}");

          await controller.saveLessonIntoDB();
          controller.downloadVideoLoading = false;
          controller.downloadingComplete = true;
        }
      } else {}
    }

    Future.delayed(const Duration(seconds: 3), () async {
      await fileDownloader.database.deleteAllRecords();
    });
  }

  Future<void> loadHomeData() async {
    dataLoading = true;
    ResponseModel response;
    if (!appController.isInReview) {
      response = await dataController.getData(
        url: API.homeApi,
      );
      if (response.success) {
        homeResponse = HomeResponse.fromJson(response.data);
        dataLoading = false;
      }
    } else {
      dataLoading = false;
    }
  }

  selectNewToShow(int index, List<HomeNewModel> news) {
    this.currentIndex = index;
    this.news = news;
  }

  notificationOnTap(context) {
    if (appController.role == Role.guest) {
      appController.showToast(context,
          message: 'Please Login First'.tr, status: ToastStatus.warning);
    } else {
      Get.to(NotificationsPage(
        fromNavBar: false,
      ));
    }
  }

  @override
  void onInit() async {
    // HiveHelper.removeAllDataFromDB();
    !await appController.internetChecker();
    await initialHome();
    await loadYearsData();
    super.onInit();
  }

  initialHome() async {
    await loadHomeData();
    // loadSliderData();
  }

  List<YearModel> years = [];
  late RxInt yearsId = Get.find<AppController>().user?.yearId.obs ?? 1.obs;
  String chosenYear = '';
  RxBool _yearsLoading = false.obs;
  get yearsLoading => this._yearsLoading.value;
  set yearsLoading(value) => this._yearsLoading.value = value;

  void setYear(YearModel? yearModel) async {
    ResponseModel response;
    try {
      response = await dataController.postData(
        url: API.updateProfile,
        body: {"year_id": yearModel!.id},
      );
      if (response.success) {
        AppController appController = Get.find();
        UserModel? userModel = appController.user;
        userModel?.yearId = yearModel.id;
        yearsId(yearModel.id);
        if (userModel != null) {
          appController.setUser(userModel);
        }
        loadHomeData();
        appController.showToast(Get.context!,
            message: response.data[0], status: ToastStatus.success);
      } else if (response.status == 403 || response.status == 401) {
        appController.removeUserData();
        appController.showToast(Get.context!,
            message: response.message, status: ToastStatus.fail);
        Nav.offAll(Pages.login);
      } else if (response.code == ErrorCode.VALIDATION_ERROR ||
          response.errors != null ||
          response.errors!.isNotEmpty) {
        Get.back();
        appController.showToast(Get.overlayContext!,
            message: response.errorsAsString);
      } else {
        Get.back();
        appController.showToast(Get.context!,
            message: response.message!, status: ToastStatus.fail);
      }
    } catch (e) {
      Get.back();
    }
  }

  loadYearsData() async {
    years = [];
    yearsLoading = true;
    ResponseModel response;
    if (!appController.isInReview) {
      response = await dataController.getData(
        url: API.yearsApi,
      );
      if (response.success) {
        response.data.forEach((element) {
          YearModel year = YearModel.fromJson(element);
          if (year.id == appController.user?.yearId) chosenYear = year.title;
          years.add(year);
        });
        yearsLoading = false;
      } else {
        loadYearsData();
      }
    } else {
      years = FakeRepo.years;
      yearsLoading = false;
    }
  }

  @override
  void onClose() {
    super.onClose();
  }
}
