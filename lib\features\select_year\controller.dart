import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/features/auth/signup/models/class_model.dart';

class SelectYearController extends GetxController{
  RxBool yearsLoading = true.obs;
  final String majorId = Get.parameters['id']!;
  List<YearModel> years = [];
  final DataController dataController = Get.find();

  getYearsByMajor()async{
    yearsLoading(true);
    ResponseModel response = await dataController.getData(url: API.yearsApi,param: {
      "major_id":majorId
    });
    if(response.success){
      years = List<YearModel>.from(response.data.map((item)=>YearModel.fromJson(item)));
      yearsLoading(false);
    }
  }
@override
void onInit() {
  getYearsByMajor();
    super.onInit();
  }

}