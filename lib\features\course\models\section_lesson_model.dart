import 'package:all_in_one/core/utils/encription_helper.dart';

class SectionLessonModel {
  int id;
  String title;
  String subTitle;
  String link;
  String link360;
  String audioLink720;
  String videoLink720;
  bool isFree;
  int sectionId;

  SectionLessonModel({
    required this.id,
    required this.title,
    required this.subTitle,
    required this.isFree,
    required this.link360,
    required this.link,
    required this.sectionId,
    required this.audioLink720,
    required this.videoLink720,
  });

  factory SectionLessonModel.fromJson(Map<String, dynamic> json, bool isFree) =>
      SectionLessonModel(
        id: json['id'],
        title: json['title'],
        link360: decryptLink(json['link_360'] ?? ''),
        isFree: isFree == true
            ? true
            : json['is_free'] == 0
                ? false
                : true,
        subTitle: json['sub_title'],
        link: decryptLink(json['link']),
        sectionId: json['lecture_id'],
        audioLink720: decryptLink(json['audio_link_720'] ?? ''),
        videoLink720: decryptLink(json['video_link_720'] ?? ''),
      );
}
