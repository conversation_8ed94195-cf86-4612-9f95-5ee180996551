import 'dart:io';
import 'package:all_in_one/core/repository/fake_repo.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/auth/signup/models/city_model.dart';
import 'package:all_in_one/features/auth/signup/models/class_model.dart';
import 'package:all_in_one/features/auth/signup/models/school_model.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_year_model.dart';
import 'package:all_in_one/features/profile/edit_profile/widgets/permission_dialog.dart';

class EditProfileController extends GetxController {
  AppController appController = Get.find();
  DataController dataController = Get.find();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  TextEditingController name = TextEditingController();
  TextEditingController phone = TextEditingController();
  TextEditingController schoolController = TextEditingController();
  TextEditingController majorController = TextEditingController();

  DateTime? selectedDate;

  List<CityModel> cities = [];
  int? cityId;
  String chosenCity = '';
  RxBool _citiesLoading = false.obs;
  get citiesLoading => this._citiesLoading.value;
  set citiesLoading(value) => this._citiesLoading.value = value;

  List<YearModel> years = [];
  late int yearsId;
  String chosenYear = '';
  RxBool _yearsLoading = false.obs;
  get yearsLoading => this._yearsLoading.value;
  set yearsLoading(value) => this._yearsLoading.value = value;

  List<SchoolModel> schools = [];
  late int schoolId;
  RxBool _schoolsLoading = false.obs;
  get schoolsLoading => this._schoolsLoading.value;
  set schoolsLoading(value) => this._schoolsLoading.value = value;

  List<Major> majors = [];
  RxBool _majorsLoading = false.obs;
  get majorsLoading => this._majorsLoading.value;
  set majorsLoading(value) => this._majorsLoading.value = value;
  int? majorId;
  //########### Errors ###########

  RxnString _yearError = RxnString(null);
  String? get yearError => this._yearError.value;
  set yearError(String? value) => this._yearError.value = value;

  final ImagePicker picker = ImagePicker();
  File? imageFile;
  RxString _imagePath = ''.obs;
  String get imagePath => this._imagePath.value;
  set imagePath(String value) => this._imagePath.value = value;

  imgFromGallery() async {
    bool hasGalleryAccess = !(await Permission.photos.isDenied);
    bool hasStorageAccess = !(await Permission.storage.isDenied);

    if (!hasGalleryAccess || !hasStorageAccess) {
      await Permission.photos.request();
      await Permission.storage.request();
    }
    DialogHelper.showDialog(dialogBody: PermissionDialogForAccessToPhotos(
      onAllow: () async {
        Get.back();

        final pickedFile = await picker.pickImage(source: ImageSource.gallery);
        if (pickedFile != null) {
          imageFile = File(pickedFile.path);
          imagePath = imageFile!.path;
        }
      },
    ));
  }

  void loadMajorsData() async {
    majors = [];
    majorsLoading = true;
    ResponseModel response;
    if (!appController.isInReview) {
      response = await dataController.getData(
        url: API.majorsApi,
      );
      if (response.success) {
        response.data.forEach((element) => majors.add(Major.fromJson(element)));
        majorsLoading = false;
      }
    } else {
      majors = FakeRepo.majors;
      majorsLoading = false;
    }
  }

  void loadCitiesData() async {
    cities = [];
    citiesLoading = true;
    ResponseModel response;
    if (!appController.isInReview) {
      response = await dataController.getData(
        url: API.citiesApi,
      );
      if (response.success) {
        response.data.forEach((element) {
          CityModel city = CityModel.fromJson(element);
          if (city.id == appController.user?.cityId) chosenCity = city.name;
          cities.add(city);
        });
        citiesLoading = false;
      } else {
        loadCitiesData();
      }
    } else {
      cities = FakeRepo.cities;
      citiesLoading = false;
    }
  }

  void loadYearsData() async {
    years = [];
    yearsLoading = true;
    ResponseModel response;
    if (!appController.isInReview) {
      response = await dataController.getData(
        url: API.yearsApi,
      );
      if (response.success) {
        response.data.forEach((element) {
          YearModel year = YearModel.fromJson(element);
          if (year.id == appController.user?.yearId) chosenYear = year.title;
          years.add(year);
        });
        yearsLoading = false;
      } else {
        loadYearsData();
      }
    } else {
      years = FakeRepo.years;
      yearsLoading = false;
    }
  }

  void loadSchoolsData() async {
    schools = [];
    schoolsLoading = true;
    ResponseModel response;
    if (!appController.isInReview) {
      response = await dataController.getData(
        url: API.schoolsApi,
      );
      if (response.success) {
        response.data
            .forEach((element) => schools.add(SchoolModel.fromJson(element)));
        schoolsLoading = false;
      }
    } else {
      schools = FakeRepo.schools;
      schoolsLoading = false;
    }
  }

  upDateProfile(context) async {
    var formData = FormData.fromMap({});
    if (formKey.currentState!.validate()) {
      if (imageFile != null)
        formData.files.add(
            MapEntry('avatar', await MultipartFile.fromFile(imageFile!.path)));
      formData.fields.add(MapEntry('name', name.text));
      // formData.fields.add(MapEntry('phone', phone.text));
      formData.fields
          .add(MapEntry('dial_country_code', appController.dialCountryCode));
      formData.fields.add(MapEntry('role', 'student'));
      formData.fields.add(MapEntry('year_id', yearsId.toString()));
      if (cityId != null)
        formData.fields.add(MapEntry('city_id', cityId.toString()));
      formData.fields.add(MapEntry('school_id', schoolId.toString()));
      ResponseModel response;
      try {
        response = await dataController.postData(
          url: API.updateProfile,
          body: formData,
        );
        if (response.success) {
          HomePageController homePageController = Get.find();
          homePageController
              .setYear(years.firstWhere((element) => element.id == yearsId));
          saveUserData(response.data);
          Get.back();
          appController.showToast(Get.context!,
              message: response.data[0], status: ToastStatus.success);
        } else if (response.status == 403 || response.status == 401) {
          appController.removeUserData();
          appController.showToast(Get.context!,
              message: response.message, status: ToastStatus.fail);
          Nav.offAll(Pages.login);
        } else if (response.code == ErrorCode.VALIDATION_ERROR ||
            response.errors != null ||
            response.errors!.isNotEmpty) {
          Get.back();
          appController.showToast(context, message: response.errorsAsString);
        } else {
          Get.back();
          appController.showToast(Get.context!,
              message: response.message!, status: ToastStatus.fail);
        }
      } catch (e) {
        Get.back();
      }
    } else {
      Get.back();
    }
  }

  void initialFields() {
    imagePath = appController.user?.avatar.value ?? "";
    name.text = appController.user?.name.value ?? "";
    phone.text = appController.user?.phone ?? "";
    schoolController.text = appController.user?.college ?? "";
    yearsId = appController.user?.yearId ?? 1;
    cityId = appController.user?.cityId;
    schoolId = appController.user?.collegeId ?? 1;
  }

  saveUserData(Map<String, dynamic> user) {
    appController.user?.name.value = user['name'];
    if (appController.user?.yearId != yearsId) {
      HomePageController homePageController = Get.find();
      homePageController.loadHomeData();
      appController.user?.yearId = yearsId;
      appController.user?.year.value = chosenYear;
    }
    appController.user?.college = schoolController.text;
    appController.user?.collegeId = schoolId;
    if (cityId != null && appController.user?.cityId != cityId) {
      appController.user?.cityId = cityId!;
    }

    appController.removeUser();
    if (appController.user != null) {
      appController.setUser(appController.user!);
    }
  }

  @override
  void onInit() {
    loadCitiesData();
    loadYearsData();
    loadSchoolsData();
    loadMajorsData();

    initialFields();
    super.onInit();
  }

  @override
  void onClose() {
    name.dispose();
    phone.dispose();
    schoolController.dispose();
    super.onClose();
  }
}
