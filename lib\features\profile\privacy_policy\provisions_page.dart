import 'package:all_in_one/features/auth/widgets/app_auth_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/made_by_ixcoders_widget.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';

import 'provisions_controller.dart';

class ProvisionsPage extends StatelessWidget {
  const ProvisionsPage();

  @override
  Widget build(BuildContext context) {
    ProvisionsController controller = Get.put(ProvisionsController());
    return Scaffold(
      body: AppAuthPage(
        title:"",
        child: Stack(
          children: [
          
            Align(
              alignment: Alignment.bottomCenter,
              child: Container(
                height: Get.height*0.7,
                decoration: BoxDecoration(
                  color: AppStyle.whiteColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                  )
                ),
                padding: EdgeInsets.symmetric(horizontal: 20,vertical: 25),
                child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Usage Policy'.tr,
                        style: Theme.of(context).textTheme.displayLarge?.copyWith(
                            color: AppStyle.primaryColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 15),
                      Expanded(
                        child: Obx(
                            ()=> controller.usagePolicyLoading?
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SkeletonWidget(height: 15,radius: 0,),
                                const SizedBox(height: 12,),
                                SkeletonWidget(height: 15,radius: 0,),
                                const SizedBox(height: 12,),
                                SkeletonWidget(height: 15,radius: 0,),
                                const SizedBox(height: 12,),
                                SkeletonWidget(height: 15,radius: 0,),
                                const SizedBox(height: 12,),
                                SkeletonWidget(height: 15,radius: 0,),
                                const SizedBox(height: 12,),
                                SkeletonWidget(height: 15,radius: 0,),
                                const SizedBox(height: 12,),
                                SkeletonWidget(height: 15,radius: 0,),
                                const SizedBox(height: 12,),
                                SkeletonWidget(height: 15,radius: 0,),
                                const SizedBox(height: 12,),
                                SkeletonWidget(height: 15,radius: 0,),
                                const SizedBox(height: 12,),
                                SkeletonWidget(height: 15,radius: 0,),
                                const SizedBox(height: 12,),
                                SkeletonWidget(height: 15,radius: 0,width: 250,),
                                const SizedBox(height: 12,),
                              ],
                            ):SingleChildScrollView(
                              child: SizedBox(
                                width: Get.width,
                                child: Text(
                                  controller.usagePolicy,
                                  textAlign: TextAlign.start,
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: AppStyle.lightBlackColor,
                                  ),
                                ),
                              ),
                            )
                        ),
                      ),
                      const SizedBox(height: 10,),
                      // MadeByIxcoadersWidget(),
                      AppButton(
                        text: 'Well'.tr,
                        height: 60,
                        radius: 10,
                        withLoading: false,
                        margin: EdgeInsets.zero,
                        style: Get.textTheme.titleMedium!.copyWith(color: AppStyle.whiteColor,fontWeight: FontWeight.bold),
                        onTap: ()async{
                          Get.back();
                        },
                      ),
                    ],
                  ),
              ),
            ),
            PositionedDirectional(
                top: 20,
                start: 25,
                child: InkWell(
                  onTap: ()=> Get.back(),
                  child: CircleAvatar(
                    backgroundColor: Colors.white,
                    radius: 15,
                    child: Icon(Icons.arrow_back,color: AppStyle.primaryColor,),
                  ),
                )
            ),
          ],
        ),
      ),
    );
  }
}
