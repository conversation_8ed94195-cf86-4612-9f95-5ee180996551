import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/core/widgets/made_by_ixcoders_widget.dart';
import 'package:all_in_one/features/course/widgets/dialogs/fatora_subscription_dialog_body_widget.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_page_courses_section.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_page_news_section.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_page_slider_section.dart';

class HomePageBody extends StatelessWidget {
  const HomePageBody({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    AppController appController = Get.find();
    HomePageController controller = Get.find();

    return CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverToBoxAdapter(
            child: const SizedBox(
              height: 18,
            ),
          ),
          HomePageSliderSection(
            banners: controller.homeResponse.banners,
          ),
          SliverToBoxAdapter(
            child: const SizedBox(
              height: 18,
            ),
          ),
          HomePageNewsSection(),
         
          HomePageCoursesSection(),
          HomePageMajorsGridView(),
          // if (!appController.isInReview)
          //   SliverToBoxAdapter(child: YearSubscribtionButton()),
          SliverToBoxAdapter(
            child: const SizedBox(
              height: 16,
            ),
          ),
          //HomePageTasksSection(),
          // const SizedBox(
          //   height: 15,
          // ),
          SliverToBoxAdapter(child: MadeByIxcoadersWidget()),
          SliverToBoxAdapter(
            child: const SizedBox(
              height: 15,
            ),
          ),
        ]);
  }
}

class YearSubscribtionButton extends StatelessWidget {
  const YearSubscribtionButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    HomePageController controller = Get.find();

    return Visibility(
      visible: !controller.appController.isGuestUser(),
      child: AppButton(
        text: 'Year Subscription'.tr,
        height: 60,
        radius: 10,
        withLoading: false,
        margin: EdgeInsets.symmetric(vertical: 5, horizontal: 20),
        style: Get.textTheme.titleMedium!
            .copyWith(color: AppStyle.whiteColor, fontWeight: FontWeight.bold),
        onTap: () async {
          // DialogHelper.showDialog(
          //     dialogBody: FatoraSubscriptionDialogBodyWidget(
          //   type: SubscriptionType.Year,
          //   // name: controller.homeResponse.year.title,
          //   // price: controller.homeResponse.year.price.toString(),
          //   fromCourse: true,
          // ));
        },
      ),
    );
  }
}
