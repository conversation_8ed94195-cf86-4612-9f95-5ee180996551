
import 'package:flutter/material.dart';
// import 'package:flutter_html/shims/dart_ui.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';

class ProfileItemWidget extends StatelessWidget {
  final String text;
  final Widget icon;
  final VoidCallback onTap;
  const ProfileItemWidget({required this.text,required this.icon,required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: Get.width,
        padding: EdgeInsets.symmetric(horizontal: 15,vertical: 20),
        decoration: BoxDecoration(
          color: AppStyle.whiteBackgroundColor,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          children: [
            icon,
            const SizedBox(width: 10,),
            Expanded(
              child: Text(
                text,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                   fontWeight: FontWeight.w700
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
