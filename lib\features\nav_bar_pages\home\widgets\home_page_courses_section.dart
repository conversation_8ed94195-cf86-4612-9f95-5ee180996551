import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>';
import 'package:all_in_one/features/select_year/index.dart';

import 'course_list_item_widget.dart';

class HomePageCoursesSection extends StatelessWidget {
  const HomePageCoursesSection();

  @override
  @override
  Widget build(BuildContext context) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      sliver: SliverToBoxAdapter(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 10),
            Text(
              'Sections'.tr,
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                  color: AppStyle.mediumBlackTextColor,
                  fontWeight: FontWeight.w900),
            ),
            const SizedBox(height: 10),
            
          ],
        ),
      ),
    );
  }
}

class HomePageMajorsGridView extends StatelessWidget {
  const HomePageMajorsGridView({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    HomePageController controller = Get.find();
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      sliver: SliverGrid(
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 2,
            childAspectRatio: 1,
            mainAxisSpacing: 12),
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            return CourseListItemWidget(
              onTap: () {
    Nav.to(Pages.selectYear,params: {"id": controller.homeResponse.majors[index].id.toString()})    ;            
              },
              major: controller.homeResponse.majors[index],
            );
          },
          childCount: controller.homeResponse.majors.length,
        ),
      ),
    );
  }
}
