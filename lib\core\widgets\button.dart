import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/loading.dart';

class AppButton extends StatelessWidget {
  final String text;
  final Function() onTap;
  final bool withLoading;
  final EdgeInsets margin;
  final EdgeInsets padding;
  final TextStyle? style;
  final double? height;
  final double? width;
  final double radius;
  Color? borderColor;
  final Color? fillColor;
  final Color? textColor;
  final Widget? moreWidget;
  final bool showLoadingText;

  AppButton(
      {Key? key,
      required this.text,
      required this.onTap,
      this.style,
      this.height = 45,
      this.width,
      this.withLoading = true,
      this.radius = 0,
      this.fillColor,
      this.textColor,
      Color? borderColor,
      this.moreWidget,
      this.padding = EdgeInsets.zero,
      this.showLoadingText = true,
      this.margin = const EdgeInsets.symmetric(horizontal: 16)}) {
    this.borderColor = borderColor ?? AppStyle.primaryColor;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        if (withLoading) {
          Loading.overlayLoading(context, showText: showLoadingText);
          await onTap();
        } else {
          onTap();
        }
      },
      child: Container(
        width: width,
        height: height,
        margin: margin,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(radius),
            color: fillColor ?? Get.theme.primaryColor,
            border: Border.all(color: borderColor!)),
        alignment: Alignment.center,
        padding: padding,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Visibility(
              visible: moreWidget != null,
              child: moreWidget ?? SizedBox(),
            ),
            Text(text, style: style),
          ],
        ),
      ),
    );
  }
}
