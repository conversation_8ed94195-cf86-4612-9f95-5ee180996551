import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/cached_image_widget.dart';

class SubjectGridItemWidget extends StatelessWidget {
  final int id;
  final String subjectName;
  final String teacherName;
  final String media;
  final VoidCallback onTap;
  const SubjectGridItemWidget({
    required this.id,
    required this.subjectName,
    required this.teacherName,
    required this.onTap,
    required this.media
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: AppStyle.whiteColor,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
                color: Colors.grey.withOpacity(0.2),
                blurRadius: 1.0,
                spreadRadius: 1,
                offset: const Offset(-2, 1) // changes position of shadow
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CachedImageWidget(
              imageUrl: media,
              height: 175,
              width: Get.width,
              fit: BoxFit.cover,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(15),
                topRight: Radius.circular(15)
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 15,vertical: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    subjectName,
                    style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      color: AppStyle.blackColor,
                      fontWeight: FontWeight.w900,
                    ),
                  ),
                  const SizedBox(
                    height: 2,
                  ),
                  Text(
                    teacherName,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppStyle.lightBlackColor,
                        fontWeight: FontWeight.w600
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
