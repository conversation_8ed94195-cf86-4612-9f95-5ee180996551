import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';

class PageError extends StatelessWidget {
  final Function() retry;
  final String error;
  const PageError({super.key, required this.retry, this.error = 'Some Error'});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.only(top: 8),
        child: Wrap(
          alignment: WrapAlignment.center,
          crossAxisAlignment: WrapCrossAlignment.center,
          spacing: 12,
          runSpacing: 6,
          children: [
            Text(
              error,
              style: Theme.of(context).textTheme.titleSmall!.copyWith(fontWeight: AppFontWeight.bold),
            ),
            AppButton(
              text: "Try Again".tr,
              onTap: ()async=> retry(),
              margin: EdgeInsets.symmetric(horizontal: Get.width*0.3),
              withLoading: false,
            ),
            // ElevatedButton(
            //   onPressed: retry,
            //   child: Text('retry'),
            // ),
          ],
        ),
      ),
    );
  }
}
