import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';

class PermissionDialogForAccessToPhotos extends StatelessWidget {
  final Function() onAllow;
  const PermissionDialogForAccessToPhotos({super.key, required this.onAllow});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Assets.icons.gallerysvg.svg(
            height: 64,
            colorFilter:
                ColorFilter.mode(AppStyle.primaryColor, BlendMode.srcIn)),
        const SizedBox(height: 16),
        Text(
          "allow to access to photos to change the profile picture".tr,
          style: Get.textTheme.titleMedium,
        ),
        const SizedBox(height: 16),
        GestureDetector(
          onTap: onAllow,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 12),
            width: Get.width,
            padding: const EdgeInsets.symmetric(vertical: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppStyle.primaryColor),
              color: AppStyle.primaryColor,
            ),
            child: Center(
              child: Text(
                "Allow".tr,
                style: Get.textTheme.bodyMedium
                    ?.copyWith(color: AppStyle.whiteColor),
              ),
            ),
          ),
        ),
        const SizedBox(height: 12),
        GestureDetector(
          onTap: Get.back,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 12),
            width: Get.width,
            padding: const EdgeInsets.symmetric(vertical: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppStyle.primaryColor),
              color: AppStyle.whiteColor,
            ),
            child: Center(
              child: Text(
                "Don't allow".tr,
                style: Get.textTheme.bodyMedium
                    ?.copyWith(color: AppStyle.primaryColor),
              ),
            ),
          ),
        )
      ],
    );
  }
}
