import 'dart:io';

import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/constants/role.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/controllers/global_controller.dart';
import 'package:all_in_one/core/controllers/notification_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/widgets/loading.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/nav_bar_pages/notifications/notifications_page.dart';
import 'package:all_in_one/features/nav_bar_pages/offline/pages/offline_subject_page.dart';
import 'package:all_in_one/features/nav_bar_pages/profile/profile_page.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/tasks_page.dart';
import '../home/<USER>';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/features/alternative_ui/index.dart';

class NavBarController extends GetxController {
  AppController appController = Get.find();
  DataController dataController = Get.find();
  GlobalController globalController = Get.find();
  NotificationController notificationController = Get.find();

  var scaffoldKey = GlobalKey<ScaffoldState>();

  String chosenLanguage = '';
  String chosenCurrency = '';

  Rx<int> _selectedIndex = 0.obs;
  int get selectedIndex => this._selectedIndex.value;
  int taps = 0;

  set selectedIndex(int value) {
    this._selectedIndex.value = value;
  }

  Map<int, Widget> get pages => {
        0: HomePage(),
        1: NotificationsPage(fromNavBar: true),
        2: appController.isInReview
            ? OfflineTaskManager()
            : TasksPage(fromNavBar: true),
        3: OfflineSubjectPage(),
        4: ProfilePage(),
      };

  bottomNavigationOnChange(BuildContext context, int index) {
    if ((index == 1 || index == 2 || index == 3) &&
        appController.role == Role.guest) {
      appController.showToast(context,
          message: 'Please Login First'.tr, status: ToastStatus.warning);
    } else if (index == 4 && appController.role == Role.guest) {
      Nav.offAll(Pages.login);
    } else {
      selectedIndex = index;
    }
  }

  notificationOnTap(context) {
    if (appController.role == Role.guest) {
      appController.showToast(context,
          message: 'Please Login First'.tr, status: ToastStatus.warning);
    } else {
      Get.to(NotificationsPage(
        fromNavBar: false,
      ));
    }
  }

  logOut(context) async {
    Loading.overlayLoading(context);
    ResponseModel response;
    response = await dataController.getData(
      url: API.logout,
    );
    2.seconds.delay();
    Get.back();
    appController.removeUserData();
    Nav.offAll(Pages.login);
    // if(response.success){
    //   // globalController.updateBaseUrl(API.globalBaseUrl);
    //   Get.back();
    //   appController.removeUserData();
    //   appController.showToast(context, message: response.message,status: ToastStatus.success);
    //   Nav.offAll(Pages.login);
    // }else{
    //   Get.back();
    //   appController.showToast(context, message: response.message);
    // }
  }

  deleteAccount(context) async {
    Loading.overlayLoading(context);
    ResponseModel response;
    response = await dataController.deleteData(
      url: API.deleteAccount,
    );
    // 2.seconds.delay();
    // Get.back();
    // appController.removeUserData();
    // Nav.offAll(Pages.login);
    if (response.success) {
      Get.back();
      appController.removeUserData();
      appController.showToast(context,
          message: response.message, status: ToastStatus.success);
      Nav.offAll(Pages.login);
    } else {
      Get.back();
      appController.removeUserData();
      appController.showToast(context, message: response.message);
      Nav.offAll(Pages.login);
    }
  }

  bool isUserGuest() {
    if (appController.role == Role.guest) return true;
    return false;
  }

  closeApp(BuildContext context) {
    if (taps == 1) {
      if (Platform.isAndroid) {
        SystemChannels.platform.invokeMethod('SystemNavigator.pop');
      } else if (Platform.isIOS) {
        exit(0);
      }
    }
    Fluttertoast.showToast(
      msg: 'Press twice to exist'.tr,
      toastLength: Toast.LENGTH_SHORT,
      fontSize: 12,
    );
    taps++;

    Future.delayed(const Duration(milliseconds: 500), () {
      taps = 0;
    });
  }

  // onSelectCurrency(CurrencyModel currency,BuildContext context){
  //   Get.back();
  //   appController.setSelectedCurrency(currency);
  //   selectedIndex =0;
  //   try{
  //     HomePageController controller = Get.find();
  //     controller.initialApis();
  //   }catch(e){
  //
  //   }
  // }

  void initialNotification() async {
    await notificationController.initNotifications();
  }

  @override
  void onInit() {
    super.onInit();
    initialNotification();
    // globalController.loadCountersData();
    // chosenLanguage = appController.getAppLanguageLabel();
  }
}
