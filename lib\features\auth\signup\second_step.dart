import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/validator.dart';
import 'package:all_in_one/core/widgets/app_text_feild.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/features/auth/signup/controller.dart';
import 'package:all_in_one/features/auth/widgets/auth_page_switcher_widget.dart';

class SecondStep extends StatelessWidget {
  const SecondStep({super.key});

  @override
  Widget build(BuildContext context) {
    SignUpPageController controller = Get.find();
    return SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("sign up".tr,style:Theme.of(context).textTheme.headlineMedium?.copyWith(color: AppStyle.blackColor) ,),
                        const SizedBox(height: 10),
                      Padding(
                        padding: const EdgeInsetsDirectional.only(end: 100),
                        child: Text(
                          'create your account to use All In One App'.tr,
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: AppStyle.lightBlackColor,
                          ),
                        ),
                      ),
                      const SizedBox(height: 36,),

                                            Obx(
                            ()=> TextFormField(
                          controller: controller.phone,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(color: AppStyle.lightBlackColor),
                          validator: Validator.notNullValidation,
                          keyboardType: TextInputType.phone,
                          decoration: InputDecoration(
                              hintText: "Phone".tr,
                              errorText: controller.phoneError.isNotEmpty?controller.phoneError:null
                          ),
                        ),
                      ),
                      const SizedBox(height: 16,),

                      AppTextField(controller.password, 
                      isSecure: true,

                      type: FieldTypeEnum.MainTheme,hint: "password".tr,),
                      const SizedBox(height: 16,),

                      AppTextField(controller.passwordConfirmation, 
                      isSecure: true,

                      type: FieldTypeEnum.MainTheme,hint: "password confirmation".tr,),
                      
                      const SizedBox(height: 120,),
                      AppButton(
                        text: 'continue'.tr,
                        height: 58,
                        radius: 10,
                        withLoading: false,
                        margin: EdgeInsets.zero,
                        style: Get.textTheme.titleMedium!.copyWith(color: AppStyle.whiteColor,fontWeight: FontWeight.bold),
                        onTap: ()=> controller.pageController.nextPage(duration: 1.seconds, curve: Curves.easeIn)),
                        const SizedBox(
                        height: 20,
                      ),
                      AuthPageSwitcherWidget(
                        label: 'Already have an account?'.tr,
                        onTapLabel: 'Sign In'.tr,
                        onTap: () => Nav.replacement(Pages.login),
                      ),
                    

                    ]));}}