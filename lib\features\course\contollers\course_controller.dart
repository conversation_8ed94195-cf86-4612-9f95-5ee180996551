import 'dart:developer';
import 'dart:isolate';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:io' as io;
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/core/widgets/loading.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/course/models/section_lesson_model.dart';
import 'package:all_in_one/features/course/models/subject_model.dart';
import 'package:all_in_one/features/course/widgets/dialogs/check_code_dialog_body_widget.dart';
import 'package:all_in_one/features/video/network_video_player_page.dart';

import '../../../core/utils/encription_helper.dart';
import '../../video/downloaded_video_player_page.dart';

class CourseController extends GetxController {
  DataController dataController = Get.find();
  AppController appController = Get.find();
  bool canContactWithTeacher = false;

  late String subjectName;
  late int subjectId;
  late bool fromHome;

  ///check sections are empty
  bool areLessonsEmpty = true;
  bool areFilesEmpty = true;
  bool areNotesEmpty = true;
  bool areQuizzesEmpty = true;

  RxInt _selectedSection = 0.obs;
  get selectedSection => this._selectedSection.value;
  set selectedSection(value) => this._selectedSection.value = value;

  RxInt _selectedOptions = RxInt(0);
  int get selectedOptions => this._selectedOptions.value;
  set selectedOptions(value) => this._selectedOptions.value = value;

  final pageController = PageController(
    initialPage: 0,
  );
  onPageChange(int pageNum) {
    selectedOptions = pageNum;
  }

  optionOnTap(int pageNum) {
    selectedOptions = pageNum;
    pageController.animateToPage(pageNum,
        duration: Duration(milliseconds: 500),
        curve: Curves.fastLinearToSlowEaseIn);
  }

  List<String> options = [
    'Lessons'.tr,
    'Notes'.tr,
    'Files'.tr,
    'Exams'.tr,
  ];

  late SubjectModel subjectModel;
  RxBool _detailLoading = true.obs;
  get detailLoading => this._detailLoading.value;
  set detailLoading(value) => this._detailLoading.value = value;

  void loadSubjectDetail() async {
    detailLoading = true;

    ResponseModel response;
    response = await dataController
        .postData(url: API.subjectApi, body: {'id': subjectId});
    if (response.success) {
      subjectModel = SubjectModel.fromJson(response.data);
      checkAreListsEmpty();
      checkIfUserSubscribeWithResearch();
      detailLoading = false;
    }
  }

  ///todo
  checkIfUserSubscribeWithResearch() {
    for (int i = 0; i < subjectModel.lectures.length; i++) {
      if (subjectModel.lectures[i].isSubscription.value) {
        canContactWithTeacher = true;
        log('canContactWithTeacher i= $i $canContactWithTeacher');
        break;
      }
    }
  }

  int selectedSectionId = 0;
  int selectedIndex = 0;

  subscribe(int sectionId, int index) async {
    selectedSectionId = sectionId;
    selectedIndex = index;
    await DialogHelper.showDialog(
      dialogBody: CheckCodeDialogBodyWidget(),
    );
  }

  checkAreLessonsEmpty() {
    for (int i = 0; i < subjectModel.lecturesCnt; i++) {
      if (subjectModel.lectures[i].lessons.isNotEmpty) {
        areLessonsEmpty = false;
      }
    }
  }

  checkAreFilesEmpty() {
    for (int i = 0; i < subjectModel.lecturesCnt; i++) {
      if (subjectModel.lectures[i].files.isNotEmpty) {
        areFilesEmpty = false;
      }
    }
  }

  checkAreNotesEmpty() {
    for (int i = 0; i < subjectModel.lecturesCnt; i++) {
      if (subjectModel.lectures[i].notes.isNotEmpty) {
        areNotesEmpty = false;
      }
    }
  }

  checkAreQuizzesEmpty() {
    subjectModel.lectures.forEach((section) {
      if (section.hasQuiz) {
        areQuizzesEmpty = false;
        return;
      }
    });
  }

  checkAreListsEmpty() {
    checkAreLessonsEmpty();
    checkAreFilesEmpty();
    checkAreNotesEmpty();
    checkAreQuizzesEmpty();
  }

  lessonOnTap(
      {required BuildContext context,
      required SectionLessonModel lesson,
      required bool isSubscription}) async {
    if (isSubscription || lesson.isFree) {
      String videoName = "${lesson.id}_${lesson.title}";
      bool isExist =
          await checkIfVideoDownloadedBefore(videoName: videoName + 'enc.aes');
      if (isExist) {
        String basePath = appController.videosDirectoryPath;
        Loading.overlayLoading(Get.context!);
        final ReceivePort receivePort = ReceivePort();
        await Isolate.spawn((sendPost) {
          decryptFile(basePath, videoName, sendPost);
        }, receivePort.sendPort);
        receivePort.listen((message) {
          if (message == 'done') {
            Get.back();
            Get.to(
              () => DownloadedVideoPlayerPage(
                videoName: videoName,
              ),
            );
          }
        });
      } else {
        log(lesson.link360, name: "link 360");
        log(lesson.videoLink720, name: "link 720");
        log(lesson.audioLink720, name: "link 720");

        // if (appController.isGuestUser() == false
        //    ) {
        //   appController.showToast(Get.context!,
        //       message: 'Something went wrong'.tr, status: ToastStatus.warning);
        //   return;
        // }
        Get.to(() => NetworkVideoPlayerPage(
              lessonName: lesson.title,
              youtubeUrl: lesson.link,
              lessonSubTitle: lesson.subTitle,
              videoUrls: appController.isGuestUser()
                  ? [
                      'https://www.kadi-center.com/storage/test.mp4',
                      'https://www.kadi-center.com/storage/test.mp4',
                    ]
                  : [
                      lesson.link360,
                      lesson.videoLink720 == ""
                          ? lesson.link360
                          : lesson.videoLink720,
                      lesson.audioLink720 == ""
                          ? lesson.link360
                          : lesson.audioLink720,
                    ],
              lessonId: lesson.id,
              sectionId: lesson.sectionId,
              isFree: lesson.isFree,
            ));
      }
    } else {
      appController.showToast(Get.context!,
          message: 'Please participate in this research first'.tr,
          status: ToastStatus.warning);
    }
  }

  Future<bool> checkIfVideoDownloadedBefore({required String videoName}) async {
    String path = appController.videosDirectoryPath + "/" + videoName;
    bool isVideoDownloaded = io.File(path).existsSync();
    return isVideoDownloaded;
  }

  afterSubscription(SubscriptionType type, {int? index}) {
    if (type == SubscriptionType.Section) {
      subjectModel.lectures[index!].isSubscription.value = true;
    } else if (type == SubscriptionType.Subject) {
      for (int i = 0; i < subjectModel.lectures.length; i++) {
        subjectModel.lectures[i].isSubscription.value = true;
      }
    }
  }

  @override
  void onInit() async {
    fromHome = Get.arguments?['from_home'] ?? false;
    subjectName = Get.arguments?['title'] ?? "test";
    subjectId = Get.arguments?['id'] ?? 1;
    !await appController.internetChecker();
    loadSubjectDetail();
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
