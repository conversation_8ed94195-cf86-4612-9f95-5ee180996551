import 'dart:developer';
import 'dart:io' show Platform;

import 'package:all_in_one/core/style/style.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/controllers/global_controller.dart';
import 'package:all_in_one/core/controllers/notification_controller.dart';
import '../../core/controllers/app_controller.dart';
import '../../core/routes.dart';

class SplashScreenController extends GetxController {
  AppController appController = Get.find();
  GlobalController globalController = Get.find();
  NotificationController notificationController = Get.find();

  late bool isRealDevice;

  _isRealDevice() async {
    if (Platform.isAndroid) {
      isRealDevice = !(await isEmulator());
    } else if (Platform.isIOS) {
      DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      isRealDevice = iosInfo.isPhysicalDevice;
      isRealDevice = true;
    }
  }

  loadData() async {
    await appController.loadUserData();
    await 4.seconds.delay();
    if (isRealDevice) {
      if (appController.isGuestUser() && appController.isInReview) {
        Nav.offAll(Pages.login);
        return;
      }
      if (appController.isGuestUser()) {
        Nav.offAll(Pages.signUp);
      } else {
        Nav.offAll(Pages.navBar);
      }
    } else {
      Get.to(Container(
          width: Get.width,
          height: Get.height,
          decoration: BoxDecoration(
            color: AppStyle.whiteColor,
          ),
          child: Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 32.0),
              child: Text(
                "This app is not to be used on Emulators",
                textAlign: TextAlign.center,
                style: Get.textTheme.headlineLarge,
              ),
            ),
          )));
    }
    try {
      log('${appController.token}');
      log('${appController.role.toString()}');
      log('${appController.user?.toJson()}');
    } catch (e) {}
  }

  Future<bool> isEmulator() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();

    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;

      if (isRunningOnEmulatorModel('bluestack', androidInfo) ||
          isRunningOnEmulatorModel('star2qltechn', androidInfo)) {
        return true;
      }

      bool isEmulator = androidInfo.isPhysicalDevice == false ||
          androidInfo.model.toLowerCase().contains('sdk') == true ||
          androidInfo.model.toLowerCase().contains('emulator') == true ||
          androidInfo.model.toLowerCase().contains('android sdk built for') ==
              true;
      // if (!isEmulator) {
      //   DialogHelper.showDialog(
      //       dialogBody: Column(
      //     mainAxisSize: MainAxisSize.min,
      //     children: [
      //       Text("Model:${androidInfo.model}"),
      //       Text("Brand:${androidInfo.brand}"),
      //       Text("Device:${androidInfo.device}"),
      //       Text("Manufacturer:${androidInfo.manufacturer}"),
      //     ],
      //   ));
      // }
      return isEmulator;
    }

    return false;
  }

  @override
  void onInit() {
    _isRealDevice();
    loadData();
    super.onInit();
  }
}

bool isRunningOnEmulatorModel(String emulator, AndroidDeviceInfo androidInfo) {
  String lower = emulator.toLowerCase();

  if (androidInfo.model.toLowerCase().contains(lower) == true ||
      androidInfo.brand.toLowerCase().contains(lower) == true ||
      androidInfo.device.toLowerCase().contains(lower) == true ||
      androidInfo.manufacturer.toLowerCase().contains(lower) == true) {
    return true;
  } else {
    return false;
  }
}
