import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/constants/role.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/controllers/notification_controller.dart';
import 'package:all_in_one/core/models/app/user.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import '../../../core/constants/enum.dart';
import '../../../core/routes.dart';

class CodeVerificationPageController extends GetxController {
  DataController dataController = Get.find();
  AppController appController = Get.find();
  NotificationController notificationController = Get.find();

  String phone = '';
  TextEditingController pinCode = TextEditingController();

  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();

  RxString _time = ''.obs;
  String get time => this._time.value;
  set time(value) => this._time.value = value;

  Rx<bool> _resendLoading = false.obs;
  bool get resendLoading => _resendLoading.value;
  set resendLoading(bool value) => _resendLoading.value = value;

  bool _timerBreaker = false;
  timerBreaker() async {
    _timerBreaker = true;
    await Future.delayed(1.seconds);
    _timerBreaker = false;
  }

  timer(Duration d) async {
    time = d.toString().substring(2, 7);
    await timerBreaker();
    while (d.inSeconds > 0 && !_timerBreaker) {
      await Future.delayed(1.seconds);
      d = (d.inSeconds - 1).seconds;
      time = d.toString().substring(2, 7);
    }
    time = '';
  }

  verifyCode(context) async {
    if (formKey.currentState!.validate()) {
      signInConfirmAccount(context);
    }
  }

  resendCode(context) async {
    if (time.isNotEmpty || resendLoading) return;
    resendLoading = true;
    timer(120.seconds);
    ResponseModel response;

    response = await dataController.postData(
      url: API.resendCode,
      body: {
        'phone': phone,
        'dial_country_code': appController.dialCountryCode,
      },
    );
    if (response.success) {
      resetField();
      appController.showToast(context,
          message: response.message!, status: ToastStatus.success);
    } else {
      appController.showToast(
        context,
        message: response.message!,
      );
    }
    resendLoading = false;
  }

  signInConfirmAccount(BuildContext context) async {
    String? fcmToken;
    try {
      fcmToken = await notificationController.fcmToken();
    } catch (e) {}
    try {
      ResponseModel response;
      response = await dataController.postData(
        url: API.verifyCode,
        body: {
          'phone': phone,
          'otp': pinCode.text,
          'dial_country_code': appController.dialCountryCode,
          'device_token': fcmToken ?? 'device_token'
        },
      );
      if (response.success) {
        Get.back();
        appController.setUserData(
            user: UserModel.fromJson(response.data),
            token: response.data['token'],
            role: Role.user);
        Nav.offAll(Pages.navBar);
      } else if (response.code == ErrorCode.VALIDATION_ERROR ||
          response.errors != null ||
          response.errors!.isNotEmpty) {
        Get.back();
        appController.showToast(context, message: response.errorsAsString);
      } else {
        Get.back();
        appController.showToast(Get.context!,
            message: response.message!, status: ToastStatus.fail);
      }
    } catch (e) {
      Get.back();
    }
  }

  navigateToLogin() {
    Nav.offAll(Pages.login);
  }

  resetField() {
    pinCode.clear();
  }

  @override
  void onInit() {
    if (Get.arguments != null) {
      phone = Get.arguments['phone'];
    }
    timer(10.seconds);
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
  }
}
