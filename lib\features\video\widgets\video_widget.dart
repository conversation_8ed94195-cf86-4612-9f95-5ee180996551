import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:all_in_one/features/video/controller/network_video_player_controller.dart';
import 'package:all_in_one/features/video/widgets/custom_vedio.dart';

class VideoWidget extends StatefulWidget {
  final List<String> videoUrls;
  final String youtubeUrl;
  final int lessonId;
  const VideoWidget(
      {required this.videoUrls,
      required this.lessonId,
      required this.youtubeUrl});
  @override
  _VideoWidgetState createState() => _VideoWidgetState();
}

class _VideoWidgetState extends State<VideoWidget> with WidgetsBindingObserver {
  late NetworkVideoPlayerController videoController =
      Get.find(tag: "${widget.lessonId}");

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InAppWebView(
      onEnterFullscreen: (webController) => webController.callAsyncJavaScript(
          functionBody: "document.exitFullscreen();"),
      // onReceivedHttpError: (webController, request, response) async {
      //   await videoController.refreshVideoLink(
      //       (await webController.getUrl())?.path, false, widget.youtubeUrl);
      // },
      key: widget.key,
      initialData: InAppWebViewInitialData(
        data: player,
        baseUrl: WebUri('https://www.youtube.com'),
        encoding: 'utf-8',
        mimeType: 'text/html',
      ),
      initialOptions: InAppWebViewGroupOptions(
        crossPlatform: InAppWebViewOptions(
          mediaPlaybackRequiresUserGesture: true,
          transparentBackground: true,
          disableContextMenu: true,
          supportZoom: false,
          disableHorizontalScroll: false,
          disableVerticalScroll: false,
          useShouldOverrideUrlLoading: true,
        ),
        ios: IOSInAppWebViewOptions(
          allowsInlineMediaPlayback: true,
          allowsAirPlayForMediaPlayback: true,
          allowsPictureInPictureMediaPlayback: true,
        ),
        android: AndroidInAppWebViewOptions(
          useWideViewPort: false,
          useHybridComposition: true,
          builtInZoomControls: true,
          useShouldInterceptRequest: true,
          cacheMode: AndroidCacheMode.LOAD_CACHE_ELSE_NETWORK,
          domStorageEnabled: true,
          loadWithOverviewMode: true,
          mixedContentMode: AndroidMixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
          thirdPartyCookiesEnabled: true,
          allowContentAccess: true,
          allowFileAccess: true,
          clearSessionCache: true,
          databaseEnabled: true,
          displayZoomControls: false,
          supportMultipleWindows: true,
        ),
      ),
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        return NavigationActionPolicy.CANCEL;
      },
      onWebViewCreated: (InAppWebViewController controller) {
        videoController.webViewController = controller;
      },
    );
  }

  String get player => '''
 
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <link href="https://fonts.googleapis.com/css2?family=Tajawal&display=swap" rel="stylesheet">


        <style>
            html,
            body {
                margin: 0;
                padding: 0;
                min-height: 100%;
                direction: rtl;
                font-family: 'Tajawal', sans-serif;
                background: #333;
            }
        
            video::-webkit-media-controls-fullscreen-button {
                display: none !important;
            }
        
            video::-moz-media-controls-fullscreen-button {
                display: none !important;
            }
        
            video::-o-media-controls-fullscreen-button {
                display: none !important;
            }
        
            video::-ms-media-controls-fullscreen-button {
                display: none !important;
            }
        
            .vContainer {
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center
            }
        
            .qualities {
                position: fixed;
                width: 100%;
                bottom: -140px;
                transition: all 0.6s cubic-bezier(0.96, 0.04, 0, 1.01);
                height: 140px;
                background: #f6f6f6;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
            }
        
            .qualities .goUp {
                position: absolute;
                width: 40px;
                height: 28px;
                display: flex;
                justify-content: center;
                align-items: center;
                top: -28px;
                z-index: 22222;
                background: #ebebeb;
                cursor: pointer;
                margin-inline-start: 20px
            }
        
            .qualities .goUp svg {
                width: 20px;
            }
        
            .qualities.show {
                bottom: 0;
                box-shadow: 0px -8px 9px #00000003;
            }
        
            .qualities.show .goUp svg {
                width: 20px;
                animation: rotation 2s infinite linear;
            }
        
            .qualities.show .goUp svg path {
                fill: #3523b0
            }
        
            .qualities button {
                display: flex;
                padding: 3px 20px;
                border: 0;
                height: 46px;
                align-items: center;
                cursor: pointer;
            }
        
            .qualities button svg {
                width: 16px;
                display: none;
            }
        
            .qualities button:not(.active) {
                padding-inline-start: 36px;
            }
        
            .qualities button.active svg {
                display: block;
            }
        
            .qualities>*:nth-child(3),
            .qualities>*:nth-child(4) {
                border-block: 1px dotted #999;
            }
        
            .qualities>*:nth-child(4) {
                border-block-start: 0px
            }
        
            .qualities>*:nth-child(2) {
                display: flex;
                padding-inline: 21px;
                align-items: center;
                border: 0;
                height: 44px;
                align-items: center;
            }
        
            @keyframes rotation {
                from {
                    transform: rotate(0deg);
                }
        
                to {
                    transform: rotate(359deg);
                }
            }
        
            @media (orientation: portrait) {
        
                video,
                .vContainer {
                    width: 100vw;
                    height: auto
                }
        
                .vContainer {
                    height: 100vh
                }
            }
        
            @media (orientation: landscape) {
        
                video,
                .vContainer {
                    width: auto;
                    height: 100vh
                }
            }
        </style>
        
    <title></title>
    
</head>

<body>

    <div class="vContainer">
        <video class="" id="reem" width="100%" controls controlsList="nodownload">
            <source
                src="${widget.videoUrls[0]}"
                type="video/mp4">
        </video>

       

    <script>
    function changeVideo(newSource, buttonElement) {
        var videoElement = document.getElementById('reem');
        videoElement.src = newSource;
        videoElement.load();

        // Remove 'active' class from all buttons
        var buttons = document.querySelectorAll('.qualities button');
        buttons.forEach(function (button) {
            button.classList.remove('active');
        });

        // Add 'active' class to the clicked button
        buttonElement.classList.add('active');

        var qualitiesElement = document.querySelector('.qualities');
        qualitiesElement.classList.remove('show');
    }

    function toggleQualities() {
        var qualitiesElement = document.querySelector('.qualities');
        qualitiesElement.classList.toggle('show');
    }
    </script>

</body>

</html>
  ''';
}
