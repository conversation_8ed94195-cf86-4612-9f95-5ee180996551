import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';

class HomeCoursesSectionShimmer extends StatelessWidget {
  const HomeCoursesSectionShimmer();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 15,),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Text(
            'Sections'.tr,
            style: Theme.of(context).textTheme.titleMedium!.copyWith(
                color: AppStyle.mediumBlackTextColor,
                fontWeight: FontWeight.w900
            ),
          ),
        ),
        const SizedBox(height: 10,),
        Sized<PERSON><PERSON>(
          height: 180,
          child: ListView.separated(
            itemCount: 8,
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 20),
            itemBuilder: (context, index){
              return SkeletonWidget(
                width: 125,
                height: 180,
                radius: 10,
              );
            },
            separatorBuilder: (context, index){
              return const SizedBox(width: 8,);
            },
          ),
        ),

      ],
    );
  }
}
