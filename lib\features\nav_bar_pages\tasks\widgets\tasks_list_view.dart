import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/controller/tasks_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/models/task_model.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/widgets/task_list_item_widget.dart';

class TasksListView extends StatelessWidget {
  const TasksListView({
    super.key,
    required this.tasks,
  });

  final List<TaskModel> tasks;

  @override
  Widget build(BuildContext context) {
    TasksController tasksController = Get.find<TasksController>();
    return ListView.separated(
      itemCount: tasks.length,
      //physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return TaskListItemWidget(
          task: tasks[index],
          onEdit: (textModel) => tasksController.editTask(context, textModel),
          onDelete: tasksController.deleteTask,
        );
      },
      separatorBuilder: (context, index) {
        return const SizedBox(
          height: 10,
        );
      },
    );
  }
}
