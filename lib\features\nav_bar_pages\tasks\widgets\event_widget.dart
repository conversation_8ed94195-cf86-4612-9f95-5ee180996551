import 'package:flutter/material.dart';
import 'package:flutter_calendar_carousel/classes/event.dart';
import 'package:all_in_one/core/style/style.dart';

Event getEventWidget(Event event){
  return Event(
    date: new DateTime(2024, 2, 26),
    dot: Container(
      margin: EdgeInsets.symmetric(horizontal: 1.0),
      decoration: BoxDecoration(
          color: AppStyle.primaryColor,
          shape: BoxShape.circle
      ),
      height: 5.0,
      width: 5.0,
    ),
  );
}