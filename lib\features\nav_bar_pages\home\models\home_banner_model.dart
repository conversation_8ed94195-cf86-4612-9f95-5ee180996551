import 'dart:developer';

class HomeBannerModel {
  int id;
  String title;
  String? link;
  String banner;
  String? text;

  HomeBannerModel(
      {required this.id,
      required this.title,
      required this.link,
      required this.banner,
      this.text});

  factory HomeBannerModel.fromJson(Map<String, dynamic> json) {
    log(json.toString(), name: "slider");
    return HomeBannerModel(
        id: json['id'],
        title: json['title'],
        link: json['link'],
        text: json['description'],
        banner: json['banner']['original_url']);
  }
}
