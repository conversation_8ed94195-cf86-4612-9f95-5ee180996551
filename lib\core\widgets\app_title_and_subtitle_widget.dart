import 'package:flutter/material.dart';

class AppTitleAndSubTitleWidget extends StatelessWidget {
  const AppTitleAndSubTitleWidget({
    Key? key,
    this.subtitle,
    this.title,
  }) : super(key: key);

  final String? title;
  final String? subtitle;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 300,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Assets.dhLogo.image(width: 90,height: 90),

          if (title != null) const SizedBox(height: 8),
          if (title != null)
            Text(
              title!,
              style: Theme.of(context).textTheme.headlineLarge,
              textAlign: TextAlign.center,
            ),

          if (subtitle != null) const SizedBox(height: 8),
          if (subtitle != null)
            Text(
              subtitle!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }
}
