class TaskDateModel {
  String taskDate;
  int year;
  int month;
  int day;
  int taskCnt;
  int taskId;

  TaskDateModel(
      {required this.taskDate,
      required this.taskCnt,
      required this.year,
      required this.month,
      required this.day,
      required this.taskId});

  factory TaskDateModel.fromJson(Map<String, dynamic> json) => TaskDateModel(
      taskCnt: json['task_cnt'],
      taskDate: json['task_date'],
      taskId: json['pivot']['task_id'],
      year: getYear(json['task_date']),
      month: getMonth(json['task_date']),
      day: getDay(json['task_date']));

  static int getYear(String date) {
    int year = int.tryParse(date.substring(0, 4))!;
    return year;
  }

  static int getMonth(String date) {
    int month = int.tryParse(date.substring(5, 7))!;
    return month;
  }

  static int getDay(String date) {
    int day = int.tryParse(date.substring(8, 10))!;
    return day;
  }
}
