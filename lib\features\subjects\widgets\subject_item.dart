import 'package:flutter/material.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/cached_image_widget.dart';
import 'package:all_in_one/features/course/models/subject_model.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_year_model.dart';

class SubjectItem extends StatelessWidget {
  final SubjectModel subjectModel;
  const SubjectItem({required this.subjectModel, required this.onTap});
  final Function() onTap;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: CachedImageWidget(
              imageUrl: subjectModel.mainImage,
              borderRadius: BorderRadius.all(Radius.circular(10)),
              loadingHeight: 190,
              fit: BoxFit.cover,
            ),
          ),
          Text(
            subjectModel.title,
            style: Theme.of(context).textTheme.titleSmall!.copyWith(
                  color: AppStyle.blackColor,
                  // fontWeight: FontWeight.w900,
                ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
