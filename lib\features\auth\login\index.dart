import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/widgets/app_text_feild.dart';
import 'package:all_in_one/features/auth/widgets/app_auth_page.dart';
import 'package:all_in_one/features/auth/widgets/auth_page_switcher_widget.dart';

import '../../../core/style/style.dart';
import '../../../core/utils/validator.dart';
import '../../../core/widgets/button.dart';
import 'controller.dart';

class LoginPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    LoginPageController controller = Get.put(LoginPageController());
    return Scaffold(
      key: controller.scaffold<PERSON>ey,
      body: Safe<PERSON>rea(
        child: AppAuthPage(
          title: 'Sign In'.tr,
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              height: Get.height * 0.6,
              decoration: BoxDecoration(
                  color: AppStyle.whiteColor,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(25),
                    topRight: Radius.circular(25),
                  )),
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 25),
              child: SingleChildScrollView(
                child: Form(
                  key: controller.formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Welcome Back'.tr,
                        style: Theme.of(context)
                            .textTheme
                            .headlineLarge
                            ?.copyWith(
                                color: AppStyle.blackTextColor,
                                fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 10),
                      Padding(
                        padding: const EdgeInsetsDirectional.only(end: 100),
                        child: Text(
                          'Log in using your phone number to continue'.tr,
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    color: AppStyle.lightBlackColor,
                                  ),
                        ),
                      ),
                      const SizedBox(
                        height: 50,
                      ),
                      // const SizedBox(height: 500,),
                      Obx(
                        () => TextFormField(
                          controller: controller.phone,
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium
                              ?.copyWith(color: AppStyle.lightBlackColor),
                          validator: Validator.notNullValidation,
                          keyboardType: TextInputType.phone,
                          decoration: InputDecoration(
                              hintText: "Phone".tr,
                              errorText: controller.phoneError.isNotEmpty
                                  ? controller.phoneError
                                  : null),
                        ),
                      ),
                      const SizedBox(
                        height: 16,
                      ),

                      AppTextField(
                        controller.password,
                        isSecure: true,
                        type: FieldTypeEnum.MainTheme,
                        hint: "password".tr,
                      ),
                      const SizedBox(
                        height: 50,
                      ),
                      AppButton(
                          text: 'Sign In'.tr,
                          height: 58,
                          radius: 10,
                          withLoading: true,
                          margin: EdgeInsets.zero,
                          style: Get.textTheme.titleMedium!.copyWith(
                              color: AppStyle.whiteColor,
                              fontWeight: FontWeight.bold),
                          onTap: () async => controller.login(context)),
                      if (controller.appController.isInReview)
                        Padding(
                          padding: const EdgeInsets.only(top: 16.0),
                          child: AppButton(
                              text: 'Guest Skip'.tr,
                              height: 58,
                              radius: 10,
                              withLoading: true,
                              fillColor: Colors.transparent,
                              margin: EdgeInsets.zero,
                              style: Get.textTheme.titleMedium!.copyWith(
                                  color: AppStyle.primaryColor,
                                  fontWeight: FontWeight.bold),
                              onTap: () async => Nav.offAll(Pages.navBar)),
                        ),

                      const SizedBox(
                        height: 30,
                      ),
                      AuthPageSwitcherWidget(
                        label: 'don`t have an account?'.tr,
                        onTapLabel: 'Sign Up'.tr,
                        onTap: () => Nav.replacement(Pages.signUp),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
