import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init('offline_tasks');
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Offline Task Manager',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const OfflineTaskManager(),
    );
  }
}

// Offline Task model for local storage
class OfflineTask {
  final int id;
  final String title;
  final String details;
  final String date;
  final int colorValue;
  final bool isCompleted;

  OfflineTask({
    required this.id,
    required this.title,
    required this.details,
    required this.date,
    required this.colorValue,
    this.isCompleted = false,
  });

  Color get color => Color(colorValue);

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'details': details,
    'date': date,
    'colorValue': colorValue,
    'isCompleted': isCompleted,
  };

  factory OfflineTask.fromJson(Map<String, dynamic> json) => OfflineTask(
    id: json['id'],
    title: json['title'],
    details: json['details'],
    date: json['date'],
    colorValue: json['colorValue'],
    isCompleted: json['isCompleted'] ?? false,
  );
}

// Controller for the offline task manager
class OfflineTaskController extends GetxController {
  late final GetStorage box;
  RxList<OfflineTask> tasks = <OfflineTask>[].obs;
  RxList<OfflineTask> filteredTasks = <OfflineTask>[].obs;
  
  final titleController = TextEditingController();
  final detailsController = TextEditingController();
  final dateController = TextEditingController();
  
  final formKey = GlobalKey<FormState>();
  
  RxInt selectedColorIndex = 0.obs;
  
  List<Color> taskColors = [
    Colors.blue,
    Colors.red,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.teal,
  ];
  
  @override
  void onInit() async {
    super.onInit();
    try {
      await GetStorage.init('offline_tasks');
      box = GetStorage('offline_tasks');
    } catch (e) {
      // Create a fallback storage if initialization fails
      box = GetStorage();
    }
    loadTasks();
  }
  
  void loadTasks() {
    if (box.hasData('tasks')) {
      final List<dynamic> storedTasks = box.read('tasks');
      tasks.value = storedTasks.map((task) => OfflineTask.fromJson(task)).toList();
      filteredTasks.value = List.from(tasks);
    }
  }
  
  void saveTasks() {
    box.write('tasks', tasks.map((task) => task.toJson()).toList());
  }
  
  void addTask() {
    if (formKey.currentState!.validate()) {
      final newTask = OfflineTask(
        id: DateTime.now().millisecondsSinceEpoch,
        title: titleController.text,
        details: detailsController.text,
        date: dateController.text,
        colorValue: taskColors[selectedColorIndex.value].hashCode,
      );
      
      tasks.add(newTask);
      filteredTasks.value = List.from(tasks);
      saveTasks();
      
      titleController.clear();
      detailsController.clear();
      dateController.clear();
      selectedColorIndex.value = 0;
    }
  }
  
  void deleteTask(int id) {
    tasks.removeWhere((task) => task.id == id);
    filteredTasks.value = List.from(tasks);
    saveTasks();
  }
  
  void toggleTaskCompletion(int id) {
    final index = tasks.indexWhere((task) => task.id == id);
    if (index != -1) {
      final task = tasks[index];
      final updatedTask = OfflineTask(
        id: task.id,
        title: task.title,
        details: task.details,
        date: task.date,
        colorValue: task.colorValue,
        isCompleted: !task.isCompleted,
      );
      
      tasks[index] = updatedTask;
      filteredTasks.value = List.from(tasks);
      saveTasks();
    }
  }
  
  void filterTasks(String filter) {
    switch (filter) {
      case 'all':
        filteredTasks.value = List.from(tasks);
        break;
      case 'completed':
        filteredTasks.value = tasks.where((task) => task.isCompleted).toList();
        break;
      case 'pending':
        filteredTasks.value = tasks.where((task) => !task.isCompleted).toList();
        break;
      case 'today':
        final today = DateTime.now();
        final formattedToday = '${today.year}-${today.month}-${today.day}';
        filteredTasks.value = tasks.where((task) => task.date == formattedToday).toList();
        break;
    }
  }
}

// Offline Task Manager UI
class OfflineTaskManager extends StatelessWidget {
  const OfflineTaskManager({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(OfflineTaskController());
    
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          "Task Manager",
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: controller.filterTasks,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'all',
                child: Text('All Tasks'),
              ),
              const PopupMenuItem(
                value: 'completed',
                child: Text('Completed'),
              ),
              const PopupMenuItem(
                value: 'pending',
                child: Text('Pending'),
              ),
              const PopupMenuItem(
                value: 'today',
                child: Text('Today'),
              ),
            ],
          ),
        ],
      ),
      body: Obx(
        () => controller.filteredTasks.isEmpty
            ? const Center(
                child: Text(
                  "No tasks found",
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              )
            : ListView.builder(
                itemCount: controller.filteredTasks.length,
                padding: const EdgeInsets.all(16),
                itemBuilder: (context, index) {
                  final task = controller.filteredTasks[index];
                  return TaskCard(
                    task: task,
                    onDelete: () => controller.deleteTask(task.id),
                    onToggle: () => controller.toggleTaskCompletion(task.id),
                  );
                },
              ),
      ),
      floatingActionButton: FloatingActionButton(
        child: const Icon(Icons.add),
        onPressed: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            builder: (context) => const AddTaskBottomSheet(),
          );
        },
      ),
    );
  }
}

// Task Card Widget
class TaskCard extends StatelessWidget {
  final OfflineTask task;
  final VoidCallback onDelete;
  final VoidCallback onToggle;

  const TaskCard({
    super.key,
    required this.task,
    required this.onDelete,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: task.color.withAlpha(128), // 0.5 opacity = 128 alpha
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onToggle,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      task.title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        decoration: task.isCompleted ? TextDecoration.lineThrough : null,
                        color: task.isCompleted ? Colors.grey : Colors.black,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      task.isCompleted ? Icons.check_circle : Icons.circle_outlined,
                      color: task.color,
                    ),
                    onPressed: onToggle,
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete_outline, color: Colors.red),
                    onPressed: onDelete,
                  ),
                ],
              ),
              if (task.details.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  task.details,
                  style: TextStyle(
                    fontSize: 14,
                    color: task.isCompleted ? Colors.grey : Colors.black87,
                    decoration: task.isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
              ],
              const SizedBox(height: 12),
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                  const SizedBox(width: 4),
                  Text(
                    task.date,
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Add Task Bottom Sheet
class AddTaskBottomSheet extends StatelessWidget {
  const AddTaskBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<OfflineTaskController>();
    
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16,
        right: 16,
        top: 16,
      ),
      child: Form(
        key: controller.formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              "Add New Task",
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: controller.titleController,
              decoration: const InputDecoration(
                labelText: "Title",
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "Please enter a title";
                }
                return null;
              },
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: controller.detailsController,
              decoration: const InputDecoration(
                labelText: "Details (optional)",
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 12),
            TextFormField(
              controller: controller.dateController,
              decoration: const InputDecoration(
                labelText: "Date",
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.calendar_today),
              ),
              readOnly: true,
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime.now().subtract(const Duration(days: 365)),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                
                if (date != null) {
                  controller.dateController.text = "${date.year}-${date.month}-${date.day}";
                }
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "Please select a date";
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            const Text(
              "Task Color",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 50,
              child: Obx(
                () => ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: controller.taskColors.length,
                  itemBuilder: (context, index) {
                    final isSelected = controller.selectedColorIndex.value == index;
                    return GestureDetector(
                      onTap: () => controller.selectedColorIndex.value = index,
                      child: Container(
                        width: 40,
                        height: 40,
                        margin: const EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          color: controller.taskColors[index],
                          shape: BoxShape.circle,
                          border: isSelected
                              ? Border.all(color: Colors.black, width: 2)
                              : null,
                        ),
                        child: isSelected
                            ? const Icon(Icons.check, color: Colors.white)
                            : null,
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                controller.addTask();
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: const Text(
                "Add Task",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
