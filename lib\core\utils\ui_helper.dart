
import 'package:flutter/material.dart';

abstract class UIHelper{
  static BorderRadiusDirectional oneSideBorderRadius(double radius,{bool topStart=false,bool topEnd=false,bool bottomStart=false, bool bottomEnd=false}){
    return BorderRadiusDirectional.only(
      topEnd: topStart?Radius.circular(radius):Radius.zero,
      topStart: topEnd?Radius.circular(radius):Radius.zero,
      bottomStart: bottomStart?Radius.circular(radius):Radius.zero,
      bottomEnd: bottomEnd?Radius.circular(radius):Radius.zero,
    );
  }
}