import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/controller/tasks_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/widgets/selected_chips.dart';

class TasksFilter extends StatefulWidget {
  const TasksFilter({super.key});

  @override
  State<TasksFilter> createState() => _TasksFilterState();
}

class _TasksFilterState extends State<TasksFilter> {
  String currentFilter = 'today'.tr;
  DateTime? date;
  List<String> filterList = [
    'all'.tr,
    'today'.tr,
    'week'.tr,
    'month'.tr,
    'specific_date'.tr
  ];
  @override
  Widget build(BuildContext context) {
    return Builder(builder: (context) {
      TasksController tasksController = Get.find<TasksController>();

      return SizedBox(
        height: 50,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: SelectedChips(
                items: filterList,
                onSelectedItem: (filterIndex) async {
                  if (filterIndex == filterList.length - 1) {
                    date = await selectDate(context);
                    if (date != null) {
                      setState(() {
                        currentFilter = DateFormat('dd-MM-yyyy').format(date!);
                        filterList.removeAt(filterList.length - 1);
                        filterList.insert(filterList.length, currentFilter);
                      });
                    }
                  }
                  tasksController.filterTasks(
                      filter: getFilterFromIndex(filterIndex),
                      specificDate: date);
                },
              ),
            ),
            //   Text(
            //   currentFilter,
            // ),
          ],
        ),
      );
    });
  }

  TaskFilter getFilterFromIndex(int index) {
    switch (index) {
      case 0:
        return TaskFilter.all;
      case 1:
        return TaskFilter.thisDay;
      case 2:
        return TaskFilter.thisWeek;
      case 3:
        return TaskFilter.thisMonth;
      case 4:
        return TaskFilter.specificDate;
      default:
        return TaskFilter.all;
    }
  }

  Future<DateTime?> selectDate(
    BuildContext context,
  ) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      cancelText: 'cancel'.tr,
      initialDate: DateTime.now(), // The date the picker starts on
      firstDate: DateTime.now(), // The earliest date the user can select
      lastDate: DateTime.now()
          .add(Duration(days: 365 * 10)), // The latest date the user can select
      builder: (BuildContext context, Widget? child) {
        return child!;
      },
    );

    return pickedDate;
  }
}
