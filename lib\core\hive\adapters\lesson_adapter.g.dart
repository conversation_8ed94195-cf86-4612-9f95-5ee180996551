// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lesson_adapter.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LessonAdapterAdapter extends TypeAdapter<LessonAdapter> {
  @override
  final int typeId = 2;

  @override
  LessonAdapter read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LessonAdapter(
      id: fields[0] as int,
      title: fields[1] as String,
      subTitle: fields[2] as String,
    );
  }

  @override
  void write(BinaryWriter writer, LessonAdapter obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.title)
      ..writeByte(2)
      ..write(obj.subTitle);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LessonAdapterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
