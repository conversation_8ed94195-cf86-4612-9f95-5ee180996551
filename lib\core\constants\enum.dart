import 'package:flutter/material.dart';
import 'package:all_in_one/core/style/inputs.dart';

import 'defaults.dart';
import 'localization.dart';
import 'role.dart';

abstract class AppEnum {
  static AppLocalization getLocaleFromName(String? s) {
    if (s != null)
      for (AppLocalization locale in AppLocalization.values) {
        if (locale.value == s) return locale;
      }
    return Default.defaultLocale;
  }

  static Role getRoleFromName(String? s) {
    if (s != null)
      for (Role role in Role.values) {
        if (role.name == s) {
          return role;
        }
      }
    return Default.defaultRole;
  }
}

enum FieldTypeEnum {
  MainTheme,
  BorderLess,
  LabelTheme;

  InputDecorationTheme getTextFieldDecoration() {
    switch (this) {
      case MainTheme:
        return AppInputFieldThemes.mainThemeDecoration();
      case BorderLess:
        return AppInputFieldThemes.borderLessDecoration();
      case LabelTheme:
        return AppInputFieldThemes.labelThemeDecoration();
    }
  }
}

enum OfferType { Offer, Ads }

enum SubscriptionType {
  Year,
  Subject,
  Section;

  String getSubscriptionType() {
    switch (this) {
      case Year:
        return 'years';
      case Subject:
        return 'subjects';
      case Section:
        return 'sections';
    }
  }

  static SubscriptionType getTypeFromString(String type) {
    switch (type) {
      case 'years':
        return Year;
      case 'subjects':
        return Subject;
      case 'sections':
        return Section;
      default:
        return Year;
    }
  }
}

enum SubscriptionStatus { Success, Failed }

enum TaskFilter { all,thisDay, thisWeek, thisMonth, specificDate }
