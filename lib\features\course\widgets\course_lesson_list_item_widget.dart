import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/features/course/contollers/course_controller.dart';
import 'package:all_in_one/features/course/models/section_lesson_model.dart';

class CourseLessonListItemWidget extends StatelessWidget {
  final SectionLessonModel lesson;
  final bool isSubscription;
  const CourseLessonListItemWidget({
    required this.lesson,
    required this.isSubscription,
  });

  @override
  Widget build(BuildContext context) {
    CourseController courseController= Get.find();
    return InkWell(
      onTap: ()async{
        courseController.lessonOnTap(
          context: context,
          lesson: lesson,
          isSubscription: isSubscription,
        );
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 10),
        padding: EdgeInsets.symmetric(vertical: 16),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    lesson.title,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppStyle.lightBlackColor,
                        fontWeight: FontWeight.w700
                    ),
                  ),
                  const SizedBox(height: 4,),
                  Text(
                    lesson.subTitle,
                    style: Theme.of(context).textTheme.labelMedium!.copyWith(
                        color: AppStyle.greyTextColor,
                        fontWeight: FontWeight.w500
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 20,),
            isSubscription||lesson.isFree?
            Assets.icons.playIcon.svg():
            Assets.icons.lockIcon.svg()
          ],
        ),
      ),
    );
  }
}
