name: Monthly issue metrics
on:
  workflow_dispatch:
  schedule:
    - cron: '17 10 1 * *'

permissions:
  issues: write
  pull-requests: read

jobs:
  build:
    name: issue metrics
    runs-on: ubuntu-latest

    steps:

      - name: Get dates for last month
        shell: bash
        run: |
          # Calculate the first day of the previous month
          first_day=$(date -d "last month" +%Y-%m-01)
          
          # Calculate the last day of the previous month
          last_day=$(date -d "$first_day +1 month -1 day" +%Y-%m-%d)
          
          #Set an environment variable with the date range
          echo "$first_day..$last_day"
          echo "last_month=$first_day..$last_day" >> "$GITHUB_ENV"  

      - name: Run issue-metrics tool for issues and prs opened last month
        uses: github/issue-metrics@v2
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SEARCH_QUERY: 'repo:781flyingdutchman/background_downloader created:${{ env.last_month }} -reason:"not planned"'

      - name: Create issue for opened issues and prs
        uses: peter-evans/create-issue-from-file@v4
        with:
          title: Monthly issue metrics report for opened issues and prs
          token: ${{ secrets.GITHUB_TOKEN }}
          content-filepath: ./issue_metrics.md
          assignees: 781flyingdutchman

      - name: Run issue-metrics tool for issues and prs closed last month
        uses: github/issue-metrics@v2
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SEARCH_QUERY: 'repo:781flyingdutchman/background_downloader closed:${{ env.last_month }} -reason:"not planned"'

      - name: Create issue for closed issues and prs
        uses: peter-evans/create-issue-from-file@v4
        with:
          title: Monthly issue metrics report for closed issues and prs
          content-filepath: ./issue_metrics.md
          assignees: 781flyingdutchman
