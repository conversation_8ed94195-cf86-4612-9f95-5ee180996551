<manifest xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <application>

        <receiver android:name="NotificationReceiver" android:exported="false">
            <intent-filter>
                <action android:name="com.bbflight.background_downloader.cancelActive" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.bbflight.background_downloader.pause" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.bbflight.background_downloader.resume" />
            </intent-filter>
        </receiver>

        <provider
            android:name="com.bbflight.background_downloader.OpenFileProvider"
            android:authorities="${applicationId}.com.bbflight.background_downloader.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/bgd_file_paths" />
        </provider>

    </application>
</manifest>
