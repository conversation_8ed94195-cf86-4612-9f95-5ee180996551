import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/url_launcher.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_banner_model.dart';

import '../../../../core/widgets/cached_image_widget.dart';

class HomePageSliderSection extends StatelessWidget {
  final List<HomeBannerModel> banners;
  HomePageSliderSection({required this.banners});

  RxInt activeIndex = 0.obs;

  @override
  Widget build(BuildContext context) {
    return SliverVisibility(
      visible: banners.isNotEmpty,
      sliver: SliverToBoxAdapter(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Row(
            children: [
              Obx(() => AnimatedSmoothIndicator(
                    axisDirection: Axis.vertical,
                    activeIndex: activeIndex.value,
                    count: banners.length,
                    effect: ExpandingDotsEffect(
                      dotHeight: 6,
                      dotWidth: 6,
                      activeDotColor: AppStyle.primaryColor,
                    ),
                  )),
              SizedBox(width: 5),
              Expanded(
                child: CarouselSlider(
                  options: CarouselOptions(
                      scrollDirection: Axis.vertical,
                      height: 200,
                      enlargeCenterPage: true,
                      padEnds: true,
                      onPageChanged: (position, reason) {
                        activeIndex.value = position;
                      },
                      autoPlay: true),
                  items: imageSliders,
                ),
              ),
              
            ],
          ),
        ),
      ),
    );
  }

  late final List<Widget> imageSliders = banners
      .map((item) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 0),
            child: InkWell(
              onTap: item.link != null
                  ? () => UrlLauncher.openLauncher(item.link!)
                  : () {
                      if (item.text != null) {
                        Nav.to(Pages.sliderDetails, arguments: item);
                      }
                    },
              child: CachedImageWidget(
                imageUrl: item.banner,
                width: Get.width * 0.85,
                height: 160,
                borderRadius: BorderRadius.circular(15),
                fit: BoxFit.fill,
              ),
            ),
          ))
      .toList();
}


///////////////
// SizedBox(
// height: 200,
// width: MediaQuery.of(context).size.width,
// child: PageView.builder(
// itemCount: imageSliders.length,
// pageSnapping: true,
// controller: _pageController,
// onPageChanged: (page) {
// // setState(() {
// //   activePage = page;
// // });
// },
// itemBuilder: (context,pagePosition){
// return Container(
// margin: EdgeInsets.all(10),
// child: CachedImageWidget(
// imageUrl: imgList[pagePosition],
// width: Get.width*0.85,
// height: 190,
// borderRadius: BorderRadius.circular(15),
// fit: BoxFit.cover,
// ));
// }),
// );