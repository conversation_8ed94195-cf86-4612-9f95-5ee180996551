import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/course/models/section_note_model.dart';

class NoteController extends GetxController {
  DataController dataController = Get.find();
  AppController appController = Get.find();
  late int videoId;

  TextEditingController comment = TextEditingController();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  RxList<SectionNoteModel> notes = RxList<SectionNoteModel>([]);
  RxBool _notesLoading = false.obs;
  get notesLoading => this._notesLoading.value;
  set notesLoading(value) => this._notesLoading.value = value;

  void initialId(int videoId) {
    this.videoId = videoId;
    loadNotesData();
  }

  void loadNotesData() async {
    notes.value = [];
    notesLoading = true;
    ResponseModel response;
    response = await dataController.getData(
      url: API.notesApi(videoId),
    );
    if (response.success) {
      response.data
          .forEach((element) => notes.add(SectionNoteModel.fromJson(element)));
      notesLoading = false;
    } else {
      notesLoading = false;
    }
  }

  addNote(context) async {
    if (formKey.currentState!.validate()) {
      try {
        ResponseModel response;
        response = await dataController.postData(
          url: API.addNoteApi,
          body: {
            'comment': comment.text,
            'video_id': videoId,
          },
        );
        if (response.success) {
          Get.back();
          Get.back();
          notes.add(
              SectionNoteModel(id: 0, note: comment.text, videoId: videoId));
          comment.clear();
        } else {
          Get.back();
          appController.showToast(Get.context!,
              message: response.message!, status: ToastStatus.fail);
        }
      } catch (e) {
        Get.back();
      }
    } else {
      Get.back();
    }
  }

  deleteNote(int id) async {
    await dataController.deleteData(url: "${API.addNoteApi}/$id");
    loadNotesData();
  }

  @override
  void onInit() async {
    super.onInit();
  }
}
