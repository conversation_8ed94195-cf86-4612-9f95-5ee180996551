import 'package:flutter/material.dart';
import 'package:all_in_one/core/style/style.dart';

class AuthPageSwitcherWidget extends StatelessWidget {
  final String label;
  final String onTapLabel;
  final VoidCallback? onTap;
  const AuthPageSwitcherWidget({required this.label,required this.onTapLabel,required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.titleLarge!.copyWith(color: AppStyle.blackTextColor),
            ),
            const SizedBox(width: 4,),
            Container(
              padding: EdgeInsets.only(bottom: 4),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: AppStyle.blackTextColor,width: 1.5),
                )
              ),
              child: Text(
                onTapLabel,
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                    color: AppStyle.blackTextColor,
                    fontWeight: FontWeight.bold,
                  // decoration: TextDecoration.underline,
                  // decorationColor: AppStyle.blackTextColor,
                ),

              ),
            )
          ],
        ),
      ),
    );
  }
}
