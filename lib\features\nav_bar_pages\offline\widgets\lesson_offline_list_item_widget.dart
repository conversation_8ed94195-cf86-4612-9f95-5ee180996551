import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/features/nav_bar_pages/offline/offline_controller.dart';
import 'delete_lesson_dialog.dart';

class LessonOfflineListItemWidget extends StatelessWidget {
  final int subjectId;
  final int lessonId;
  final int lessonIndex;
  final int sectionIndex;
  final int sectionId;
  final String lessonName;
  final String lessonSubTitle;
  const LessonOfflineListItemWidget({
    required this.subjectId,
    required this.lessonId,
    required this.lessonIndex,
    required this.sectionIndex,
    required this.sectionId,
    required this.lessonName,
    required this.lessonSubTitle,
  });

  @override
  Widget build(BuildContext context) {
    OfflineController controller = Get.find();
    return InkWell(
      onTap: () async => controller.lessonOnTap("${lessonId}_$lessonName"),
      child: Container(
        margin: EdgeInsets.only(bottom: 10),
        padding: EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    lessonName,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        color: AppStyle.lightBlackColor,
                        fontWeight: FontWeight.w700),
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  Text(
                    lessonSubTitle,
                    style: Theme.of(context).textTheme.labelMedium!.copyWith(
                        color: AppStyle.greyTextColor,
                        fontWeight: FontWeight.w500),
                  ),
                ],
              ),
            ),
            const SizedBox(
              width: 20,
            ),
            InkWell(
              onTap: () async {
                DialogHelper.showDialog(
                    dialogBody: DeleteLessonDialog(
                  subjectId: subjectId,
                  sectionId: sectionId,
                  lessonId: lessonId,
                  lessonName: lessonName,
                  lessonIndex: lessonIndex,
                  sectionIndex: sectionIndex,
                ));
              },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: CircleAvatar(
                  radius: 13.5,
                  backgroundColor: AppStyle.primaryColor,
                  child: CircleAvatar(
                    radius: 12,
                    backgroundColor: AppStyle.whiteBackgroundColor,
                    child: Icon(
                      Icons.delete_outline,
                      size: 15,
                      color: AppStyle.primaryColor,
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
