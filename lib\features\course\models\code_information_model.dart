
class CodeInformationModel {
  final String type;
  final String status;
  final int itemId;
  final String itemTitle;

  const CodeInformationModel({
    required this.type,
    required this.status,
    required this.itemId,
    required this.itemTitle
  });

  factory CodeInformationModel.fromJson(Map<String, dynamic> json) => CodeInformationModel(
    type: json['type'],
    status: json['status'],
    itemId: json['item_id'],
    itemTitle: json['item_title'],
   );
}