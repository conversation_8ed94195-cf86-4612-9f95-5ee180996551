

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/nav_bar_pages/notifications/models/notification_model.dart';

class NotificationsController extends GetxController{
  DataController dataController = Get.find();
  AppController appController = Get.find();


  RxList<NotificationModel> notifications= RxList<NotificationModel>([]);

  RxBool _dataLoading = true.obs;
  get dataLoading  => this._dataLoading.value;
  set dataLoading (value) => this._dataLoading.value = value;

  void loadNotificationData() async {
    notifications.value=[];
    dataLoading =true;
    ResponseModel response;
    response = await dataController.getData(
      url: API.getNotifications,
    );
    if(response.success){
      response.data.forEach((element) => notifications.add(NotificationModel.fromJson(element)));
      dataLoading =false;
    }else{
      dataLoading =false;
      if(response.status == 403||response.status == 401){
        appController.removeUserData();
        appController.showToast(Get.context!, message: response.message,status: ToastStatus.fail);
        Nav.offAll(Pages.login);
      }
    }
  }


  @override
  void onInit() async {
    super.onInit();
    !await appController.internetChecker();
    loadNotificationData();
  }

}