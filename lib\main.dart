import 'dart:io';

import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/utils/platform_utils.dart';
import 'package:background_downloader/background_downloader.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:secure_application/secure_application.dart';
import 'package:all_in_one/core/constants/localization.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/notification_controller.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/features/splash_screen/splash_screen.dart';
import 'package:all_in_one/firebase_options.dart';

import 'core/constants/defaults.dart';
import 'core/controllers/global_controller.dart';
import 'core/hive/hive_data.dart';
import 'core/routes.dart';
import 'core/translation/app_translation.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // await FlutterDownloader.initialize(debug: true, ignoreSsl: true);
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  // HttpOverrides.global = MyHttpOverrides();
  HiveData.init();
  await fileDownloader.configure(androidConfig: [
    (Config.runInForeground, Config.always),
  ]);
  await fileDownloader.trackTasks();
  // storeRetrieveKey();
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    Default.preferredOrientation();
    Default.lightStatusBar();
    Get.put(GlobalController());

    final AppController appController = Get.put(AppController());

    Get.put(NotificationController());
    return SecureApplication(
      child: Obx(
        () => GetMaterialApp(
            debugShowCheckedModeBanner: false,
            title: Default.appTitle,
            theme: AppStyle.lightTheme(appController.locale),
            themeMode: ThemeMode.light,
            translations: AppTranslation(),
            localizationsDelegates: [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            locale: appController.locale == AppLocalization.Ar
                ? Locale('ar', 'AE')
                : Locale('en', 'UK'),
            fallbackLocale: Locale('ar', 'AE'),
            supportedLocales: <Locale>[
              Locale('en', 'UK'),
              Locale('ar', 'AE'),
            ],
            // initialRoute: '/',
            unknownRoute: AppRouting.unknownRoute,
            getPages: AppRouting.routes(),
            home: SplashScreen()
            //     Visibility(
            //       visible: !appController.isLoading.value,
            //       replacement: Scaffold(
            //         body: Center(
            //           child: CircularProgressIndicator(),
            //         ),
            //       ),
            //       child:
            //  SplashScreen()
            //     ),
            ),
      ),
    );
  }
}
