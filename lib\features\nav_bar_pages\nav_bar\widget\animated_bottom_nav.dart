import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/features/nav_bar_pages/nav_bar/widget/bottom_nav_item.dart';

class AnimatedBottomNav extends StatelessWidget {
  final int? currentIndex;
  final Function(int)? onChange;

  AnimatedBottomNav({Key? key, this.currentIndex, this.onChange})
      : super(key: key);

  AppController appController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 65,
      decoration: BoxDecoration(
        color: AppStyle.whiteColor,
        boxShadow: AppStyle.boxShadow16,
      ),
      padding: EdgeInsets.symmetric(horizontal: 10, vertical: 3),
      child: Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () => onChange!(0),
              child: BottomNavItem(
                icon: Assets.icons.homeIcon.path,
                title: "Home".tr,
                isActive: currentIndex == 0,
              ),
            ),
          ),
          // Expanded(
          //   child: InkWell(
          //     onTap: () => onChange!(1),
          //     child: BottomNavItem(
          //       icon: Assets.icons.notificationIcon.path,
          //       title: "Notifications".tr,
          //       isActive: currentIndex == 1,
          //     ),
          //   ),
          // ),
          Expanded(
            child: InkWell(
              onTap: () => onChange!(2),
              child: BottomNavItem(
                icon: Assets.icons.tasksIcon.path,
                title: "Tasks".tr,
                isActive: currentIndex == 2,
              ),
            ),
          ),
          // Expanded(
          //   child: InkWell(
          //     onTap: () => onChange!(3),
          //     child: BottomNavItem(
          //       icon: Assets.icons.videoIcon.path,
          //       title: "My Videos".tr,
          //       isActive: currentIndex == 3,
          //     ),
          //   ),
          // ),
          Expanded(
            child: InkWell(
              onTap: () => onChange!(4),
              child: BottomNavItem(
                icon: Assets.icons.profileIcon.path,
                title: "Profile".tr,
                isActive: currentIndex == 4,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
