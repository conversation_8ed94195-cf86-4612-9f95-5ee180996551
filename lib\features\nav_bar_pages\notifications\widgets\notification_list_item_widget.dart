import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/features/nav_bar_pages/notifications/models/notification_model.dart';

class NotificationListItemWidget extends StatelessWidget {
  final NotificationModel notificationModel;
  const NotificationListItemWidget({required this.notificationModel});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        PositionedDirectional(
            bottom: 20,
            end: 35,
            child: Container(
                decoration: BoxDecoration(
                  color: AppStyle.primaryColor,
                  borderRadius: const BorderRadius.only(
                    bottomRight: Radius.circular(12),
                    bottomLeft: Radius.circular(12),
                  ),
                  boxShadow: [
                    BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        blurRadius: 1.0,
                        spreadRadius: 1,
                        offset:
                        const Offset(-2, 1) // changes position of shadow
                    ),
                  ],
                ),
                padding: EdgeInsets.only(top: 20, bottom: 8, left: 10, right: 10),
                child: Center(
                    child: Text(
                      notificationModel.createdAt,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(color: AppStyle.whiteColor,fontWeight: FontWeight.w700),
                    )))),
        Container(
          width: Get.width,
          padding: EdgeInsets.symmetric(horizontal: 20,vertical: 15),
          margin: EdgeInsets.only(left: 20,right: 20,bottom: 60),
          decoration: BoxDecoration(
            color: Color(0xffFfffff),
            borderRadius: const BorderRadius.all(Radius.circular(12)),
            boxShadow: [
              BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  blurRadius: 5,
                  spreadRadius: 3,
                  offset: const Offset(3, 3) // changes position of shadow
              ),
            ],
          ),
          child: Text(
            notificationModel.title,
            style: Theme.of(context).textTheme.bodyLarge!.copyWith(color: AppStyle.lightBlackColor,fontWeight: FontWeight.w500),
            textAlign: TextAlign.start,
          ),
        ),
      ],
    );
  }
}
