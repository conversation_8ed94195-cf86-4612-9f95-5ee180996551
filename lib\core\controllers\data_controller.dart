import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:isolate';
import 'dart:ui';

import 'package:background_downloader/background_downloader.dart';
import 'package:get/get.dart' hide GetConnect, Response, FormData;
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:all_in_one/core/hive/hive_helper.dart';
import 'package:all_in_one/features/video/controller/network_video_player_controller.dart';
import '../models/general/response_model.dart';
import 'app_controller.dart';

class DataController extends GetxService {
  AppController appController = Get.find();

  final bool withLog;
  DataController({this.withLog = true});
  //################### STORAGE ######################
  // Future<String> get localPath async {
  //   final path = await getApplicationDocumentsDirectory();
  //   return path.path;
  // }

  // Future<File> localFile(String name) async {
  //   final file = await localPath;
  //   return new File('$file/$name');
  // }

  //#################### API ########################
  Dio dio = Dio();

  Future<Map<String, dynamic>> get header async => {
        'Authorization': 'Bearer ${appController.token}',
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        // "App_Version": (await PackageInfo.fromPlatform()).version,
        'Accept-Language': 'ar',
        'from_mobile': true,
      };
  @override
  onInit() async {
    super.onInit();
  }

  //==================== POST =======================
  Future<ResponseModel> postData({
    required String url,
    required body,
    Map<String, dynamic>? param,
    bool isFullURL = false,
    CancelToken? cancel,
  }) async {
    Response response;
    try {
      if (withLog) {
        if (body is FormData) {}
      }
      response = await dio.post(
        isFullURL ? url : appController.baseUrl + url,
        data: body,
        options:
            Options(headers: await header, contentType: 'Application/json'),
        queryParameters: param,
        cancelToken: cancel,
      );
      if (withLog) {}
      log(response.toString(), name: response.realUri.path);
      if (response.data == null) throw 'connection error';
      return ResponseModel.fromJson(response.data);
    } catch (error) {
      if (error is DioException) {
        if (withLog) {}
        return handlingDioError(error);
      }
      return ResponseModel(success: false, message: 'No Internet');
    }
  }

  //================= GET ============================
  Future<ResponseModel> getData(
      {required String url,
      Map<String, dynamic>? param,
      bool isFullURL = false,
      CancelToken? cancel,
      String? extraParam}) async {
    Response response;
    try {
      if (withLog) {
        log(isFullURL ? url : appController.baseUrl + url);
        log('$param');
      }
      log('$header');

      int seconds = 0;
      Timer timer = Timer.periodic(1.seconds, (timer) {
        seconds = seconds + 1;
      });
      response = await dio.get(
        isFullURL ? url : appController.baseUrl + url,
        options: Options(headers: await header),
        queryParameters: param,
        cancelToken: cancel,
      );
      log(response.toString(), name: response.realUri.path);

      timer.cancel();
      if (withLog) {}
      if (response.data == null) throw '';
      return ResponseModel.fromJson(response.data, extraParam: extraParam);
    } catch (error) {
      if (withLog) {}
      if (error is DioException) {
        if (withLog) {}
        return handlingDioError(error);
      }
      return ResponseModel(success: false, message: 'No Internet');
    }
  }

  //======================= PUT =====================
  Future<ResponseModel> putData({
    required String url,
    body,
    Map<String, dynamic>? param,
    bool isFullURL = false,
    CancelToken? cancel,
  }) async {
    Response response;
    if (withLog) {
      log(isFullURL ? url : appController.baseUrl + url);
      log('$body');
      if (body is FormData) {
        log('${body.fields}');
        log('${body.files}');
      }
      log('$param');
    }
    try {
      response = await dio.put(
        isFullURL ? url : appController.baseUrl + url,
        data: body ?? "",
        options: Options(headers: await header),
        queryParameters: param,
        cancelToken: cancel,
      );
      if (withLog) {}
      if (response.data == null) throw 'connection error';
      ResponseModel data = ResponseModel.fromJson(response.data);
      return data;
    } catch (error, stackTrace) {
      if (error is DioException) {
        return handlingDioError(error);
      }
      return ResponseModel(success: false, message: error.toString());
    }
  }

  //======================= DELETE =====================
  Future<ResponseModel> deleteData({
    required String url,
    body,
    Map<String, dynamic>? param,
    bool isFullURL = false,
    CancelToken? cancel,
  }) async {
    Response response;
    if (withLog) {
      log(isFullURL ? url : appController.baseUrl + url);
      log('$body');
      if (body is FormData) {
        log('${body.fields}');
        log('${body.files}');
      }
      log('$param');
    }
    try {
      response = await dio.delete(
        isFullURL ? url : appController.baseUrl + url,
        data: body ?? "",
        options: Options(headers: await header),
        queryParameters: param,
        cancelToken: cancel,
      );
      if (withLog) {
        log('--------------- dataController ---------------');
        log('${response.data}');
      }
      if (response.data == null) throw 'connection error';
      ResponseModel data = ResponseModel.fromJson(response.data);
      return data;
    } catch (error) {
      if (error is DioException) {
        return handlingDioError(error);
      }
      if (error is SocketException) {
        return ResponseModel(success: false, message: 'No Internet');
      }

      return ResponseModel(success: false, message: error.toString());
    }
  }

  //==================== Download File ==================

  Future<String?> downloadVideo(
      {required String url,
      required String videoName,
      required Function(double) onProgress,
      required Map<String, dynamic> metaData,
      CancelToken? cancel,
      required int lessonId,
      required Function() onComplete}) async {
    try {
      String token = appController.token;
      appController.hasActiveDownload = true;

      FileDownloader().configureNotification(
        running: TaskNotification(
            'Downloading ${videoName}', 'file will be saved into my videos'),
        error: TaskNotification("Download field", "for file ${videoName}"),
        complete: TaskNotification("Download ${videoName} Completed",
            "you can see your downloaded video in my videos section"),
        progressBar: true,
      );

      final task = DownloadTask(
        url: url,
        filename: "$videoName.mp4",
        baseDirectory: BaseDirectory.root,
        metaData: jsonEncode(metaData),
        directory: '${appController.videosDirectoryPath}',
        updates: Updates.statusAndProgress,
        headers: {'Authorization': 'Bearer $token'},
        requiresWiFi: false,
        retries: 5,
        allowPause: true,
        taskId: "$lessonId",
      );

      TaskStatusUpdate result = await FileDownloader().download(task,
          onProgress: (val) => downloadProgressListener(val, lessonId),
          onStatus: (status) {
            log(status.name);
            if (status == TaskStatus.complete) {
              log("download completed");

              onComplete();
              appController.hasActiveDownload = false;

              fileDownloader.cancelTaskWithId(task.taskId);
              fileDownloader.database.deleteRecordWithId(task.taskId);
            } else if (status == TaskStatus.canceled ||
                status == TaskStatus.notFound ||
                status == TaskStatus.failed) {
              appController.hasActiveDownload = false;

              if (Get.isRegistered<NetworkVideoPlayerController>(
                  tag: "$lessonId")) {
                NetworkVideoPlayerController networkVideoPlayerController =
                    Get.find(tag: "$lessonId");
                networkVideoPlayerController.downloadVideoLoading = false;
                HiveHelper.downloads.delete(lessonId);
              }
            }
          });
      Response response = Response(requestOptions: RequestOptions(path: url));
      String filePath = '${appController.videosDirectoryPath}/$videoName.mp4';
    } catch (e) {}
    return null;
  }

  ResponseModel handlingDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.cancel:
        return ResponseModel(success: false, code: ErrorCode.CANCELED);
      case DioExceptionType.connectionTimeout:
        return ResponseModel(
          success: false,
          message: 'No Internet',
          code: ErrorCode.NO_INTERNET,
        );
      case DioExceptionType.receiveTimeout:
        return ResponseModel(
          success: false,
          message: 'No Internet',
          code: ErrorCode.NO_INTERNET,
        );
      case DioExceptionType.sendTimeout:
        return ResponseModel(
          success: false,
          message: 'No Internet',
          code: ErrorCode.NO_INTERNET,
        );
      case DioExceptionType.badResponse:
        switch (error.response!.statusCode) {
          case 403:
            // Nav.offAll(Pages.login);
            //TODO: unauth
            return ResponseModel(
              success: false,
              message: 'No Auth'.tr,
              code: ErrorCode.UN_AUTHORIZED,
            );
          case 422:
            //TODO:
            return ResponseModel.fromJson(error.response!.data)
              ..code = ErrorCode.VALIDATION_ERROR;
          case 500:
            return ResponseModel(
              success: false,
              code: ErrorCode.SERVER_ERROR,
              message: 'Server Error',
            );
          default:
            if (error.response != null && error.response!.data != null) {
              try {
                return ResponseModel.fromJson(error.response!.data);
              } catch (_) {}
            }
            String? errorMessage;
            if (error.response?.data != null) {
              errorMessage = error.response?.data['errors'][0];
            }
            return ResponseModel(
                success: false, message: errorMessage ?? 'Unknown Error');
        }
      case DioExceptionType.unknown:
        if (error.message!.contains('SocketException') ||
            error.message!.contains('Connection reset by peer')) {
          return ResponseModel(
            success: false,
            message: 'No Internet',
            code: ErrorCode.NO_INTERNET,
          );
        }
        return ResponseModel(
            success: false, message: error.error ?? 'Unknown Error');
      default:
        log("default");
        return ResponseModel(
            success: false, message: error.error ?? 'Unknown Error');
    }
  }

  cancelDownload(int lessonId) async {
    await FileDownloader().cancelTaskWithId("$lessonId");
    if (Get.isRegistered<NetworkVideoPlayerController>(tag: "$lessonId")) {
      NetworkVideoPlayerController networkVideoPlayerController =
          Get.find(tag: "$lessonId");
      networkVideoPlayerController.downloadVideoLoading = false;
      HiveHelper.downloads.delete(lessonId);
    }
  }
}

// Unused
// @pragma('vm:entry-point')
//  void downloadCallback(String id, int status, int progress) {
//   final SendPort? send =
//       IsolateNameServer.lookupPortByName('downloader_send_port');
//   log(status.toString());
//   send?.send([id, status, progress]);
// }

@pragma('vm:entry-point')
void downloadProgressListener(double progress, int lessonId) {
  log("incoming value of $progress");

  // PathProviderAndroid.registerWith();

  HiveHelper.downloads.put(lessonId, progress);
  if (Get.isRegistered<NetworkVideoPlayerController>(tag: "$lessonId")) {
    Get.find<NetworkVideoPlayerController>(tag: "$lessonId")
        .downloadedValue(progress > 0 ? progress : 0);
  }
}

FileDownloader fileDownloader = FileDownloader();
