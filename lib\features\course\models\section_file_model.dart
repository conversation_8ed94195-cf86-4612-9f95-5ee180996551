import 'dart:developer';

class SectionFileModel {
  String originalUrl;
  String title;
  String? supportLink;

  SectionFileModel(
      {required this.originalUrl, required this.title, this.supportLink});

  factory SectionFileModel.fromJson(Map<String, dynamic> json) {
    log(json.toString(), name: "file");
    return SectionFileModel(
        originalUrl: json['original_url'],
        supportLink: json['link'],
        title: json['original_url'].substring(
            json['original_url'].lastIndexOf('/') + 1,
            json['original_url'].length));
  }
}
