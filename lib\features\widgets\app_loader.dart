import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:skeletonizer/skeletonizer.dart';

class AppLoader<T> extends StatelessWidget {
  final Widget child;
  final Axis axis;
  final Container? listLoader;
  final bool loading;
  final int length;
  final bool isSliver;

  final Widget? customBone;

  const AppLoader(
      {super.key,
      required this.child,
      required this.loading,
      this.axis = Axis.vertical,
      this.length = 1,
      this.isSliver = false,
      this.listLoader,
      this.customBone});

  @override
  Widget build(BuildContext context) {
    // return loading? Center(child: CircularProgressIndicator()):child;
    return customBone != null
        ? Skeletonizer(enabled: loading, child: child)
        : isSliver
            ? Skeletonizer.sliver(
                enabled: loading,
                child: child,
              )
            : Skeletonizer(enabled: loading, child: child);
  }
}
