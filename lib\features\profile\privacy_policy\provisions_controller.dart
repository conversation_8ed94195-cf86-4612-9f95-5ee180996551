
import 'package:get/get.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/models/general/response_model.dart';

class ProvisionsController extends GetxController{
  AppController appController = Get.find();
  DataController dataController = Get.find();


  String usagePolicy = '';
  RxBool _usagePolicyLoading = true.obs;
  get usagePolicyLoading  => this._usagePolicyLoading.value;
  set usagePolicyLoading (value) => this._usagePolicyLoading.value = value;

  void loadUsagePolicyData() async {
    usagePolicyLoading =true;
    ResponseModel response;
    response = await dataController.getData(
      url: API.settingDataApi('usage_policy'),
    );
    if(response.success){
      usagePolicy = response.data[0];
      usagePolicyLoading =false;
    }else{
      loadUsagePolicyData();
    }
  }

  @override
  void onInit() {
    loadUsagePolicyData();
    super.onInit();
  }

}