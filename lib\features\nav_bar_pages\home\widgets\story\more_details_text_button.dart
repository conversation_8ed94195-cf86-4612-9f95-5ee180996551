import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_controller.dart';

class MoreDetailsTextButton extends StatelessWidget {
  const MoreDetailsTextButton({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final homeController = Get.find<HomePageController>();
    final currentNew = homeController.news[homeController.currentIndex];
    return Visibility(
      visible: currentNew.releated != null,
      child: Align(
        alignment: Alignment.bottomCenter,
        child: InkWell(
            onTap: () {
              Get.back();
              Nav.to(Pages.course, arguments: {
                'title': currentNew.releated?.title ?? '',
                'id': currentNew.releated?.id ?? 0,
                'from_home': true
              });
            },
            child: Padding(
              padding: EdgeInsets.only(bottom: 15),
              child: Text(
                'For more detail click here'.tr,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppStyle.whiteColor,
                      decoration: TextDecoration.underline,
                      decorationThickness: 2,
                      decorationColor: AppStyle.whiteColor,
                    ),
              ),
            )),
      ),
    );
  }
}
