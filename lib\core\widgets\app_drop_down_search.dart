import 'package:all_in_one/core/style/style.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../style/drop_down_input.dart';


class AppDropDownSearchWidget extends StatefulWidget {
  String hint;
  String chosenValue;
  List<String> items;
  final String? Function(String?)? validator;
  final Function(String)? afterSelection;
  final Function(int)? selectedIndex;
  final bool? isChanged;
  final double? width;
  final double? textSize;
  AppDropDownSearchWidget(
      {this.textSize,
        required this.hint,
        required this.items,
        this.validator,
        this.isChanged,
        required this.chosenValue,
        this.width,
        this.selectedIndex,
        this.afterSelection});
  @override
  _AppDropDownSearchWidgetState createState() => _AppDropDownSearchWidgetState(Value: chosenValue);
}

class _AppDropDownSearchWidgetState extends State<AppDropDownSearchWidget> {
  String? Value;
  _AppDropDownSearchWidgetState({this.Value});


  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: DropdownSearch<String>(
        popupProps: PopupProps.menu(
          searchFieldProps: TextFieldProps(
              style: TextStyle(fontSize: 14),
              cursorColor: AppStyle.secondaryColor,
              decoration: InputDecoration(
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: AppStyle.primaryColor),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: AppStyle.primaryColor),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(10),
                  borderSide: BorderSide(color: AppStyle.redColor),
                ),
                contentPadding: EdgeInsets.symmetric(vertical: 7,horizontal: 10),
                hintText: 'search'.tr,
              )
          ),
          showSearchBox: true,
          showSelectedItems: true,
          itemBuilder: (context,item,selected,_){
            return Container(
              color: selected ? AppStyle.primaryColor.withOpacity(0.1):Colors.transparent,
              padding: const EdgeInsets.symmetric(horizontal: 10,vertical: 12),
              child: Text(
                item,
                style: TextStyle(color: selected ? AppStyle.primaryColor:AppStyle.lightBlackColor,fontSize: 14),
              ),
            );
          },
        ),
        items:(_,__)=> widget.items,
        decoratorProps: DropDownDecoratorProps(
          baseStyle: TextStyle(fontSize: 14,overflow: TextOverflow.ellipsis),
          decoration: AppDropDownInputThemes.dropDownDecoration(widget.hint)
        ),
        onChanged: (value){
          setState(() {
            Value = value!;
            if(widget.afterSelection != null){
              widget.afterSelection!(value);
            }
            if(widget.selectedIndex != null){
              widget.selectedIndex!(widget.items.indexOf(value));
            }
          });
        },
        validator: widget.validator,
        selectedItem: Value==''||Value==null?null:Value,
      ),
    );
  }
}
