import 'dart:math';

import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/features/auth/signup/models/class_model.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/select_semester_bottom_sheet.dart';
import 'package:all_in_one/features/select_year/controller.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';
import 'package:all_in_one/features/widgets/app_loader.dart';

class SelectYearPage extends StatelessWidget {
  const SelectYearPage({super.key});

  @override
  Widget build(BuildContext context) {
    SelectYearController controller = Get.put(SelectYearController());
    return Scaffold(
        body: Obx(
      () => AppLoader(
        loading: controller.yearsLoading.value,
        child: SingleChildScrollView(
          child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                AppBarWidget(
                  centerWidget: Text(
                    ''.tr,
                    style: Theme.of(context).textTheme.titleLarge!.copyWith(
                        color: AppStyle.whiteColor,
                        fontWeight: AppFontWeight.bold),
                  ),
                  trailingIcon: SizedBox(
                    width: 35,
                    height: 35,
                  ),
                  leadingIcon: Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                  ),
                  trailingOnTap: () {},
                  leadingOnTap: () {
                    Get.back();
                  },
                ),
                SizedBox(height: 30),
                Text(
                  'Select a Study Year'.tr,
                  style: Get.textTheme.titleLarge!.copyWith(
                      color: AppStyle.primaryColor,
                      fontWeight: AppFontWeight.bold,
                      fontSize: 28),
                ),
                SizedBox(height: 30),
                if (controller.years.isEmpty && !controller.yearsLoading.value)
                  Assets.icons.noDataIcon.svg(),
                ..._getYears(controller.years, context, controller.majorId)
              ]),
        ),
      ),
    ));
  }
}

List<Widget> _getYears(
    List<YearModel> years, BuildContext context, String majorId) {
  return List.generate(
    years.length,
    (index) => InkWell(
        onTap: () {
          showModalBottomSheet(
            context: context,
            builder: (context) {
              return Builder(builder: (context) {
                return SelectSemesterBottomSheet(
                  yearId: years[index].id,
                  majorId: majorId,
                );
              });
            },
          );
        },
        child: YearOfStudy(index: index, year: years[index].title)),
  );
}

class YearOfStudy extends StatelessWidget {
  const YearOfStudy({
    super.key,
    required this.index,
    required this.year,
  });

  final int index;
  final String year;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Card(
        color: Colors.white30,
        child: Row(
          children: [
            Container(
              width: 20,
              height: 100,
              decoration: BoxDecoration(
                  color: AppStyle.primaryColor
                      .withOpacity(min((index + 1) / 5, 1)),
                  borderRadius: BorderRadius.only(
                      topRight: Radius.circular(10),
                      bottomRight: Radius.circular(10))),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 24.0),
                    child: Row(
                      children: [
                        Icon(
                          Icons.person_2_outlined,
                          color: AppStyle.primaryColor
                              .withOpacity(min((index + 1) / 5, 1)),
                        ),
                        Text(
                          year,
                          style: Get.textTheme.titleLarge!
                              .copyWith(color: AppStyle.primaryColor),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
      ),
    );
  }
}
