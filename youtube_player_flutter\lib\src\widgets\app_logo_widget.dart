import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import '../enums/player_state.dart';
import '../utils/youtube_player_controller.dart';

class AppLogoWidget extends StatefulWidget {
  final YoutubePlayerController? controller;

  /// Creates [PlayPauseButton] widget.
  AppLogoWidget({
    this.controller,
  });

  @override
  _AppLogoWidgetState createState() => _AppLogoWidgetState();
}

class _AppLogoWidgetState extends State<AppLogoWidget>
    with TickerProviderStateMixin {
  late YoutubePlayerController _controller;
  late Timer _timer;
  // ValueNotifier<bool> isVisibleLoge = ValueNotifier(true);

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final controller = YoutubePlayerController.of(context);
    if (controller == null) {
      assert(
        widget.controller != null,
        '\n\nNo controller could be found in the provided context.\n\n'
        'Try passing the controller explicitly.',
      );
      _controller = widget.controller!;
    } else {
      _controller = controller;
    }
  }

  playingTimerFunction() {
    _timer = new Timer(const Duration(milliseconds: 3500), () {
      _controller.isVisibleLoge.value = false;
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final _playerState = _controller.value.playerState;
    if ((!_controller.flags.autoPlay && _controller.value.isReady) ||
        _playerState == PlayerState.playing ||
        (_playerState == PlayerState.paused)) {
      playingTimerFunction();
    }
    return PositionedDirectional(
      start: 10,
      bottom: 5,
      child: SizedBox(
        width: 100,
        height: 45,
        child: Align(
          alignment: Alignment.center,
          child: ValueListenableBuilder(
            valueListenable: _controller.isVisibleLoge,
            builder: (context, bool isVisible, _) {
              if (isVisible) {
                return Material(
                  color: Colors.black87,
                  child: Image.asset(
                    'assets/app_name.png',
                    package: 'youtube_player_flutter',
                    width: 95,
                    height: 35,
                    color: Colors.white,
                    fit: BoxFit.fill,
                  ),
                );
              } else {
                return const SizedBox();
              }
            },
          ),
        ),
      ),
    );
  }
}
