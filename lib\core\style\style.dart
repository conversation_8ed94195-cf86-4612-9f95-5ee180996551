import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:all_in_one/core/constants/localization.dart';

import 'inputs.dart';

abstract class AppStyle {
  static Color primaryColor = const Color(0xFF0648a6);
  static const lightPrimaryColor = const Color(0xFF6062FE);
  static const secondaryColor = const Color(0xFFBCC5CF);
  static const spacesColor = const Color(0xFFE8E8E8);

  static const redColor = const Color(0xFFFF5050);
  static const ixCodersColor = const Color(0xffffcc66);
  static const overLayColor = const Color(0xFF0003FF);
  static const textFieldColor = const Color(0xFFCDCDFC);
  static const lightRedColor = const Color(0xFFF98B71);
  static const greyColor = Color(0xFF545454);
  static const lightGreyColor = Color(0xFFDCDCDC);
  static const whiteBackgroundColor = Color(0xFFf5f5f5);
  static const lightBackgroundColor = Color(0xFFFCFCFC);
  static const notificationBackgroundColor = Color(0xFFF6F6F6);

  static const lightBlackColor = Color(0xFF505050);
  static const blackTextColor = Color(0xFF303030);
  static const mediumBlackTextColor = const Color(0xFF414141);
  static const orangeColor = Color(0xFFD95F30);

  static const blackColor = Color(0xFF000000);
  static const whiteColor = const Color(0xFFFFFFFF);
  static const greyTextColor = const Color(0xFF8A8A8A);
  static const lightGreyTextColor = const Color(0xFF9F9EA2);
  static const boldGreyTextColor = const Color(0xFF606060);

  static const blueTextColor = const Color(0xFF57A4FF);
  static const dividerColor = const Color(0xFFD0D0D0);

  static const goldColor = const Color(0xFFFFC107);
  static const boldGoldColor = const Color(0xFFF5A623);
  static const favoriteColor = const Color(0xFFE8421C);
  static const seenColor = const Color(0xFF7ED321);
  static const greenColor = const Color(0xFF37A755);

  static ThemeData lightTheme(AppLocalization local) => ThemeData(
      useMaterial3: true,
      primaryColor: primaryColor,
      dividerColor: dividerColor,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      scaffoldBackgroundColor: const Color(0xFFFFFFFF),
      colorScheme: ColorScheme.light(
        brightness: Brightness.light,
        primary: primaryColor,
        secondary: secondaryColor,
        surface: const Color(0xFFFFFFFF),
        error: redColor,
      ),
      fontFamily: 'NotoSans',
      textTheme: TextTheme(
        displayLarge:
            const TextStyle(fontSize: 32, fontWeight: AppFontWeight.bold),
        displayMedium:
            const TextStyle(fontSize: 30, fontWeight: AppFontWeight.bold),
        displaySmall:
            const TextStyle(fontSize: 28, fontWeight: AppFontWeight.bold),
        headlineLarge:
            const TextStyle(fontSize: 24, fontWeight: AppFontWeight.bold),
        headlineMedium:
            const TextStyle(fontSize: 22, fontWeight: AppFontWeight.bold),
        headlineSmall:
            const TextStyle(fontSize: 20, fontWeight: AppFontWeight.bold),
        titleLarge:
            const TextStyle(fontSize: 18, fontWeight: AppFontWeight.regular),
        titleMedium:
            const TextStyle(fontSize: 16, fontWeight: AppFontWeight.regular),
        titleSmall:
            const TextStyle(fontSize: 15, fontWeight: AppFontWeight.regular), //
        bodyLarge:
            const TextStyle(fontSize: 14, fontWeight: AppFontWeight.regular),
        bodyMedium:
            const TextStyle(fontSize: 13, fontWeight: AppFontWeight.regular),
        bodySmall:
            const TextStyle(fontSize: 12, fontWeight: AppFontWeight.regular),
        labelLarge:
            const TextStyle(fontSize: 11, fontWeight: AppFontWeight.regular),
        labelMedium:
            const TextStyle(fontSize: 10, fontWeight: AppFontWeight.regular),
        labelSmall:
            const TextStyle(fontSize: 8, fontWeight: AppFontWeight.regular),
      ),
      inputDecorationTheme: AppInputFieldThemes.mainThemeDecoration());

  static ThemeData reviewTheme() => ThemeData(
      useMaterial3: true,
      primaryColor: Color.fromARGB(255, 21, 255, 0),
      dividerColor: dividerColor,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      scaffoldBackgroundColor: const Color(0xFFFFFFFF),
      colorScheme: ColorScheme.light(
        brightness: Brightness.light,
        primary: primaryColor,
        secondary: secondaryColor,
        surface: const Color(0xFFFFFFFF),
        error: redColor,
      ),
      fontFamily: 'NotoSans',
      textTheme: TextTheme(
        displayLarge:
            const TextStyle(fontSize: 32, fontWeight: AppFontWeight.bold),
        displayMedium:
            const TextStyle(fontSize: 30, fontWeight: AppFontWeight.bold),
        displaySmall:
            const TextStyle(fontSize: 28, fontWeight: AppFontWeight.bold),
        headlineLarge:
            const TextStyle(fontSize: 24, fontWeight: AppFontWeight.bold),
        headlineMedium:
            const TextStyle(fontSize: 22, fontWeight: AppFontWeight.bold),
        headlineSmall:
            const TextStyle(fontSize: 20, fontWeight: AppFontWeight.bold),
        titleLarge:
            const TextStyle(fontSize: 18, fontWeight: AppFontWeight.regular),
        titleMedium:
            const TextStyle(fontSize: 16, fontWeight: AppFontWeight.regular),
        titleSmall:
            const TextStyle(fontSize: 15, fontWeight: AppFontWeight.regular), //
        bodyLarge:
            const TextStyle(fontSize: 14, fontWeight: AppFontWeight.regular),
        bodyMedium:
            const TextStyle(fontSize: 13, fontWeight: AppFontWeight.regular),
        bodySmall:
            const TextStyle(fontSize: 12, fontWeight: AppFontWeight.regular),
        labelLarge:
            const TextStyle(fontSize: 11, fontWeight: AppFontWeight.regular),
        labelMedium:
            const TextStyle(fontSize: 10, fontWeight: AppFontWeight.regular),
        labelSmall:
            const TextStyle(fontSize: 8, fontWeight: AppFontWeight.regular),
      ),
      inputDecorationTheme: AppInputFieldThemes.mainThemeDecoration());
  static lightStatusBar() {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: blackTextColor.withOpacity(0.8),
        statusBarIconBrightness: Brightness.light,
      ),
    );
  }

  static darkStatusBar() {
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
  }

  static List<BoxShadow> boxShadow16 = [
    BoxShadow(
      offset: Offset(0, 3),
      blurRadius: 6,
      color: Colors.black.withOpacity(0.16),
    ),
  ];
}

abstract class AppFontWeight {
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight bold = FontWeight.w700;
}
