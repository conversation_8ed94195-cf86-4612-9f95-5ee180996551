
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/url_launcher.dart';
import 'package:all_in_one/core/widgets/cached_image_widget.dart';
import 'package:all_in_one/features/course/models/subject_model.dart';

Future<void> showTeacherDetailBottomSheet(BuildContext context,Teacher teacher,bool canContactWithTeacher) async {
  await showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    useRootNavigator: false,
    enableDrag: true,
    backgroundColor: Colors.transparent,
    barrierColor: Colors.black26,
    builder: (context) {
      return TeacherDetailBottomSheet(teacher: teacher,canContactWithTeacher: canContactWithTeacher,);
    },
  );
}

class TeacherDetailBottomSheet extends StatelessWidget {
  Teacher teacher;
  bool canContactWithTeacher;
  TeacherDetailBottomSheet({required this.teacher,required this.canContactWithTeacher});
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  Widget ContactInfoWidget({required BuildContext context,required String data,required Widget icon,required VoidCallback onTap}){
    return InkWell(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: AppStyle.whiteBackgroundColor,
          borderRadius: BorderRadius.circular(10),
        ),
        padding: EdgeInsets.symmetric(horizontal: 30,vertical: 20),
        child: Row(
          children: [
            icon,
            const SizedBox(width: 15,),
            Text(
              data,
              style: Theme.of(context).textTheme.titleMedium!.copyWith(
                  color: AppStyle.primaryColor,
                  fontWeight: FontWeight.w700
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
            color: AppStyle.whiteColor,
          borderRadius: BorderRadius.only(
            topRight: Radius.circular(15),
            topLeft: Radius.circular(15)
          )
        ),
        padding: EdgeInsets.symmetric(horizontal: 20,vertical: 30),
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              CachedImageWidget(
                imageUrl: teacher.avatar,
                height: 200,
                borderRadius: BorderRadius.all(Radius.circular(15)),
                width: Get.width,
                loadingHeight: 200,
              ),
              const SizedBox(height: 15,),
              Row(
                children: [
                  Assets.icons.userIcon.svg(),
                  const SizedBox(width: 8,),
                  Text(
                      'Teacher name'.trParams({
                        'name': teacher.name
                      })!,
                    style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      color: AppStyle.lightBlackColor,
                      fontWeight: FontWeight.w700
                    ),
                  )
                ],
              ),
              const SizedBox(height: 10),
              Visibility(
                visible: teacher.about.isNotEmpty,
                child: Row(
                  children: [
                    Assets.icons.notebookIcon.svg(),
                    const SizedBox(width: 8,),
                    Text(
                        'Description'.tr,
                      style: Theme.of(context).textTheme.titleMedium!.copyWith(
                          color: AppStyle.lightBlackColor,
                          fontWeight: FontWeight.w700
                      ),
                    )
                  ],
                ),
              ),
              Visibility(
                  visible: teacher.about.isNotEmpty,
                  child: const SizedBox(height: 10),
              ),
              Visibility(
                visible: teacher.about.isNotEmpty,
                child: Text(
                  teacher.about,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: AppStyle.blackColor,
                      fontWeight: FontWeight.w500
                  ),
                ),
              ),
              const SizedBox(height: 18),
              Visibility(
                visible: canContactWithTeacher,
                child: Text(
                  'Contact with'.tr,
                  style: Theme.of(context).textTheme.titleMedium!.copyWith(
                      color: AppStyle.lightBlackColor,
                      fontWeight: FontWeight.w700
                  ),
                ),
              ),
              Visibility(
                  visible: canContactWithTeacher,
                  child: const SizedBox(height: 15)),
              Visibility(
                visible: canContactWithTeacher,
                child: ContactInfoWidget(
                  context: context,
                  data: 'Contact with Telegram'.tr,
                  icon: Assets.icons.telegramIcon.svg(),
                  onTap: ()async{
                    await UrlLauncher.openTelegram(
                        phone: '+'+teacher.dialCountryCode+teacher.phone
                    );
                  }
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
