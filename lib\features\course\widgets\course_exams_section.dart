import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/app_animation/item_animation.dart';
import 'package:all_in_one/core/widgets/no_data_widget.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/course/contollers/course_controller.dart';
import 'package:all_in_one/features/course/exam_page.dart';

class CourseExamsSection extends StatelessWidget {
  const CourseExamsSection();

  @override
  Widget build(BuildContext context) {
    CourseController controller = Get.find();

    return AnimationLimiter(
      child: !controller.areQuizzesEmpty?ListView.builder(
        itemCount: controller.subjectModel.lectures.length,
        padding: EdgeInsets.zero,
        itemBuilder: (context,index){
          return ItemAnimation(
            index: index,
            elementCount: controller.subjectModel.lectures.length,
            child: Visibility(
              visible: controller.subjectModel.lectures[index].hasQuiz,
              child: Obx(
                  ()=> InkWell(
                    onTap: controller.subjectModel.lectures[index].isSubscription.value?
                        ()=> Get.to(
                        ExamPage(examName: controller.subjectModel.lectures[index].title,),
                        arguments: {"id": controller.subjectModel.lectures[index].id}
                    )
                        :(){
                      controller.appController.showToast(
                          context,
                          message: 'Please participate in this research first'.tr,
                          status: ToastStatus.warning
                      );
                    },
                    child: Container(
                      margin: EdgeInsets.only(left: 15,right: 15,bottom: 10),
                      padding: EdgeInsets.symmetric(horizontal: 16,vertical: 17),
                      decoration: BoxDecoration(
                        color: AppStyle.whiteBackgroundColor,
                        borderRadius: BorderRadius.all(Radius.circular(10)),
                        boxShadow: [
                          BoxShadow(
                              color: Colors.grey.withOpacity(0.2),
                              blurRadius: 1.0,
                              spreadRadius: 1,
                              offset: const Offset(-2, 1) // changes position of shadow
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            controller.subjectModel.lectures[index].quizTitle,
                            softWrap: true,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                            style: Theme.of(context).textTheme.titleMedium!.copyWith(
                                color: AppStyle.primaryColor
                            ),
                          ),
                          const SizedBox(height: 6,),
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  controller.subjectModel.lectures[index].quizTitle,
                                  softWrap: true,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                                      color: AppStyle.primaryColor
                                  ),
                                ),
                              ),
                              SizedBox(width: 15,),
                              controller.subjectModel.lectures[index].isSubscription.value?
                              SizedBox():
                              Assets.icons.lockIcon.svg()
                            ],
                          ),
                        ],
                      ),
                    ),
                  )
              ),
          
            
            ),
          );
        },
      )
          :NoDataWidget(
        imageWidth: 100,
        imageHeight: 100,
      ),
    );
  }
}
