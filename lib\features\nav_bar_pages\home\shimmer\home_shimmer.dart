import 'package:flutter/material.dart';
import 'package:all_in_one/core/widgets/skeleton_widget.dart';

import 'home_courses_section_shimmer.dart';
import 'home_news_section_shimmer.dart';

class HomeShimmer extends StatelessWidget {
  const HomeShimmer();

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Column(
        children: [
          const SizedBox(
            height: 18,
          ),
          SkeletonWidget(
            width: double.infinity,
            height: 160,
            radius: 15,
            margin: EdgeInsetsDirectional.symmetric(horizontal: 20),
          ),
          const SizedBox(
            height: 18,
          ),
          HomeNewsSectionShimmer(),

          const SizedBox(
            height: 18,
          ),
          HomeCoursesSectionShimmer(),
          const SizedBox(
            height: 25,
          ),
          SkeletonWidget(
            width: double.infinity,
            height: 65,
            radius: 12,
            margin: EdgeInsetsDirectional.symmetric(horizontal: 15),
          ),
          const SizedBox(
            height: 20,
          ),

          // HomeTasksSectionShimmer(),
        ],
      ),
    );
  }
}
