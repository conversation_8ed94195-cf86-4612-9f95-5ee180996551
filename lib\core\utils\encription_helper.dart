import 'dart:developer';
import 'dart:io';
import 'dart:isolate';
import 'dart:convert';
import 'package:encrypt/encrypt.dart';

decryptLink(String base64EncryptedData) {
  if (base64EncryptedData.isEmpty) {
    return '';
  }
  log("decoding ..");
  final iv = IV.fromUtf8("Hkald6&ksl#usk9@");
  final key = Key.fromUtf8("E4rqzxa37VCwz7I/enrUy1S/xwH6BR==");
  final encrypter = Encrypter(AES(key, mode: AESMode.cbc, padding: 'PKCS7'));
  final encrypted = Encrypted.fromBase64(base64EncryptedData);
  final link = encrypter.decrypt(encrypted, iv: iv);
  log("link:$link");
  return link;
}

Future encryptFile(String basePath, String videoName) async {
  File inFile = new File("$basePath/${videoName}.mp4");
  File outFile = new File("$basePath/${videoName}.aes");

  bool outFileExists = await outFile.exists();

  if (!outFileExists) {
    await outFile.create();
  }
  await outFile.writeAsBytes(await inFile.readAsBytes());
  return;
  final videoFileContents = await inFile.readAsStringSync(encoding: latin1);

  final key = Key.fromUtf8('abcdefghgklmnopqrstvwxyz!@#%^&*(');
  final iv = IV.fromLength(16);

  final encrypter = Encrypter(AES(key));

  final encrypted = encrypter.encrypt(videoFileContents, iv: iv);
  // inFile.delete();
  await encrypter.decrypt(encrypted, iv: iv);
  await outFile.writeAsBytes(encrypted.bytes);
}

Future decryptFile(String basePath, String videoName, SendPort sendPort) async {
  log('start decr');
  File inFile = new File("$basePath/${videoName}.aes");
  File outFile = new File("$basePath/video.mp4");

  bool outFileExists = await outFile.exists();

  if (!outFileExists) {
    await outFile.create();
  }
  await outFile.writeAsBytes(await inFile.readAsBytes());

  // final videoFileContents = await inFile.readAsBytesSync();

  // final key = Key.fromUtf8('abcdefghgklmnopqrstvwxyz!@#%^&*(');
  // final iv = IV.fromLength(16);

  // final encrypter = Encrypter(AES(key));

  // final encryptedFile = Encrypted(videoFileContents);

  // final decrypted = encrypter.decrypt(encryptedFile, iv: iv);

  // final decryptedBytes = latin1.encode(decrypted);
  // await outFile.writeAsBytes(decryptedBytes);
  sendPort.send('done');
  log('end dec');
}
