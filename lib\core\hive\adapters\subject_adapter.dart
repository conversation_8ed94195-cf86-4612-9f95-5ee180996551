
import 'package:hive/hive.dart';
import 'subject_section_adapter.dart';
part 'subject_adapter.g.dart';

@HiveType(typeId: 0)
class SubjectAdapter extends HiveObject{

  @HiveField(0)
  int id;
  @HiveField(1)
  String title;
  @HiveField(2)
  String media;
  @HiveField(3)
  String teacherName;
  @HiveField(4)
  int sectionsCnt;
  @HiveField(5)
  String banner;
  @HiveField(6)
  List<SubjectSectionAdapter> sections;


  SubjectAdapter({
      required this.title,
      required this.id,
      required this.media,
      required this.teacherName,
      required this.banner,
      required this.sectionsCnt,
      required this.sections
    });


}
