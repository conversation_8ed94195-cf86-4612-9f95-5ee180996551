import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:share_plus/share_plus.dart';
import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/app_text_feild.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/image.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';

// Offline Task model for local storage
class OfflineTask {
  final int id;
  final String title;
  final String details;
  final String date;
  final int colorValue;
  final bool isCompleted;

  OfflineTask({
    required this.id,
    required this.title,
    required this.details,
    required this.date,
    required this.colorValue,
    this.isCompleted = false,
  });

  Color get color => Color(colorValue);

  Map<String, dynamic> toJson() => {
        'id': id,
        'title': title,
        'details': details,
        'date': date,
        'colorValue': colorValue,
        'isCompleted': isCompleted,
      };

  factory OfflineTask.fromJson(Map<String, dynamic> json) => OfflineTask(
        id: json['id'],
        title: json['title'],
        details: json['details'],
        date: json['date'],
        colorValue: json['colorValue'],
        isCompleted: json['isCompleted'] ?? false,
      );
}

// Controller for the offline task manager
class OfflineTaskController extends GetxController {
  late final GetStorage box;
  RxList<OfflineTask> tasks = <OfflineTask>[].obs;
  RxList<OfflineTask> filteredTasks = <OfflineTask>[].obs;

  final titleController = TextEditingController();
  final detailsController = TextEditingController();
  final dateController = TextEditingController();

  final formKey = GlobalKey<FormState>();

  RxInt selectedColorIndex = 0.obs;

  List<Color> taskColors = [
    Colors.blue,
    Colors.red,
    Colors.green,
    Colors.orange,
    Colors.purple,
    Colors.teal,
  ];

  @override
  void onInit() async {
    super.onInit();
    try {
      await GetStorage.init('offline_tasks');
      box = GetStorage('offline_tasks');
    } catch (e) {
      // Create a fallback storage if initialization fails
      box = GetStorage();
    }
    loadTasks();
  }

  void loadTasks() {
    if (box.hasData('tasks')) {
      final List<dynamic> storedTasks = box.read('tasks');
      tasks.value =
          storedTasks.map((task) => OfflineTask.fromJson(task)).toList();
      filteredTasks.value = List.from(tasks);
    }
  }

  void saveTasks() {
    box.write('tasks', tasks.map((task) => task.toJson()).toList());
  }

  void addTask() {
    if (formKey.currentState!.validate()) {
      final newTask = OfflineTask(
        id: DateTime.now().millisecondsSinceEpoch,
        title: titleController.text,
        details: detailsController.text,
        date: dateController.text,
        colorValue: taskColors[selectedColorIndex.value].hashCode,
      );

      tasks.add(newTask);
      filteredTasks.value = List.from(tasks);
      saveTasks();

      titleController.clear();
      detailsController.clear();
      dateController.clear();
      selectedColorIndex.value = 0;
    }
  }

  void deleteTask(int id) {
    tasks.removeWhere((task) => task.id == id);
    filteredTasks.value = List.from(tasks);
    saveTasks();
  }

  void toggleTaskCompletion(int id) {
    final index = tasks.indexWhere((task) => task.id == id);
    if (index != -1) {
      final task = tasks[index];
      final updatedTask = OfflineTask(
        id: task.id,
        title: task.title,
        details: task.details,
        date: task.date,
        colorValue: task.colorValue,
        isCompleted: !task.isCompleted,
      );

      tasks[index] = updatedTask;
      filteredTasks.value = List.from(tasks);
      saveTasks();
    }
  }

  void filterTasks(String filter) {
    switch (filter) {
      case 'all':
        filteredTasks.value = List.from(tasks);
        break;
      case 'completed':
        filteredTasks.value = tasks.where((task) => task.isCompleted).toList();
        break;
      case 'pending':
        filteredTasks.value = tasks.where((task) => !task.isCompleted).toList();
        break;
      case 'today':
        final today = DateTime.now();
        final formattedToday = '${today.year}-${today.month}-${today.day}';
        filteredTasks.value =
            tasks.where((task) => task.date == formattedToday).toList();
        break;
    }
  }
}

class AlternativeUi extends StatelessWidget {
  const AlternativeUi({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the AppController to check if isInReview is true
    AppController appController;
    try {
      appController = Get.find<AppController>();
    } catch (e) {
      // For testing purposes, create a mock AppController
      appController = Get.put(AppController());
      appController.isInReview = true;
    }

    // Otherwise, show the original articles UI
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 180.0,
            floating: false,
            pinned: true,
            backgroundColor: AppStyle.primaryColor,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                "articles".tr,
                style: Get.textTheme.titleLarge?.copyWith(
                  color: AppStyle.whiteColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppStyle.primaryColor.withOpacity(0.8),
                      AppStyle.primaryColor,
                    ],
                  ),
                ),
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 50.0),
                    child: Assets.icons.book.svg(
                      width: 60,
                      height: 60,
                    ),
                  ),
                ),
              ),
            ),
            actions: [
              IconButton(
                icon: Icon(Icons.search, color: AppStyle.whiteColor),
                onPressed: () {
                  DialogHelper.showDialog(
                    dialogBody: SearchDialogWidget(),
                  );
                },
              ),
            ],
          ),
          SliverPadding(
            padding: const EdgeInsets.all(16.0),
            sliver: SliverToBoxAdapter(
              child: Text(
                "featured_articles".tr,
                style: Get.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: SizedBox(
              height: 220,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: featuredArticles.length,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemBuilder: (context, index) => FeaturedArticleCard(
                  model: featuredArticles[index],
                ),
              ),
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.all(16.0),
            sliver: SliverToBoxAdapter(
              child: Text(
                "all_articles".tr,
                style: Get.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) => ArticleListItem(model: list[index]),
                childCount: list.length,
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: SizedBox(height: 20),
          ),
        ],
      ),
    );
  }
}

// Offline Task Manager UI
class OfflineTaskManager extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(OfflineTaskController());

    return Scaffold(
      appBar: AppBar(
        title: Text(
          "Task Manager".tr,
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: AppStyle.primaryColor,
        actions: [
          PopupMenuButton<String>(
            onSelected: controller.filterTasks,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'all',
                child: Text('All Tasks'.tr),
              ),
              PopupMenuItem(
                value: 'completed',
                child: Text('Completed'.tr),
              ),
              PopupMenuItem(
                value: 'pending',
                child: Text('Pending'.tr),
              ),
              PopupMenuItem(
                value: 'today',
                child: Text('Today\'s Tasks'.tr),
              ),
            ],
          ),
        ],
      ),
      body: Obx(
        () => controller.filteredTasks.isEmpty
            ? Center(
                child: Text(
                  "No tasks found".tr,
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
              )
            : ListView.builder(
                itemCount: controller.filteredTasks.length,
                padding: EdgeInsets.all(16),
                itemBuilder: (context, index) {
                  final task = controller.filteredTasks[index];
                  return TaskCard(
                    task: task,
                    onDelete: () => controller.deleteTask(task.id),
                    onToggle: () => controller.toggleTaskCompletion(task.id),
                  );
                },
              ),
      ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppStyle.primaryColor,
        child: Icon(Icons.add),
        onPressed: () {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            builder: (context) => AddTaskBottomSheet(),
          );
        },
      ),
    );
  }
}

// Task Card Widget
class TaskCard extends StatelessWidget {
  final OfflineTask task;
  final VoidCallback onDelete;
  final VoidCallback onToggle;

  const TaskCard({
    required this.task,
    required this.onDelete,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: task.color.withAlpha(128), // 0.5 opacity = 128 alpha
          width: 1,
        ),
      ),
      child: InkWell(
        onTap: onToggle,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      task.title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        decoration: task.isCompleted
                            ? TextDecoration.lineThrough
                            : null,
                        color: task.isCompleted ? Colors.grey : Colors.black,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: Icon(
                      task.isCompleted
                          ? Icons.check_circle
                          : Icons.circle_outlined,
                      color: task.color,
                    ),
                    onPressed: onToggle,
                  ),
                  IconButton(
                    icon: Icon(Icons.delete_outline, color: Colors.red),
                    onPressed: onDelete,
                  ),
                ],
              ),
              if (task.details.isNotEmpty) ...[
                SizedBox(height: 8),
                Text(
                  task.details,
                  style: TextStyle(
                    fontSize: 14,
                    color: task.isCompleted ? Colors.grey : Colors.black87,
                    decoration:
                        task.isCompleted ? TextDecoration.lineThrough : null,
                  ),
                ),
              ],
              SizedBox(height: 12),
              Row(
                children: [
                  Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                  SizedBox(width: 4),
                  Text(
                    task.date,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Add Task Bottom Sheet
class AddTaskBottomSheet extends StatelessWidget {
  final OfflineTaskController controller;

  AddTaskBottomSheet({Key? key})
      : controller = Get.find<OfflineTaskController>(),
        super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
        left: 16,
        right: 16,
        top: 16,
      ),
      child: Form(
        key: controller.formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              "Add New Task".tr,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: controller.titleController,
              decoration: InputDecoration(
                labelText: "Task Title".tr,
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "Please enter a title".tr;
                }
                return null;
              },
            ),
            SizedBox(height: 12),
            TextFormField(
              controller: controller.detailsController,
              decoration: InputDecoration(
                labelText: "Details (optional)".tr,
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            SizedBox(height: 12),
            TextFormField(
              controller: controller.dateController,
              decoration: InputDecoration(
                labelText: "Task Date".tr,
                border: OutlineInputBorder(),
                suffixIcon: Icon(Icons.calendar_today),
              ),
              readOnly: true,
              onTap: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime.now().subtract(Duration(days: 365)),
                  lastDate: DateTime.now().add(Duration(days: 365)),
                );

                if (date != null) {
                  controller.dateController.text =
                      "${date.year}-${date.month}-${date.day}";
                }
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "Please select a date".tr;
                }
                return null;
              },
            ),
            SizedBox(height: 16),
            Text(
              "Task Color".tr,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            SizedBox(
              height: 50,
              child: GetBuilder<OfflineTaskController>(
                builder: (controller) => ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: controller.taskColors.length,
                  itemBuilder: (context, index) {
                    final isSelected =
                        controller.selectedColorIndex.value == index;
                    return GestureDetector(
                      onTap: () {
                        controller.selectedColorIndex.value = index;
                        controller.update(); // Update the UI
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        margin: EdgeInsets.only(right: 8),
                        decoration: BoxDecoration(
                          color: controller.taskColors[index],
                          shape: BoxShape.circle,
                          border: isSelected
                              ? Border.all(color: Colors.black, width: 2)
                              : null,
                        ),
                        child: isSelected
                            ? Icon(Icons.check, color: Colors.white)
                            : null,
                      ),
                    );
                  },
                ),
              ),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                if (controller.formKey.currentState!.validate()) {
                  controller.addTask();
                  Navigator.pop(context);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppStyle.primaryColor,
                padding: EdgeInsets.symmetric(vertical: 12),
              ),
              child: Text(
                "Add New".tr,
                style: TextStyle(
                  fontSize: 16,
                  color: AppStyle.whiteColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}

class SearchDialogWidget extends StatelessWidget {
  final TextEditingController searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Expanded(
              child: AppTextField(
                searchController,
                type: FieldTypeEnum.MainTheme,
                hint: "search_articles".tr,
              ),
            ),
            IconButton(
              icon: Icon(Icons.search, color: AppStyle.primaryColor),
              onPressed: () {
                Get.back();
                // Implement search functionality
              },
            ),
          ],
        ),
        SizedBox(height: 10),
        AppButton(
          text: "cancel".tr,
          onTap: () => Get.back(),
        ),
      ],
    );
  }
}

class FeaturedArticleCard extends StatelessWidget {
  final AlternativeMode model;

  const FeaturedArticleCard({required this.model});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () =>
          Nav.to(Pages.alternativeDetails, params: {"id": model.id.toString()}),
      child: Container(
        width: 280,
        margin: const EdgeInsets.only(right: 16, bottom: 8, top: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            children: [
              AppImage(
                path: model.imageUrl,
                type: ImageType.CachedNetwork,
                width: 280,
                height: 200,
                fit: BoxFit.cover,
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                      colors: [
                        Colors.black.withOpacity(0.8),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        model.title,
                        style: Get.textTheme.titleMedium?.copyWith(
                          color: AppStyle.whiteColor,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        model.category,
                        style: Get.textTheme.bodySmall?.copyWith(
                          color: AppStyle.whiteColor.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ArticleListItem extends StatelessWidget {
  final AlternativeMode model;

  const ArticleListItem({required this.model});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () =>
          Nav.to(Pages.alternativeDetails, params: {"id": model.id.toString()}),
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        decoration: BoxDecoration(
          color: AppStyle.whiteColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                bottomLeft: Radius.circular(12),
              ),
              child: AppImage(
                path: model.imageUrl,
                type: ImageType.CachedNetwork,
                width: 100,
                height: 100,
                fit: BoxFit.cover,
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppStyle.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            model.category,
                            style: Get.textTheme.bodySmall?.copyWith(
                              color: AppStyle.primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Spacer(),
                        Text(
                          model.readTime,
                          style: Get.textTheme.bodySmall?.copyWith(
                            color: AppStyle.greyColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      model.title,
                      style: Get.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      model.description,
                      style: Get.textTheme.bodySmall?.copyWith(
                        color: AppStyle.greyColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AlternativeUiDetails extends StatelessWidget {
  const AlternativeUiDetails({super.key});

  @override
  Widget build(BuildContext context) {
    AlternativeMode model = list[int.parse(Get.parameters["id"]!) - 1];
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 250.0,
            floating: false,
            pinned: true,
            backgroundColor: AppStyle.primaryColor,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: AppStyle.whiteColor),
              onPressed: () => Get.back(),
            ),
            actions: [
              GetBuilder<AppController>(
                builder: (appController) => IconButton(
                  icon: Icon(
                      appController.isBookmarked(model.id.toString())
                          ? Icons.bookmark
                          : Icons.bookmark_border,
                      color: AppStyle.whiteColor),
                  onPressed: () {
                    if (appController.isBookmarked(model.id.toString())) {
                      appController.removeBookmark(model.id.toString());
                    } else {
                      appController.addBookmark(model.id.toString());
                    }
                    appController.update();
                  },
                ),
              ),
              IconButton(
                icon: Icon(Icons.share, color: AppStyle.whiteColor),
                onPressed: () {
                  try {
                    final box = context.findRenderObject() as RenderBox?;

                    Share.share(
                      'Check out this article: ${model.title}\n\n${model.description.substring(0, 100)}...',
                      sharePositionOrigin:
                          box!.localToGlobal(Offset.zero) & box.size,
                    );
                  } catch (e) {
                    Get.snackbar(
                      'Error',
                      'Could not share article: $e',
                      snackPosition: SnackPosition.BOTTOM,
                    );
                  }
                },
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              background: AppImage(
                path: model.imageUrl,
                type: ImageType.CachedNetwork,
                width: Get.width,
                height: 250,
                fit: BoxFit.cover,
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                        decoration: BoxDecoration(
                          color: AppStyle.primaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          model.category,
                          style: Get.textTheme.bodySmall?.copyWith(
                            color: AppStyle.primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Spacer(),
                      Icon(Icons.access_time,
                          size: 16, color: AppStyle.greyColor),
                      SizedBox(width: 4),
                      Text(
                        model.readTime,
                        style: Get.textTheme.bodySmall?.copyWith(
                          color: AppStyle.greyColor,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 16),
                  Text(
                    model.title,
                    style: Get.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 16,
                        backgroundImage: NetworkImage(model.authorImage),
                      ),
                      SizedBox(width: 8),
                      Text(
                        model.author,
                        style: Get.textTheme.bodyMedium,
                      ),
                      Text(
                        " • ${model.date}",
                        style: Get.textTheme.bodyMedium?.copyWith(
                          color: AppStyle.greyColor,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 24),
                  Text(
                    model.description,
                    style: Get.textTheme.bodyLarge?.copyWith(height: 1.6),
                  ),
                  SizedBox(height: 24),
                  if (model.relatedArticles.isNotEmpty) ...[
                    Text(
                      "related_articles".tr,
                      style: Get.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 16),
                    ...model.relatedArticles.map((id) {
                      AlternativeMode related =
                          list.firstWhere((element) => element.id == id);
                      return RelatedArticleItem(model: related);
                    }).toList(),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class RelatedArticleItem extends StatelessWidget {
  final AlternativeMode model;

  const RelatedArticleItem({required this.model});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () =>
          Nav.to(Pages.alternativeDetails, params: {"id": model.id.toString()}),
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppStyle.whiteColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppStyle.greyColor.withOpacity(0.2)),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: AppImage(
                path: model.imageUrl,
                type: ImageType.CachedNetwork,
                width: 70,
                height: 70,
                fit: BoxFit.cover,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    model.title,
                    style: Get.textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: 4),
                  Text(
                    model.readTime,
                    style: Get.textTheme.bodySmall?.copyWith(
                      color: AppStyle.greyColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class AlternativeMode {
  final int id;
  final String imageUrl;
  final String title;
  final String description;
  final String category;
  final String readTime;
  final String author;
  final String authorImage;
  final String date;
  final List<int> relatedArticles;

  const AlternativeMode({
    required this.id,
    required this.imageUrl,
    required this.title,
    required this.description,
    this.category = '',
    this.readTime = '',
    this.author = '',
    this.authorImage = '',
    this.date = '',
    this.relatedArticles = const [],
  });
}

List<AlternativeMode> featuredArticles = [list[0], list[1], list[4]];

List<AlternativeMode> list = [
  AlternativeMode(
    id: 1,
    imageUrl:
        'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?q=80&w=2070&auto=format&fit=crop',
    title: 'الذكاء في التعليم',
    description:
        '''تقنية البلوكتشين، التي تم تطويرها في الأصل كالبنية التحتية الأساسية لعملة البيتكوين، لها تطبيقات تتجاوز العملات المشفرة. يتم استكشاف نظام السجل اللامركزي وغير القابل للتغيير في مختلف الصناعات، بما في ذلك التمويل، وإدارة سلاسل التوريد، والرعاية الصحية. على سبيل المثال، يمكن استخدام البلوكتشين لإنشاء سجلات غير قابلة للتلاعب بالمعاملات، مما يضمن الشفافية والثقة. في سلاسل التوريد، يمكن للبلوكتشين تتبع حركة البضائع من المنشأ إلى المستهلك، مما يوفر سجلًا غير قابل للتغيير للرحلة. في الرعاية الصحية، يمكن استخدام البلوكتشين لتخزين ومشاركة السجلات الطبية بشكل آمن.

على الرغم من أن إمكانات البلوكتشين هائلة، فإن اعتماده على نطاق واسع يواجه تحديات، بما في ذلك القدرة على التوسع، والشكوك التنظيمية، والتكامل مع الأنظمة الحالية. تعمل العديد من الشركات والمؤسسات على تطوير حلول تستند إلى تقنية البلوكتشين لمعالجة مشكلات حقيقية في مختلف القطاعات. في قطاع التعليم، يمكن استخدام البلوكتشين لتخزين وإدارة الشهادات والمؤهلات الأكاديمية، مما يسهل عملية التحقق منها ويمنع التزوير. كما يمكن استخدامها لتتبع حقوق الملكية الفكرية للمواد التعليمية وضمان حصول المؤلفين على حقوقهم.

مع تطور التكنولوجيا، من المتوقع أن تصبح تطبيقات البلوكتشين أكثر انتشارًا وتأثيرًا في حياتنا اليومية. ستحتاج الشركات والمؤسسات إلى التكيف مع هذه التغييرات واستكشاف كيفية الاستفادة من هذه التقنية الثورية. في النهاية، سيكون للبلوكتشين دور محوري في تشكيل مستقبل العديد من الصناعات، بما في ذلك التعليم والتمويل والرعاية الصحية وغيرها.''',
    category: 'تكنولوجيا',
    readTime: '5 دقائق للقراءة',
    author: 'أحمد محمد',
    authorImage: 'https://randomuser.me/api/portraits/men/32.jpg',
    date: '15 مايو 2023',
    relatedArticles: [2, 7, 9],
  ),
  AlternativeMode(
    id: 2,
    imageUrl:
        'https://images.unsplash.com/photo-1544197150-b99a580bb7a8?q=80&w=2070&auto=format&fit=crop',
    title: 'نمو الحوسبة السحابية',
    description:
        '''نمو الحوسبة السحابية كان واحدًا من أهم الاتجاهات التكنولوجية في العقد الماضي. تتحول الشركات بشكل متزايد إلى العمليات السحابية للاستفادة من المرونة والقدرة على التوسع وتوفير التكاليف التي تقدمها. تتيح الحوسبة السحابية للشركات الوصول إلى موارد حوسبة قوية دون الحاجة إلى استثمارات كبيرة مقدمة في الأجهزة. هذا أدى إلى ديمقراطية الوصول إلى بنية تحتية تكنولوجية عالية المستوى، مما مكن حتى الشركات الصغيرة من التنافس مع الشركات الكبيرة.

ومع ذلك، فإن زيادة الحوسبة السحابية تأتي مع تحديات، بما في ذلك المخاوف بشأن أمن البيانات، والامتثال، والاعتماد على بائع معين. مع استمرار نمو السحابة، يجب على الشركات النظر بعناية في هذه العوامل لتحقيق أقصى استفادة منها. تشير التقديرات إلى أن سوق الحوسبة السحابية العالمي سيستمر في النمو بمعدل سنوي مركب يزيد عن 15% خلال السنوات القادمة، مما يعكس الطلب المتزايد على هذه الخدمات.

تتنوع خدمات الحوسبة السحابية بين نماذج مختلفة، مثل البنية التحتية كخدمة (IaaS)، والمنصة كخدمة (PaaS)، والبرمجيات كخدمة (SaaS). يتيح هذا التنوع للشركات اختيار النموذج الأنسب لاحتياجاتها وميزانيتها. بالإضافة إلى ذلك، تتطور تقنيات الحوسبة السحابية باستمرار، مع ظهور مفاهيم جديدة مثل الحوسبة الطرفية (Edge Computing) والحوسبة السحابية الهجينة (Hybrid Cloud)، مما يوفر مزيدًا من المرونة والكفاءة.

مع تزايد الاعتماد على الحوسبة السحابية، تصبح مهارات إدارة السحابة أكثر طلبًا في سوق العمل. تستثمر الشركات في تدريب موظفيها على هذه التقنيات أو توظيف متخصصين جدد لإدارة بنيتها التحتية السحابية. في المستقبل، من المتوقع أن تصبح الحوسبة السحابية جزءًا لا يتجزأ من استراتيجية تكنولوجيا المعلومات لمعظم الشركات، مما يدفع المزيد من الابتكار والتطوير في هذا المجال.''',
    category: 'تكنولوجيا',
    readTime: '4 دقائق للقراءة',
    author: 'سارة أحمد',
    authorImage: 'https://randomuser.me/api/portraits/women/44.jpg',
    date: '3 يونيو 2023',
    relatedArticles: [1, 5, 8],
  ),
  AlternativeMode(
    id: 3,
    imageUrl:
        "https://images.unsplash.com/photo-1563013544-824ae1b704d3?q=80&w=2070&auto=format&fit=crop",
    title: 'تحديات خصوصية البيانات',
    description:
        '''مع تزايد اعتمادنا على التكنولوجيا الرقمية، أصبحت خصوصية البيانات مصدر قلق متزايد. تجمع الشركات كميات هائلة من البيانات الشخصية، من عادات التسوق إلى المواقع الجغرافية وحتى المعلومات الصحية. في حين أن هذه البيانات يمكن أن تساعد في تحسين المنتجات والخدمات، فإنها تثير مخاوف بشأن كيفية استخدامها ومشاركتها.

استجابةً لذلك، قدمت الحكومات تشريعات مثل اللائحة العامة لحماية البيانات (GDPR) في الاتحاد الأوروبي وقانون خصوصية المستهلك في كاليفورنيا (CCPA) لحماية خصوصية المستخدمين. تواجه الشركات الآن تحديًا يتمثل في موازنة الاستفادة من البيانات مع احترام خصوصية المستخدمين والامتثال للوائح المتطورة.

مع تزايد الهجمات الإلكترونية واختراقات البيانات، أصبحت حماية المعلومات الشخصية أكثر أهمية من أي وقت مضى. تستثمر الشركات في تقنيات التشفير المتقدمة وأنظمة الأمان لحماية بيانات المستخدمين. كما يتزايد الوعي بين المستهلكين حول أهمية حماية خصوصيتهم الرقمية، مما يدفعهم إلى اتخاذ إجراءات مثل استخدام الشبكات الخاصة الافتراضية (VPN) وتعديل إعدادات الخصوصية على منصات التواصل الاجتماعي.

تظهر تقنيات جديدة مثل الخصوصية بالتصميم (Privacy by Design) والحوسبة متعددة الأطراف الآمنة (Secure Multi-party Computation) كحلول واعدة تسمح بالاستفادة من البيانات مع الحفاظ على خصوصية المستخدمين. في المستقبل، سيكون التوازن بين الابتكار وحماية الخصوصية أمرًا بالغ الأهمية لبناء اقتصاد رقمي مستدام يحظى بثقة المستخدمين.''',
    category: 'أمن المعلومات',
    readTime: '6 دقائق للقراءة',
    author: 'محمد علي',
    authorImage: 'https://randomuser.me/api/portraits/men/45.jpg',
    date: '20 يونيو 2023',
    relatedArticles: [2, 5, 8],
  ),
  AlternativeMode(
    id: 4,
    imageUrl:
        'https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2070&auto=format&fit=crop',
    title: 'الذكاء الاصطناعي في الرعاية الصحية',
    description:
        '''يحدث الذكاء الاصطناعي ثورة في مجال الرعاية الصحية من خلال تحسين التشخيص والعلاج ورعاية المرضى. تستخدم الخوارزميات المتقدمة لتحليل الصور الطبية، مثل الأشعة السينية والتصوير بالرنين المغناطيسي، للكشف عن الأمراض في مراحلها المبكرة بدقة تفوق أحيانًا الأطباء البشريين. يمكن للذكاء الاصطناعي أيضًا تحليل كميات هائلة من البيانات الصحية لتحديد الاتجاهات والأنماط التي قد تفوت على الممارسين البشريين، مما يؤدي إلى علاجات أكثر فعالية.

بالإضافة إلى ذلك، تساعد الروبوتات المدعومة بالذكاء الاصطناعي في العمليات الجراحية، مما يوفر دقة أكبر ووقت تعافي أقصر. على الرغم من هذه التطورات الواعدة، لا تزال هناك تحديات، بما في ذلك المخاوف المتعلقة بخصوصية البيانات، والحاجة إلى التنظيم، والتكامل مع أنظمة الرعاية الصحية الحالية. تعمل المؤسسات الصحية والشركات التكنولوجية على معالجة هذه المخاوف من خلال تطوير أطر أخلاقية وتنظيمية للذكاء الاصطناعي في الرعاية الصحية.

أحد المجالات الواعدة للذكاء الاصطناعي في الرعاية الصحية هو الطب الشخصي، حيث يمكن تخصيص العلاجات بناءً على الخصائص الفردية للمريض، مثل التركيب الجيني والتاريخ الطبي ونمط الحياة. يمكن للذكاء الاصطناعي تحليل هذه العوامل المتعددة لاقتراح خطط علاج أكثر فعالية وأقل آثارًا جانبية. بالإضافة إلى ذلك، يساعد الذكاء الاصطناعي في تحسين كفاءة النظام الصحي من خلال أتمتة المهام الإدارية وتحسين جدولة المواعيد وتخصيص الموارد.

مع تقدم التكنولوجيا، من المتوقع أن يزداد دور الذكاء الاصطناعي في الرعاية الصحية أهمية. تشير التقديرات إلى أن سوق الذكاء الاصطناعي في الرعاية الصحية سينمو بشكل كبير في السنوات القادمة، مدفوعًا بالحاجة إلى تحسين جودة الرعاية وتقليل التكاليف. ومع ذلك، سيظل التوازن بين الابتكار التكنولوجي والاعتبارات الأخلاقية والإنسانية أمرًا بالغ الأهمية لضمان أن يعود الذكاء الاصطناعي بالفائدة على جميع المرضى ومقدمي الرعاية الصحية.''',
    category: 'صحة',
    readTime: '7 دقائق للقراءة',
    author: 'فاطمة الزهراء',
    authorImage: 'https://randomuser.me/api/portraits/women/65.jpg',
    date: '5 يوليو 2023',
    relatedArticles: [1, 6, 9],
  ),
  AlternativeMode(
    id: 5,
    imageUrl:
        'https://images.unsplash.com/photo-1501504905252-473c47e087f8?q=80&w=2074&auto=format&fit=crop',
    title: 'مستقبل التعليم عبر الإنترنت',
    description:
        '''شهد التعليم عبر الإنترنت نموًا هائلاً في السنوات الأخيرة، وخاصة بعد جائحة كوفيد-19. أدى الانتقال الإجباري إلى التعلم عن بعد إلى تسريع اعتماد المنصات الرقمية في المؤسسات التعليمية في جميع أنحاء العالم. توفر هذه المنصات مرونة غير مسبوقة، مما يسمح للطلاب بالتعلم بوتيرتهم الخاصة ومن أي مكان. بالإضافة إلى ذلك، تمكن التكنولوجيا من تجارب تعليمية مخصصة، حيث يمكن تكييف المحتوى وفقًا لاحتياجات كل طالب.

ومع ذلك، فإن التعليم عبر الإنترنت ليس بدون تحديات. يكافح بعض الطلاب مع نقص التفاعل وجهًا لوجه، ويمكن أن تكون الفجوة الرقمية عائقًا كبيرًا للطلاب الذين يفتقرون إلى الوصول إلى التكنولوجيا أو الإنترنت عالي السرعة. مع استمرار تطور التعليم عبر الإنترنت، من المرجح أن نرى نموذجًا هجينًا يجمع بين أفضل ما في العالمين الرقمي والتقليدي. تعمل المؤسسات التعليمية على تطوير استراتيجيات تدمج بين التعلم عبر الإنترنت والتعلم في الفصول الدراسية التقليدية، مما يوفر مرونة أكبر مع الحفاظ على فوائد التفاعل الشخصي.

تلعب التقنيات الناشئة مثل الواقع الافتراضي والواقع المعزز دورًا متزايدًا في تعزيز تجارب التعلم عبر الإنترنت. يمكن لهذه التقنيات خلق بيئات تعليمية غامرة تحاكي التجارب العملية، مما يجعل التعلم أكثر تفاعلية وجاذبية. على سبيل المثال، يمكن للطلاب استكشاف المواقع التاريخية افتراضيًا، أو إجراء تجارب علمية في مختبرات افتراضية، أو ممارسة مهارات جراحية باستخدام محاكاة ثلاثية الأبعاد.

مع تزايد الطلب على التعلم مدى الحياة وإعادة تأهيل القوى العاملة، يصبح التعليم عبر الإنترنت أكثر أهمية. توفر المنصات التعليمية عبر الإنترنت فرصًا للبالغين العاملين لاكتساب مهارات جديدة أو تحسين مهاراتهم الحالية دون الحاجة إلى ترك وظائفهم. هذا يساهم في بناء قوى عاملة أكثر مرونة وقدرة على التكيف مع متطلبات سوق العمل المتغيرة باستمرار.''',
    category: 'تعليم',
    readTime: '5 دقائق للقراءة',
    author: 'عمر خالد',
    authorImage: 'https://randomuser.me/api/portraits/men/22.jpg',
    date: '18 يوليو 2023',
    relatedArticles: [2, 3, 7],
  ),
  AlternativeMode(
    id: 6,
    imageUrl:
        'https://images.unsplash.com/photo-1542601906990-b4d3fb778b09?q=80&w=2013&auto=format&fit=crop',
    title: 'الاستدامة في الأعمال',
    description:
        '''أصبحت الاستدامة أولوية متزايدة للشركات في جميع أنحاء العالم. مع تزايد الوعي بتغير المناخ والتدهور البيئي، تتخذ الشركات خطوات لتقليل بصمتها الكربونية وتعزيز الممارسات المستدامة. يشمل ذلك اعتماد مصادر الطاقة المتجددة، وتقليل النفايات، وتطوير منتجات صديقة للبيئة. بالإضافة إلى الفوائد البيئية، يمكن أن تؤدي مبادرات الاستدامة أيضًا إلى فوائد تجارية، مثل خفض التكاليف وتحسين سمعة العلامة التجارية وجذب المستهلكين المهتمين بالبيئة.

ومع ذلك، فإن التحول نحو نموذج أعمال أكثر استدامة ليس بدون تحديات. قد تواجه الشركات تكاليف أولية عالية، ومقاومة من أصحاب المصلحة، وصعوبات في قياس تأثير مبادرات الاستدامة. على الرغم من هذه التحديات، من المرجح أن تستمر الاستدامة في اكتساب أهمية مع استمرار الشركات في التكيف مع عالم يركز بشكل متزايد على المسؤولية البيئية. تشير الدراسات إلى أن الشركات التي تتبنى ممارسات الاستدامة تحقق أداءً ماليًا أفضل على المدى الطويل مقارنة بنظيراتها التي لا تفعل ذلك.

يتزايد الضغط على الشركات لتبني ممارسات الاستدامة من مصادر متعددة، بما في ذلك المستهلكين والمستثمرين والحكومات. يطالب المستهلكون بشكل متزايد بمنتجات وخدمات مستدامة، ويضع المستثمرون اعتبارات بيئية واجتماعية وحوكمة (ESG) في قراراتهم الاستثمارية، وتفرض الحكومات لوائح أكثر صرامة بشأن الانبعاثات والنفايات. استجابةً لذلك، تقوم العديد من الشركات بدمج الاستدامة في استراتيجياتها الأساسية بدلاً من اعتبارها مجرد مبادرة جانبية.

تلعب التكنولوجيا دورًا مهمًا في تمكين الشركات من تحقيق أهداف الاستدامة. تساعد التقنيات مثل إنترنت الأشياء والذكاء الاصطناعي والطاقة المتجددة الشركات على مراقبة وتحسين أدائها البيئي، وتحسين كفاءة استخدام الموارد، وتطوير حلول مبتكرة للتحديات البيئية. مع استمرار تطور هذه التقنيات، ستصبح ممارسات الأعمال المستدامة أكثر فعالية من حيث التكلفة وأسهل في التنفيذ.''',
    category: 'أعمال',
    readTime: '6 دقائق للقراءة',
    author: 'ليلى إبراهيم',
    authorImage: 'https://randomuser.me/api/portraits/women/28.jpg',
    date: '2 أغسطس 2023',
    relatedArticles: [4, 8, 10],
  ),
  AlternativeMode(
    id: 7,
    imageUrl:
        'https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?q=80&w=1974&auto=format&fit=crop',
    title: 'تأثير وسائل التواصل الاجتماعي على المجتمع',
    description:
        '''غيرت وسائل التواصل الاجتماعي بشكل جذري الطريقة التي نتواصل بها ونتفاعل مع بعضنا البعض. بينما سهلت هذه المنصات التواصل العالمي وتبادل الأفكار، فقد أثارت أيضًا مخاوف بشأن تأثيرها على الصحة العقلية والخصوصية والتماسك الاجتماعي. يرتبط الاستخدام المفرط لوسائل التواصل الاجتماعي بزيادة مشاعر العزلة والقلق والاكتئاب، خاصة بين الشباب.

بالإضافة إلى ذلك، أدى انتشار المعلومات المضللة والأخبار الكاذبة على هذه المنصات إلى تفاقم الانقسامات السياسية وتقويض الثقة في المؤسسات. ومع ذلك، يمكن لوسائل التواصل الاجتماعي أيضًا أن تكون قوة إيجابية، حيث تمكن الحركات الاجتماعية وتوفر منصة للأصوات المهمشة وتسهل التضامن خلال الأزمات.

تشير الدراسات الحديثة إلى أن تأثير وسائل التواصل الاجتماعي يختلف باختلاف كيفية استخدامها. فالاستخدام النشط، مثل التفاعل مع الآخرين ومشاركة المحتوى الهادف، يرتبط بنتائج إيجابية أكثر من الاستخدام السلبي الذي يقتصر على التصفح والمشاهدة. كما أن الوقت المستغرق على هذه المنصات يلعب دورًا مهمًا، حيث يرتبط الاستخدام المعتدل بتأثيرات أقل سلبية.

تتخذ المنصات الاجتماعية خطوات لمعالجة بعض هذه المخاوف، مثل تطوير أدوات للحد من الاستخدام المفرط ومكافحة المعلومات المضللة. كما تزداد الدعوات لتنظيم أفضل لهذه المنصات لضمان حماية المستخدمين، خاصة الفئات الضعيفة مثل الأطفال والمراهقين. مع استمرار تطور وسائل التواصل الاجتماعي، من الضروري إيجاد توازن بين الاستفادة من فوائدها مع التخفيف من آثارها السلبية.''',
    category: 'مجتمع',
    readTime: '8 دقائق للقراءة',
    author: 'يوسف أحمد',
    authorImage: 'https://randomuser.me/api/portraits/men/36.jpg',
    date: '15 أغسطس 2023',
    relatedArticles: [3, 5, 9],
  ),
  AlternativeMode(
    id: 8,
    imageUrl:
        'https://images.unsplash.com/photo-1532996122724-e3c354a0b15b?q=80&w=2070&auto=format&fit=crop',
    title: 'الاقتصاد الدائري ومستقبل الاستهلاك',
    description:
        '''يقدم الاقتصاد الدائري بديلاً للنموذج الاقتصادي الخطي التقليدي "أخذ-صنع-تخلص". بدلاً من ذلك، يركز على إعادة استخدام الموارد وتقليل النفايات من خلال إعادة التدوير وإعادة التصنيع وإعادة التصميم. يهدف هذا النهج إلى فصل النمو الاقتصادي عن استهلاك الموارد المحدودة، مما يخلق نظامًا أكثر استدامة.

تتبنى الشركات بشكل متزايد مبادئ الاقتصاد الدائري، من خلال تطوير منتجات قابلة لإعادة التدوير بالكامل، وإنشاء أنظمة استرداد للمنتجات المستعملة، والانتقال إلى نماذج الأعمال القائمة على الخدمات. يمكن أن يؤدي هذا التحول إلى فوائد بيئية واقتصادية كبيرة، بما في ذلك انخفاض انبعاثات غازات الاحتباس الحراري، وتقليل استخراج الموارد، وخلق فرص عمل جديدة.

تشير التقديرات إلى أن تبني مبادئ الاقتصاد الدائري يمكن أن يوفر للاقتصاد العالمي تريليونات الدولارات بحلول عام 2030، من خلال تقليل تكاليف المواد الخام وإدارة النفايات. في أوروبا، تقود المفوضية الأوروبية الجهود من خلال خطة عمل الاقتصاد الدائري، التي تهدف إلى جعل المنتجات المستدامة هي القاعدة وتمكين المستهلكين من المشاركة في التحول الأخضر.

ومع ذلك، يتطلب التحول إلى اقتصاد دائري تغييرات كبيرة في سلوك المستهلك، وسياسات الحكومة، وممارسات الأعمال. يحتاج المستهلكون إلى تبني عقلية "أقل هو أكثر" والتركيز على جودة المنتجات وقابليتها للإصلاح بدلاً من الشراء المتكرر. كما تحتاج الحكومات إلى وضع سياسات تشجع على الاستدامة، مثل الضرائب على النفايات وحوافز لإعادة التدوير. وأخيرًا، يجب على الشركات إعادة التفكير في نماذج أعمالها لتشمل الاستدامة في صميم استراتيجياتها.''',
    category: 'اقتصاد',
    readTime: '7 دقائق للقراءة',
    author: 'نور الهدى',
    authorImage: 'https://randomuser.me/api/portraits/women/56.jpg',
    date: '28 أغسطس 2023',
    relatedArticles: [6, 10, 2],
  ),
  AlternativeMode(
    id: 9,
    imageUrl:
        'https://images.unsplash.com/photo-1633356122102-3fe601e05bd2?q=80&w=2070&auto=format&fit=crop',
    title: 'الواقع المعزز في التسوق',
    description:
        '''يغير الواقع المعزز (AR) تجربة التسوق من خلال دمج العناصر الافتراضية في العالم الحقيقي. تتيح هذه التكنولوجيا للمتسوقين "تجربة" المنتجات قبل الشراء، سواء كان ذلك من خلال رؤية كيف ستبدو قطعة أثاث في منزلهم أو كيف ستبدو ملابس معينة عليهم.

تتبنى العلامات التجارية الكبرى بشكل متزايد تطبيقات الواقع المعزز لتعزيز تجربة العملاء وتقليل المرتجعات. على سبيل المثال، تسمح بعض متاجر مستحضرات التجميل للعملاء بتجربة منتجات المكياج افتراضيًا، بينما تقدم متاجر الأثاث تطبيقات تسمح للمستخدمين بوضع العناصر افتراضيًا في مساحاتهم.

تشير الإحصاءات إلى أن استخدام الواقع المعزز في التسوق يمكن أن يزيد من معدلات التحويل بنسبة تصل إلى 40% ويقلل من معدلات الإرجاع بنسبة تصل إلى 35%. هذا يترجم إلى وفورات كبيرة للشركات وتجربة أكثر رضا للمستهلكين. تستثمر شركات مثل أمازون وإيكيا وسيفورا بكثافة في تقنيات الواقع المعزز لتحسين تجربة التسوق عبر الإنترنت.

بالإضافة إلى تحسين تجربة التسوق، يمكن أن يساعد الواقع المعزز أيضًا تجار التجزئة في جمع بيانات قيمة حول تفضيلات المستهلك وسلوكهم. يمكن استخدام هذه البيانات لتخصيص العروض وتحسين تصميم المنتج وتحسين استراتيجيات التسويق. مع استمرار تطور التكنولوجيا وانخفاض تكاليفها، من المرجح أن يصبح الواقع المعزز جزءًا لا يتجزأ من مستقبل التجارة الإلكترونية والبيع بالتجزئة، مما يوفر تجارب تسوق أكثر تفاعلية وشخصية.''',
    category: 'تكنولوجيا',
    readTime: '5 دقائق للقراءة',
    author: 'كريم حسن',
    authorImage: 'https://randomuser.me/api/portraits/men/18.jpg',
    date: '10 سبتمبر 2023',
    relatedArticles: [1, 4, 7],
  ),
  AlternativeMode(
    id: 10,
    imageUrl:
        'https://images.unsplash.com/photo-1584553391861-5145f5555d27?q=80&w=2070&auto=format&fit=crop',
    title: 'مستقبل العمل عن بعد',
    description:
        '''أحدثت جائحة كوفيد-19 تحولًا كبيرًا في كيفية عمل الناس، مع انتقال العديد من الشركات إلى نماذج العمل عن بعد. حتى مع تخفيف القيود، اختارت العديد من المؤسسات الاحتفاظ بترتيبات العمل المرنة، مما يشير إلى تحول دائم في ثقافة العمل.

يوفر العمل عن بعد العديد من الفوائد، بما في ذلك زيادة الإنتاجية، وتحسين التوازن بين العمل والحياة، وانخفاض تكاليف المكاتب. كما أنه يوسع مجموعة المواهب المتاحة للشركات، حيث يمكنها الآن توظيف أشخاص من أي مكان في العالم. تشير الدراسات إلى أن 74% من الشركات تخطط للانتقال الدائم إلى العمل عن بعد لبعض موظفيها بعد الجائحة.

ومع ذلك، يأتي العمل عن بعد أيضًا مع تحديات، مثل صعوبة التعاون، والشعور بالعزلة، وطمس الحدود بين العمل والحياة الشخصية. يبلغ 45% من العاملين عن بعد عن مستويات أعلى من الإرهاق مقارنة بعملهم في المكتب، ويكافح العديد منهم للحفاظ على التواصل الفعال مع زملائهم.

للتغلب على هذه التحديات، تستثمر الشركات في أدوات التعاون الرقمية وتطور سياسات جديدة لدعم القوى العاملة الموزعة. تشمل هذه الاستراتيجيات تنظيم اجتماعات افتراضية منتظمة، وإنشاء قنوات اتصال واضحة، وتوفير تدريب على المهارات الرقمية، وتشجيع فترات الراحة المنتظمة لمنع الإرهاق.

مع تطور مستقبل العمل، من المرجح أن نرى نهجًا هجينًا يجمع بين العمل عن بعد والعمل في المكتب، مما يوفر المرونة مع الحفاظ على الفوائد التي يوفرها التفاعل الشخصي. تتوقع 83% من الشركات أن يكون نموذج العمل الهجين هو المعيار المستقبلي، مما يسمح للموظفين بالعمل من المنزل لبعض الوقت والحضور إلى المكتب للتعاون والاجتماعات المهمة.''',
    category: 'أعمال',
    readTime: '6 دقائق للقراءة',
    author: 'سلمى محمود',
    authorImage: 'https://randomuser.me/api/portraits/women/33.jpg',
    date: '22 سبتمبر 2023',
    relatedArticles: [6, 8, 2],
  ),
];
