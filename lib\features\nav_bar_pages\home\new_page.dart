import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/widgets/cached_image_widget.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/story/more_details_text_button.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/story/new_link.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/story/story_page.dart';

class NewPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final homeController = Get.find<HomePageController>();
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Scaffold(
        body: StoryPageView(
          initialPage: homeController.currentIndex,
          indicatorPadding: EdgeInsets.only(top: 40),
          indicatorsCount: (length) =>
              homeController.news[homeController.currentIndex].mediaList.length,
          itemBuilder:
              (context, pageIndex, storyIndex, pageAnimationController) =>
                  CachedImageWidget(
                      imageUrl:
                          homeController.news[pageIndex].mediaList[storyIndex]),
          pageLength: homeController.news.length,
          storyLength: (pageIndex) =>
              homeController.news[pageIndex].mediaList.length,
          gestureItemBuilder:
              (context, pageIndex, storyIndex, pageAnimationController) {
            return Align(
              alignment: Alignment.bottomCenter,
              child: Stack(
                children: [
                  MoreDetailsTextButton(),
                  NewLinkWidget(),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
