

class YearModel{
  int id;
  String title;
  int? majorId;

  YearModel({required this.id,required this.title,this.majorId});

  factory YearModel.fromJson(Map<String, dynamic> json)=> YearModel(
      id: json['id'],
      title: json['title']
  );


  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    return data;
  }
}