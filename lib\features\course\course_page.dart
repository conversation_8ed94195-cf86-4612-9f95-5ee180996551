import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/cached_image_widget.dart';
import 'package:all_in_one/core/widgets/no_data_widget.dart';
import 'package:all_in_one/features/course/shimmer/course_shimmer.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';
import 'contollers/course_controller.dart';
import 'widgets/course_exams_section.dart';
import 'widgets/course_files_section.dart';
import 'widgets/course_lessons_section.dart';
import 'widgets/course_name_secttion.dart';
import 'widgets/course_notes_section.dart';
import 'widgets/course_options_section.dart';

class CoursePage extends StatelessWidget {
  const CoursePage();

  @override
  Widget build(BuildContext context) {
    CourseController controller = Get.put(CourseController());
    return Scaffold(
      body: Obx(() => controller.detailLoading
          ? CourseShimmer(
              title: controller.subjectName,
            )
          : SafeArea(
              child: NestedScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                headerSliverBuilder:
                    (BuildContext context, bool innerBoxIsScrolled) {
                  return <Widget>[
                    SliverOverlapAbsorber(
                      handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
                          context),
                      sliver: SliverAppBar(
                        backgroundColor: AppStyle.primaryColor,
                        leading: InkWell(
                          onTap: () => Get.back(),
                          child: Padding(
                            padding: EdgeInsets.all(20),
                            child: Icon(
                              Icons.arrow_back,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        centerTitle: true,
                        expandedHeight: 400,
                        pinned: true,
                        snap: true,
                        floating: true,
                        title: Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: Text(
                            controller.subjectName,
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge!
                                .copyWith(
                                    color: AppStyle.whiteColor,
                                    fontWeight: AppFontWeight.bold),
                          ),
                        ),
                        flexibleSpace: FlexibleSpaceBar(
                          background: Container(
                            color: Colors.white,
                            margin: EdgeInsets.only(top: 65),
                            child: Column(
                              children: [
                                CachedImageWidget(
                                  imageUrl: controller.subjectModel.banner,
                                  width: Get.width,
                                  height: 220,
                                  borderRadius: BorderRadius.circular(0),
                                  fit: BoxFit.cover,
                                ),
                                const SizedBox(
                                  height: 8,
                                ),
                                CourseNameSection(),
                              ],
                            ),
                          ),
                        ),
                        automaticallyImplyLeading: false,
                      ),
                    ),
                  ];
                },
                body: Padding(
                  padding: const EdgeInsets.only(top: 50),
                  child: Column(
                    children: [
                      CourseOptionsSection(),
                      const SizedBox(
                        height: 15,
                      ),
                      Expanded(
                        child: PageView(
                          controller: controller.pageController,
                          onPageChanged: (num) => controller.onPageChange(num),
                          children: [
                            CourseLessonsSection(
                              fromHome: controller.fromHome,
                            ),
                            controller.subjectModel.lectures.isNotEmpty
                                ? Obx(
                                    () => CourseNotesSection(
                                      notes: controller
                                          .subjectModel
                                          .lectures[controller.selectedSection]
                                          .notes,
                                    ),
                                  )
                                : NoDataWidget(
                                    imageWidth: 100,
                                    imageHeight: 100,
                                  ),
                            CourseFilesSection(),
                            CourseExamsSection(),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
            )),
    );
  }
}
