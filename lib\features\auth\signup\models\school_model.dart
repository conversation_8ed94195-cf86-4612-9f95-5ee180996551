
class SchoolModel{
  int id;
  String title;

  SchoolModel({required this.id,required this.title});

  factory SchoolModel.fromJson(Map<String, dynamic> json)=> SchoolModel(
      id: json['id'],
      title: json['title']
  );


  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['title'] = this.title;
    return data;
  }
}