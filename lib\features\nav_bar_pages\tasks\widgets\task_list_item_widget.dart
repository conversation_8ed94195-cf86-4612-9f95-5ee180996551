import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/models/task_model.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/widgets/task_options.dart';

class TaskListItemWidget extends StatelessWidget {
  final TaskModel task;
  final Function(int) onDelete;
  final Function(TaskModel) onEdit;
  const TaskListItemWidget(
      {required this.task, required this.onDelete, required this.onEdit});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
      decoration: BoxDecoration(
        color: task.color,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Expanded(
                flex: 7,
                child: Text(
                  task.title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: AppStyle.lightBlackColor,
                      fontWeight: AppFontWeight.bold),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 3,
                ),
              ),
              //  refactor that and use decorator pattern
              Expanded(
                child: TaskOptions(
                  onDelete: onDelete,
                  onEdit: onEdit,
                  task: task,
                ),
              ),
            ],
          ),
          const SizedBox(
            height: 12,
          ),
          Wrap(
            children: [
              Text(
                task.details,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppStyle.blackColor, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(
            height: 12,
          ),
          Text(
            "Task date: ".tr + task.taskDate,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppStyle.boldGreyTextColor,
                ),
          ),
        ],
      ),
    );
  }
}

class TaskTileButton extends StatelessWidget {
  final Function()? onTap;
  final Color iconColor;
  final String icon;
  final String labael;
  const TaskTileButton(
      {super.key,
      this.onTap,
      required this.iconColor,
      required this.icon,
      required this.labael});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        IconButton(
            onPressed: onTap,
            // style: ButtonStyle(
            //     backgroundColor: WidgetStatePropertyAll(AppStyle.whiteColor)),
            icon: SvgPicture.asset(
              icon,
              width: 24,
              height: 24,
              colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
            )),
        Text(labael),
      ],
    );
  }
}
