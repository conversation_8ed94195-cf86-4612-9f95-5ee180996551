import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:background_downloader/background_downloader.dart';
import 'package:ffmpeg_kit_flutter_new/ffmpeg_kit.dart';
import 'package:ffmpeg_kit_flutter_new/return_code.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:path/path.dart' as path;
import 'package:all_in_one/core/controllers/app_controller.dart';

class DownloadAndMergeService {
  final AppController _appController = Get.find();
  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  Future<void> downloadAndMergeVideo({
    required String videoUrl,
    required String audioUrl,
    required String videoName,
    required int lessonId,
    required Function(double) onProgress,
    required Function() onComplete,
    required Function() onError,
  }) async {
    try {
      if(videoUrl.isEmpty || audioUrl.isEmpty){
        Get.rawSnackbar(
          message: "720p is not available for this video", 
        );
        onError();
        return;
      }
      log('Starting download and merge process for lesson $lessonId', name: 'DownloadAndMergeService');
      log('Video URL: $videoUrl', name: 'DownloadAndMergeService');
      log('Audio URL: $audioUrl', name: 'DownloadAndMergeService');

      // Show processing notification
      await _showLocalNotification(
        id: lessonId,
        title: 'Processing video',
        body: 'Please wait while we finalize your video.',
        channelId: 'video_processing',
      );

      await _cleanupTemporaryFiles(lessonId);

      final videoFileName = "${videoName}_video.mp4";
      final audioFileName = "${videoName}_audio.mp4";
      final mergedFileName = "$videoName.mp4";

      await Directory(_appController.videosDirectoryPath).create(recursive: true);

      final videoPath = path.join(_appController.videosDirectoryPath, videoFileName);
      final audioPath = path.join(_appController.videosDirectoryPath, audioFileName);
      final outputPath = path.join(_appController.videosDirectoryPath, mergedFileName);

      final videoTask = DownloadTask(
        url: videoUrl,
        filename: videoFileName,
        directory: _appController.videosDirectoryPath,
        updates: Updates.statusAndProgress,
        taskId: "${lessonId}_video",
        allowPause: false,
        requiresWiFi: false,
        retries: 5,
      );

      final audioTask = DownloadTask(
        url: audioUrl,
        filename: audioFileName,
        directory: _appController.videosDirectoryPath,
        updates: Updates.statusAndProgress,
        taskId: "${lessonId}_audio",
        allowPause: false,
        requiresWiFi: false,
        retries: 5,
      );

      double videoProgress = 0;
      double audioProgress = 0;

      final videoDownload = _downloadFile(
        task: videoTask,
        onProgress: (progress) {
          videoProgress = progress;
          onProgress((videoProgress + audioProgress) / 2);
        },
        showNotification: true,
      );

      final audioDownload = _downloadFile(
        task: audioTask,
        onProgress: (progress) {
          audioProgress = progress;
          onProgress((videoProgress + audioProgress) / 2);
        },
        showNotification: false,
      );

      final results = await Future.wait([videoDownload, audioDownload]);

      if (results.any((result) => result == false)) {
        log('Download failed. Video success: ${results[0]}, Audio success: ${results[1]}', name: 'DownloadAndMergeService');
        await _cleanupTemporaryFiles(lessonId);
        await _showLocalNotification(
          id: lessonId,
          title: 'Video processing failed',
          body: 'There was an issue during processing.',
          channelId: 'video_processing',
        );
        onError();
        return;
      }

      final videoFile = File(videoPath);
      final audioFile = File(audioPath);

      if (!await videoFile.exists() || !await audioFile.exists()) {
        log('Error: Required files do not exist. Video exists: ${await videoFile.exists()}, Audio exists: ${await audioFile.exists()}', name: 'DownloadAndMergeService');
        await _cleanupTemporaryFiles(lessonId);
        await _showLocalNotification(
          id: lessonId,
          title: 'Video processing failed',
          body: 'Required files are missing.',
          channelId: 'video_processing',
        );
        onError();
        return;
      }

      final outputFile = File(outputPath);
      if (await outputFile.exists()) {
        await outputFile.delete();
      }

      log('Starting FFmpeg merge process', name: 'DownloadAndMergeService');

      // Optimized FFmpeg command to preserve quality
      final command =
          "-i \"$videoPath\" -i \"$audioPath\" -map 0:v:0 -map 1:a:0 -c:v copy -c:a copy -map_metadata 0 -movflags +faststart \"$outputPath\"";
      log('FFmpeg command: $command', name: 'DownloadAndMergeService');

      FFmpegKit.executeAsync(command, (session) async {
        final returnCode = await session.getReturnCode();
        final logs = await session.getLogsAsString();

        await _cleanupTemporaryFiles(lessonId);

        if (ReturnCode.isSuccess(returnCode)) {
          log('FFmpeg merge successful. Output file: $outputPath', name: 'DownloadAndMergeService');
          if (await outputFile.exists() && await outputFile.length() > 0) {
            log('Output file verified: ${outputFile.path} (${await outputFile.length()} bytes)', name: 'DownloadAndMergeService');
            await _showLocalNotification(
              id: lessonId,
              title: 'Video ready!',
              body: 'Video is available in My Videos.',
              channelId: 'video_processing',
            );
            onComplete();
          } else {
            log('Error: Output file was not created or is empty', name: 'DownloadAndMergeService');
            await _showLocalNotification(
              id: lessonId,
              title: 'Video processing failed',
              body: 'Output file is missing or empty.',
              channelId: 'video_processing',
            );
            onError();
          }
        } else {
          log('FFmpeg merge failed with return code: ${returnCode?.getValue()}', name: 'DownloadAndMergeService');
          log('FFmpeg logs: $logs', name: 'DownloadAndMergeService');
          await _showLocalNotification(
            id: lessonId,
            title: 'Video processing failed',
            body: 'There was an issue during processing.',
            channelId: 'video_processing',
          );
          onError();
        }
      });
    } catch (e, stackTrace) {
      log('Error in downloadAndMergeVideo: $e', name: 'DownloadAndMergeService', error: e, stackTrace: stackTrace);
      await _cleanupTemporaryFiles(lessonId);
      await _showLocalNotification(
        id: lessonId,
        title: 'Video processing failed',
        body: 'An unexpected error occurred.',
        channelId: 'video_processing',
      );
      onError();
    }
  }

  Future<bool> _downloadFile({
    required DownloadTask task,
    required Function(double) onProgress,
    bool showNotification = true,
  }) async {
    try {
      final completer = Completer<bool>();

      if (showNotification) {
        await FileDownloader().configureNotificationForTask(
          task,
          running: TaskNotification('Downloading ${task.filename}', 'File will be saved in My Videos'),
          error: TaskNotification('Download failed', 'For file ${task.filename}'),
          complete: TaskNotification('Download ${task.filename} Completed', 'You can see your downloaded video in My Videos section'),
          progressBar: true,
        );
      }

      final downloadTask = DownloadTask(
        url: task.url,
        filename: task.filename,
        baseDirectory: BaseDirectory.root,
        directory: task.directory,
        updates: Updates.statusAndProgress,
        requiresWiFi: false,
        retries: 5,
        allowPause: false,
        taskId: task.taskId,
      );

      await FileDownloader().download(
        downloadTask,
        onProgress: (progress) {
          onProgress(progress);
        },
        onStatus: (status) async {
          log('Download ${downloadTask.taskId} status: $status', name: 'DownloadAndMergeService');

          if (status == TaskStatus.complete) {
            log('Download completed for ${downloadTask.filename}', name: 'DownloadAndMergeService');

            final file = File(path.join(downloadTask.directory, downloadTask.filename));
            final exists = await file.exists();
            final size = exists ? await file.length() : 0;
            log('File verification - exists: $exists, size: $size bytes', name: 'DownloadAndMergeService');

            if (!exists || size == 0) {
              log('File verification failed for ${downloadTask.filename}', name: 'DownloadAndMergeService');
              if (!completer.isCompleted) {
                completer.complete(false);
              }
              return;
            }

            if (!completer.isCompleted) {
              completer.complete(true);
            }
          } else if (status == TaskStatus.canceled ||
              status == TaskStatus.notFound ||
              status == TaskStatus.failed) {
            log('Download failed for ${downloadTask.taskId}: $status', name: 'DownloadAndMergeService');
            if (!completer.isCompleted) {
              completer.complete(false);
            }
          }
        },
      );

      return await completer.future.timeout(
        const Duration(minutes: 120),
        onTimeout: () {
          log('Download timed out for ${task.filename}', name: 'DownloadAndMergeService');
          return false;
        },
      );
    } catch (e, stackTrace) {
      log('Error in _downloadFile: $e', name: 'DownloadAndMergeService', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  Future<void> _cleanupTemporaryFiles(int lessonId) async {
    try {
      final videoFile = File(path.join(_appController.videosDirectoryPath, '${lessonId}_video.mp4'));
      final audioFile = File(path.join(_appController.videosDirectoryPath, '${lessonId}_audio.mp4'));

      await FileDownloader().cancelTaskWithId('${lessonId}_video');
      await FileDownloader().cancelTaskWithId('${lessonId}_audio');

      if (await videoFile.exists()) {
        try {
          await videoFile.delete();
          log('Deleted video file: ${videoFile.path}', name: 'DownloadAndMergeService');
        } catch (e) {
          log('Error deleting video file: $e', name: 'DownloadAndMergeService');
        }
      }

      if (await audioFile.exists()) {
        try {
          await audioFile.delete();
          log('Deleted audio file: ${audioFile.path}', name: 'DownloadAndMergeService');
        } catch (e) {
          log('Error deleting audio file: $e', name: 'DownloadAndMergeService');
        }
      }

      try {
        await FileDownloader().database.deleteRecordWithId('${lessonId}_video');
        await FileDownloader().database.deleteRecordWithId('${lessonId}_audio');
        log('Cleaned up download records for lesson $lessonId', name: 'DownloadAndMergeService');
      } catch (e) {
        log('Error cleaning up download records: $e', name: 'DownloadAndMergeService');
      }

      log('Cleaned up temporary files for lesson $lessonId', name: 'DownloadAndMergeService');
    } catch (e, stackTrace) {
      log('Error cleaning up temporary files: $e', name: 'DownloadAndMergeService', error: e, stackTrace: stackTrace);
    }
  }

  Future<void> _showLocalNotification({
    required int id,
    required String title,
    required String body,
    required String channelId,
  }) async {
    try {
      const AndroidNotificationDetails androidPlatformChannelSpecifics = AndroidNotificationDetails(
        'video_processing',
        'Video Processing',
        channelDescription: 'Notifications for video downloading and processing',
        importance: Importance.max,
        priority: Priority.high,
        showProgress: false,
      );
      const NotificationDetails platformChannelSpecifics = NotificationDetails(
        android: androidPlatformChannelSpecifics,
        iOS: DarwinNotificationDetails(),
      );

      await _flutterLocalNotificationsPlugin.show(
        id,
        title,
        body,
        platformChannelSpecifics,
      );
    } catch (e, stackTrace) {
      log('Error showing local notification: $e', name: 'DownloadAndMergeService', error: e, stackTrace: stackTrace);
    }
  }
}