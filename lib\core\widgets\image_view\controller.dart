import 'package:flutter/services.dart';

import 'package:get/get.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import '../../controllers/app_controller.dart';



class ImageViewPageController extends GetxController {
  DataController dataController = Get.find();
  AppController appController = Get.find();

  late final String path;
  ImageViewPageController(this.path);

  Rx<bool> _whiteBackground = true.obs;
  bool get whiteBackground => this._whiteBackground.value;
  set whiteBackground(bool value) => this._whiteBackground.value = value;

  Rx<bool> _loading = false.obs;
  bool get loading => this._loading.value;
  set loading(bool value) => this._loading.value = value;

  // saveImage(context) async {
  //   loading = true;
  //   bool? res = await GallerySaver.saveImage(path);
  //   loading = false;
  //   if (res == null || !res) {
  //     appController.showToast(context,
  //         message: 'The image has not been downloaded');
  //   } else {
  //     appController.showToast(context,
  //         status: ToastStatus.success,
  //         message: 'The image has been downloaded successfully');
  //   }
  // }

  @override
  void onInit() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    super.onInit();
  }

  @override
  void onClose() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: SystemUiOverlay.values); // to re-show bars
    super.onClose();
  }
}
