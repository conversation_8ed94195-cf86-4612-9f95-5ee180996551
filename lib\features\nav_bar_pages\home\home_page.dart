import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/features/alternative_ui/index.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_page_body.dart';

import 'controllers/home_controller.dart';
import 'shimmer/home_shimmer.dart';
import 'widgets/home_app_bar_widget.dart';

class HomePage extends StatelessWidget {
  HomePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    HomePageController controller = Get.put(HomePageController());
    controller.checkAllDownloads();

    AppController appController = Get.find();
    // Always show AlternativeUi when isInReview is true
    if (appController.isInReview) {
      return AlternativeUi();
    } else {
      return RefreshIndicator(
        onRefresh: () async => controller.initialHome(),
        child: Scaffold(
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              HomeAppBarWidget(),
              Expanded(
                child: Obx(() =>
                    controller.dataLoading ? HomeShimmer() : HomePageBody()),
              )
            ],
          ),
        ),
      );
    }
  }
}
