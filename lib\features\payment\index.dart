// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:get/get.dart';
// import 'package:all_in_one/core/style/assets.gen.dart';
// import 'package:all_in_one/core/style/style.dart';
// import 'package:all_in_one/core/widgets/button.dart';
// import 'package:all_in_one/features/payment/controller.dart';

// class PaymentMethodsDialog extends StatelessWidget {
//   final String type;
//   final int? id;
//   final bool fromCourse;
//   final int? index;
//   const PaymentMethodsDialog(
//       {super.key,
//       required this.type,
//       this.id,
//       this.fromCourse = false,
//       this.index});
//   @override
//   Widget build(BuildContext context) {
//     PaymentMethodsController controller =
//         Get.put(PaymentMethodsController(type: type, id: id));
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 8),
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Align(
//               alignment: AlignmentDirectional.centerStart,
//               child: IconButton(
//                   onPressed: Get.back, icon: Assets.icons.cancelDialog.svg())),
//           Text("please chose your payment method".tr,
//               textAlign: TextAlign.center, style: Get.textTheme.titleLarge),
//           const SizedBox(height: 32),
//           PaymentButton(
//               text: "mtn cash",
//               icon: Assets.icons.mtn.path,
//               onTap: controller.onMtnCallBack),
//           const SizedBox(height: 16),
//           PaymentButton(
//               text: "fatora",
//               icon: Assets.icons.fatora.path,
//               onTap: controller.onFatoraCallBack),
//           const SizedBox(height: 16),
//           PaymentButton(
//               text: "code actaivation",
//               icon: Assets.icons.fatora.path,
//               onTap: controller.onCodeCallBack),
//           const SizedBox(height: 32),
//         ],
//       ),
//     );
//   }
// }

// class PaymentButton extends StatelessWidget {
//   final String text;
//   final String icon;
//   final Function() onTap;
//   const PaymentButton(
//       {super.key, required this.text, required this.icon, required this.onTap});

//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//         onTap: onTap,
//         child: Container(
//           padding: const EdgeInsets.symmetric(vertical: 16),
//           decoration: BoxDecoration(
//               color: AppStyle.primaryColor,
//               borderRadius: BorderRadius.circular(12)),
//           child: Center(
//             child: Row(
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 SvgPicture.asset(
//                   icon,
//                   width: 16,
//                   height: 16,
//                   colorFilter:
//                       ColorFilter.mode(AppStyle.whiteColor, BlendMode.srcIn),
//                 ),
//                 const SizedBox(width: 16),
//                 Text(text.tr,
//                     style: Get.textTheme.bodyMedium
//                         ?.copyWith(color: AppStyle.whiteColor))
//               ],
//             ),
//           ),
//         ));
//   }
// }
