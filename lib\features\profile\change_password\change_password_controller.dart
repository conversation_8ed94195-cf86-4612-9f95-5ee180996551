import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';

class ChangePasswordController extends GetxController {
  AppController appController = Get.find();
  DataController dataController = Get.find();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  TextEditingController password = TextEditingController();
  TextEditingController passwordConfirmation = TextEditingController();
  changePassword(BuildContext context) async {
    if (formKey.currentState?.validate() == true) {
      ResponseModel response = await dataController.postData(
          url: API.passwordResets,
          body: {
            "password": password.text,
            "password_confirmation": passwordConfirmation.text
          });
      if (response.success) {
        Get.back();
        appController.showToast(context,
            status: ToastStatus.success,
            message: "password has been updated".tr);
        Get.back();
      } else {
        appController.showToast(context, message: response.errorsAsString);
      }
    } else {
      Get.back();
    }
  }

  @override
  void onClose() {
    passwordConfirmation.dispose();
    password.dispose();
    super.onClose();
  }
}
