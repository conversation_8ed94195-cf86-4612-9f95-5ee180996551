import 'dart:io';
import 'package:all_in_one/core/repository/fake_repo.dart';
import 'package:dio/dio.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:all_in_one/core/constants/api.dart';
import 'package:all_in_one/core/constants/role.dart';
import 'package:all_in_one/core/controllers/data_controller.dart';
import 'package:all_in_one/core/controllers/global_controller.dart';
import 'package:all_in_one/core/controllers/notification_controller.dart';
import 'package:all_in_one/core/models/app/user.dart';
import 'package:all_in_one/core/models/general/response_model.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:image_picker/image_picker.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/features/auth/signup/models/city_model.dart';
import 'package:all_in_one/features/auth/signup/models/class_model.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_year_model.dart';
import 'package:all_in_one/features/profile/edit_profile/widgets/permission_dialog.dart';
import '../../../core/controllers/app_controller.dart';
import 'models/school_model.dart';

class SignUpPageController extends GetxController {
  AppController appController = Get.find();
  DataController dataController = Get.find();
  GlobalController globalController = Get.find();
  PageController pageController = PageController();
  GlobalKey<FormState> formKey = GlobalKey<FormState>();
  GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  Rx<String> _phoneError = Rx<String>("");
  String get phoneError => _phoneError.value;

  TextEditingController name = TextEditingController();
  TextEditingController password = TextEditingController();
  TextEditingController passwordConfirmation = TextEditingController();

  TextEditingController phone = TextEditingController();
  TextEditingController schoolController = TextEditingController();
  TextEditingController majorController = TextEditingController();

  TextEditingController birthDate = TextEditingController();
  NotificationController notificationController = Get.find();

  DateTime? selectedDate;

  List<CityModel> cities = [];
  int? cityId;
  RxBool _citiesLoading = false.obs;
  get citiesLoading => this._citiesLoading.value;
  set citiesLoading(value) => this._citiesLoading.value = value;

  List<YearModel> years = [];
  int? yearsId;
  RxBool _yearsLoading = false.obs;
  get yearsLoading => this._yearsLoading.value;
  set yearsLoading(value) => this._yearsLoading.value = value;

  List<SchoolModel> schools = [];
  List<Major> majors = [];
  RxBool _majorsLoading = false.obs;
  get majorsLoading => this._majorsLoading.value;
  set majorsLoading(value) => this._majorsLoading.value = value;
  int? schoolId;
  int? majorId;

  RxBool _schoolsLoading = false.obs;
  get schoolsLoading => this._schoolsLoading.value;
  set schoolsLoading(value) => this._schoolsLoading.value = value;
  RxBool isPrivacyAccepted = false.obs;
  //########### Register ###########

  void loadCitiesData() async {
    cities = [];
    citiesLoading = true;
    ResponseModel response;
    if (!appController.isInReview) {
      response = await dataController.getData(
        url: API.citiesApi,
      );
      if (response.success) {
        response.data
            .forEach((element) => cities.add(CityModel.fromJson(element)));
        citiesLoading = false;
      }
    } else {
      cities = FakeRepo.cities;
      citiesLoading = false;
    }
  }

  void loadYearsData() async {
    years = [];
    yearsLoading = true;
    ResponseModel response;
    if (!appController.isInReview) {
      response = await dataController
          .getData(url: API.yearsApi, param: {"major_id": majorId});
      if (response.success) {
        response.data
            .forEach((element) => years.add(YearModel.fromJson(element)));
        yearsLoading = false;
      }
    } else {
      years = List.from(FakeRepo.years);
      yearsLoading = false;
    }
  }

  void loadSchoolsData() async {
    schools = [];
    schoolsLoading = true;
    ResponseModel response;
    if (!appController.isInReview) {
      response = await dataController.getData(
        url: API.schoolsApi,
      );
      if (response.success) {
        response.data
            .forEach((element) => schools.add(SchoolModel.fromJson(element)));
        schoolsLoading = false;
      }
    } else {
      schools = FakeRepo.schools;
      schoolsLoading = false;
    }
  }

  void loadMajorsData() async {
    majors = [];
    majorsLoading = true;
    ResponseModel response;
    if (!appController.isInReview) {
      response = await dataController.getData(
        url: API.majorsApi,
      );
      if (response.success) {
        response.data.forEach((element) => majors.add(Major.fromJson(element)));
        majorsLoading = false;
      }
    } else {
      majors = FakeRepo.majors;
      majorsLoading = false;
    }
  }

  final ImagePicker picker = ImagePicker();
  File? imageFile;
  RxString _imagePath = ''.obs;
  String get imagePath => this._imagePath.value;
  set imagePath(String value) => this._imagePath.value = value;

  imgFromGallery() async {
    bool hasGalleryAccess = !(await Permission.photos.isDenied);
    bool hasStorageAccess = !(await Permission.storage.isDenied);

    if (!hasGalleryAccess || !hasStorageAccess) {
      await Permission.photos.request();
      await Permission.storage.request();
    }
    DialogHelper.showDialog(dialogBody: PermissionDialogForAccessToPhotos(
      onAllow: () async {
        Get.back();

        final pickedFile = await picker.pickImage(source: ImageSource.gallery);
        if (pickedFile != null) {
          imageFile = File(pickedFile.path);
          imagePath = imageFile!.path;
        }
      },
    ));
  }

  signUp(context) async {
    String? deviceId = await globalController.getDeviceId();
    String? fcmToken = await notificationController.fcmToken();

    var formData = FormData.fromMap({});
    if (isPrivacyAccepted.value == false) {
      appController.showToast(context,
          message:
              "You have to agree to our privacy policy before signing up".tr);
    }
    if (formKey.currentState!.validate()) {
      if (imageFile != null)
        formData.files.add(
            MapEntry('avatar', await MultipartFile.fromFile(imageFile!.path)));
      formData.fields.add(MapEntry('name', name.text));
      formData.fields.add(MapEntry(
          'dial_country_code', appController.isInReview ? "001" : "963"));

      formData.fields.add(MapEntry('phone', phone.text));
      formData.fields.add(MapEntry('password', password.text));
      formData.fields
          .add(MapEntry('password_confirmation', passwordConfirmation.text));

      formData.fields.add(MapEntry('role', 'student'));
      formData.fields.add(MapEntry('password', password.text));
      formData.fields.add(MapEntry('year_id', yearsId.toString()));
      formData.fields.add(MapEntry('major_id', majorId.toString()));
      formData.fields.add(MapEntry('college_id', schoolId.toString()));

      if (cityId != null)
        formData.fields.add(MapEntry('city_id', cityId.toString()));
      // formData.fields.add(MapEntry('school_id', schoolId.toString()));
      if (birthDate.text.isNotEmpty)
        formData.fields.add(MapEntry('birthday', birthDate.text));
      formData.fields.add(MapEntry('mobile_id', deviceId!));
      formData.fields.add(MapEntry('device_token', fcmToken ?? ''));

      ResponseModel response;
      try {
        response = await dataController.postData(
          url: API.signUp,
          body: formData,
        );
        if (response.success) {
          Get.back();
          await appController.setUserData(
              user: UserModel.fromJson(response.data),
              token: response.data['token'],
              role: Role.user);
          Nav.replacement(
            Pages.navBar,
          );
          resetFields();
        } else if (response.code == ErrorCode.VALIDATION_ERROR ||
            response.errors != null ||
            response.errors!.isNotEmpty) {
          Get.back();
          appController.showToast(context, message: response.errorsAsString);
        } else {
          Get.back();
          appController.showToast(Get.context!,
              message: response.message!, status: ToastStatus.fail);
        }
      } catch (e) {
        Get.back();
      }
    } else {
      Get.back();
    }
  }

  navToLogin() {
    Nav.replacement(Pages.login);
  }

  resetFields() {
    name.clear();
    phone.clear();
    birthDate.clear();
    cityId = null;
    yearsId = null;
    schoolId = null;
  }

  @override
  void onInit() {
    super.onInit();
    loadYearsData();
    loadSchoolsData();
    loadMajorsData();
    loadCitiesData();
  }

  @override
  void onClose() {
    name.dispose();
    phone.dispose();
    birthDate.dispose();
    schoolController.dispose();
    super.onClose();
  }
}
