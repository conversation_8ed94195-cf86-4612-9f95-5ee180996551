

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/validator.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/features/course/contollers/subscription_controller.dart';

class ConfirmSubscriptionDialogBodyWidget extends StatelessWidget {
  const ConfirmSubscriptionDialogBodyWidget();

  @override
  Widget build(BuildContext context) {
    SubscriptionController controller = Get.find();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
            alignment: Alignment.topRight,
            child: Padding(
              padding: const EdgeInsets.only(top: 5 , right: 5),
              child: GestureDetector(
                child: Assets.icons.cancelDialog.svg(),
                onTap: ()=> controller.closeDialog(),
              ),
            )
        ),
        Text(
          "Confirm Subscription".tr,
          textAlign: TextAlign.center,
          style: Get.textTheme.titleLarge!.copyWith(
              fontWeight: FontWeight.w700
          ),
        ),
        SizedBox(height: 16),
        Text(
          controller.subscriptionFirstText,
          textAlign: TextAlign.center,
          style: Get.textTheme.titleSmall!.copyWith(
            color: AppStyle.primaryColor,
              fontWeight: FontWeight.w500
          ),
        ),
        SizedBox(height: 25),
        Align(
          alignment: AlignmentDirectional.topStart,
          child: Text(
            controller.subscriptionSecondText,
            textAlign: TextAlign.center,
            style: Get.textTheme.titleSmall!.copyWith(
              color: AppStyle.boldGreyTextColor,
              fontWeight: FontWeight.w500
            ),
          ),
        ),
        SizedBox(height: 25),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Expanded(
                child: AppButton(
                  height: 50,
                  radius: 15,
                  withLoading: false,
                  margin: EdgeInsets.zero,
                  fillColor: AppStyle.whiteColor,
                  style: Get.textTheme.bodyMedium!.copyWith(color: AppStyle.primaryColor,fontWeight: FontWeight.w700),
                  text: 'Cancel'.tr,
                  onTap: ()async => controller.closeDialog(),
                ),
              ),
              const SizedBox(width: 15,),
              Expanded(
                child: AppButton(
                  height: 50,
                  withLoading: false,
                  radius: 15,
                  margin: EdgeInsets.zero,
                  style: Get.textTheme.bodyMedium!.copyWith(color: AppStyle.whiteColor,fontWeight: FontWeight.w700),
                  text: 'Confirm'.tr,
                  onTap: ()async => controller.confirmSubscriptionLoading?null:controller.confirmCodeSubscription(),
                  moreWidget: Obx(
                        ()=> controller.confirmSubscriptionLoading?SizedBox(
                        width: 20,
                        height: 15,
                        child: Padding(
                          padding: const EdgeInsetsDirectional.only(end: 4),
                          child: CircularProgressIndicator(color: AppStyle.whiteColor,strokeWidth: 2),
                        ),
                    ):SizedBox(),
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 12),
      ],
    );
  }
}
