// import 'package:all_in_one/core/constants/defaults.dart';
// import 'package:all_in_one/core/routes.dart';
// import 'package:all_in_one/core/style/assets.gen.dart';
// import 'package:all_in_one/core/style/style.dart';
// import 'package:all_in_one/core/widgets/dialog_helper.dart';
// import 'package:all_in_one/features/nav_bar_pages/nav_bar/controller.dart';
// import 'package:all_in_one/features/nav_bar_pages/nav_bar/widget/side_menu_item.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import '../../../../core/controllers/app_controller.dart';
// import '../../../../core/widgets/image.dart';
//
// class SideMenu extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     AppController appController = Get.find();
//     NavBarController navBarController = Get.find();
//     return Container(
//       decoration: BoxDecoration(
//         color: Colors.white,
//         borderRadius: BorderRadiusDirectional.only(
//           topEnd: Radius.circular(30),
//           bottomEnd: Radius.circular(30),
//         )
//       ),
//       child: ListView(
//         children: [
//           InkWell(
//             onTap: (){
//               navBarController.closeDrawer();
//               navBarController.selectedIndex=2;
//             },
//             child: Padding(
//               padding: EdgeInsets.symmetric(horizontal: 15,vertical: 20),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Obx(
//                       ()=> AppImage(
//                         height: 60,
//                         width: 60,
//                         borderRadius: BorderRadius.circular(100),
//                         type: ImageType.CachedNetwork,
//                         path: appController.user.avatar.value,
//                       ),
//                   ),
//                   SizedBox(width: 6),
//                   Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Obx(()=> Text(
//                         appController.user.name.value,
//                         style: Get.textTheme.titleMedium!.copyWith(color: AppStyle.blackColor),),),
//                       SizedBox(height: 8),
//                       Text(appController.user.phone,style: Get.textTheme.bodyMedium!.copyWith(color: AppStyle.boldGreyTextColor.withOpacity(0.5))),
//                     ],
//                   )
//                  ],
//               ),
//             ),
//           ),
//           const SizedBox(height: 10,),
//           Container(
//             height: 20,
//             color: AppStyle.spacesColor,
//           ),
//           const SizedBox(height: 10,),
//           SideMenuItem(
//             itemName: 'Home'.tr,
//             onTap: () {
//               navBarController.closeDrawer();
//               navBarController.selectedIndex=0;
//             },
//             icon: Assets.icons.homeIcon.path,
//           ),
//
//
//           SizedBox(height: 10,),
//           Container(
//             height: 20,
//             color: AppStyle.spacesColor,
//           ),
//           SizedBox(height: 10,),
//           SideMenuItem(
//             itemName: navBarController.isUserGuest()?'Login'.tr:'Logout'.tr,
//             onTap: () => navBarController.isUserGuest()?Nav.offAll(Pages.login):DialogHelper.showDialog(
//                 title: "Confirm log out".tr,
//                 description: "When you click Confirm Sign Out, you will be logged out of your account".tr,
//                 titleActionFirst: 'Confirm'.tr,
//                 titleActionSecond: 'Cancel'.tr,
//                 onTapActionFirst: ()async{
//                   Get.back();
//                   navBarController.logOut(context);
//                 },
//                 onTapActionSecond: ()async => Get.back()
//             ),
//             icon: Assets.icons.logOutIcon.path,
//           ),
//
//         ],
//       ),
//     );
//   }
// }
