abstract class API {
  //##########  Base Url  ##########
  static const String setup = 'startup';
  static const String something = '';
  static String someThing(int id) => '$id';

  //########### AUTH ###############
  static const String signIn = 'login';
  static const String signUp = 'register';
  static const String majorsApi = 'majors';

  static const String citiesApi = 'cities';
  static const String yearsApi = 'years';
  static const String schoolsApi = 'colleges';
  static const String logout = 'logout';
  static const String deleteAccount = 'profile/delete';
  static const String verifyCode = 'verify-otp';
  static const String resendCode = 'resend-otp';
  static const String lesson = 'lessons';

  //########### Home ###############
  static const String homeApi = 'home';
  static const String subjects = 'subjects';

  static const String subjectApi = 'subject/lectures';
  static String lessonCntApi(int id, String quality) =>
      'lessons/cnt/$id?type=$quality';
  static String settingDataApi(String type) => 'setting?data=$type';
  static const String settingApi = 'settings';
  static const String mySubjectApi = 'subjects?filter=subscribed';
  static String tasksApi(String? date) =>
      date != null ? 'tasks?date=$date' : 'tasks';
  static const String addTasksApi = 'tasks';
  static const String taskColorsApi = 'colors';
  static const String tasksByDateApi = 'tasks_by_date';
  static String examApi(int id) => 'lecture/quizes/$id';
  static String notesApi(int id) => 'comments?video_id=$id';
  static const String addNoteApi = 'comments';
  static const String checkIfSubjectIsExpire = 'comments';
  static const String mtn = 'mtn_payments';
  static const String verifyMtnPayment = 'mtn_confirm';

  //########### Subscription ###############
  static String checkCodeApi(String code) => 'codes/$code';
  static const String subscribeCodeApi = 'subscriptions';
  static const String subscribeFatoryApi = 'payments';

  //########### notification ###############
  static const String getNotifications = 'notices';

  //########### Profile ###############
  static const String updateProfile = 'profile';
  static String refresh(int lessonId) => 'refresh/$lessonId';
  static const String passwordResets = 'password_forget';
}
