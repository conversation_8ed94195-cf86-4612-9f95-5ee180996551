import 'package:all_in_one/core/constants/enum.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:flutter/material.dart';

class AppDatePicker extends StatelessWidget {
  final TextEditingController controller;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? initialValue;
  final String? hint;
  final String? label;
  final TextInputType? keyboardType;
  final String? Function(String?)? onFieldSubmitted;
  final Function(String?)? onChanged;
  final bool isSecure;
  final Function()? onTap;
  final EdgeInsetsGeometry? contentPadding;
  final bool isEnabled;
  final String? Function(String?)? validator;
  final TextStyle? textStyle;
  final Color? color;
  final TextAlign textAlign;
  final FieldTypeEnum type;
  final int minLine;
  final int maxLine;
  final String? errorText;
  const AppDatePicker(
      this.controller, {
        this.hint,
        this.onFieldSubmitted,
        this.label,
        this.suffixIcon,
        this.onTap,
        this.textStyle,
        this.isEnabled = true,
        this.contentPadding,
        this.isSecure = false,
        this.keyboardType,
        this.errorText,
        this.onChanged,
        this.textAlign = TextAlign.start,
        this.color,
        this.prefixIcon,
        this.initialValue,
        this.validator,
        this.minLine=1,
        this.maxLine=1,
        required this.type,
        Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(inputDecorationTheme: type.getTextFieldDecoration()),
      child: TextFormField(
        controller: controller,
        validator: validator,
        keyboardType: keyboardType,
        obscureText: isSecure,
        readOnly: !isEnabled,
        onTap: onTap,
        minLines: minLine,
        maxLines: maxLine,
        initialValue: initialValue,
        style: textStyle,
        textAlign: textAlign,
        cursorColor: AppStyle.primaryColor,
        onFieldSubmitted: onFieldSubmitted,
        onChanged: onChanged,
        decoration: InputDecoration(
          contentPadding: contentPadding,
          errorText: errorText,
          errorStyle: TextStyle(color: AppStyle.redColor),
          isDense: true,
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
          hintText: hint,
          label: label!=null?Text(label??''):null,
        ),
      ),
    );
  }
}
