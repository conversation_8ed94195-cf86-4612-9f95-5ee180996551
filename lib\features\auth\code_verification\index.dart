import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/validator.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/features/auth/code_verification/controller.dart';
import 'package:all_in_one/features/auth/widgets/app_auth_page.dart';
import 'package:all_in_one/features/auth/widgets/auth_page_switcher_widget.dart';

class CodeVerificationPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    CodeVerificationPageController controller =
        Get.put(CodeVerificationPageController());
    return Scaffold(
      key: controller.scaffoldKey,
      body: AppAuthPage(
        title: 'تسجيل الدخول',
        child: Align(
          alignment: Alignment.bottomCenter,
          child: Container(
            height: Get.height * 0.7,
            decoration: BoxDecoration(
                color: AppStyle.whiteColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(25),
                  topRight: Radius.circular(25),
                )),
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 25),
            child: Form(
              key: controller.formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'أهلا بعودتك',
                      style: Theme.of(context)
                          .textTheme
                          .headlineLarge
                          ?.copyWith(
                              color: AppStyle.blackTextColor,
                              fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 10),
                    Padding(
                      padding: const EdgeInsetsDirectional.only(end: 100),
                      child: Text(
                        'قم بتسجيل الدخول باستخدام رقم الهاتف للمتابعة',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: AppStyle.lightBlackColor,
                            ),
                      ),
                    ),
                    const SizedBox(
                      height: 50,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 30),
                      child: Directionality(
                        textDirection: TextDirection.ltr,
                        child: Pinput(
                          controller: controller.pinCode,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          length: 4,
                          validator: (input) =>
                              Validator.verifyCodeValidation(input!),
                          errorTextStyle: Theme.of(context)
                              .textTheme
                              .bodyLarge!
                              .copyWith(color: AppStyle.redColor),
                          submittedPinTheme: PinTheme(
                            height: 65,
                            width: 65,
                            textStyle: Get.textTheme.titleLarge,
                            decoration: BoxDecoration(
                              color: AppStyle.textFieldColor.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                          defaultPinTheme: PinTheme(
                            height: 65,
                            width: 65,
                            textStyle: Get.textTheme.titleLarge,
                            decoration: BoxDecoration(
                              color: AppStyle.textFieldColor.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(
                      height: 50,
                    ),
                    AppButton(
                      text: 'Verify'.tr,
                      height: 60,
                      radius: 10,
                      withLoading: true,
                      margin: EdgeInsets.zero,
                      style: Get.textTheme.titleMedium!.copyWith(
                          color: AppStyle.whiteColor,
                          fontWeight: FontWeight.bold),
                      onTap: () async => controller.verifyCode(context),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Obx(
                      () => AuthPageSwitcherWidget(
                        label: controller.time.isEmpty
                            ? 'don`t get a code?'.tr
                            : 'Resend : '.tr,
                        onTapLabel: controller.time.isEmpty
                            ? 'click to resend'.tr
                            : controller.time,
                        onTap: () => controller.time.isEmpty
                            ? controller.resendCode(context)
                            : null,
                      ),
                    ),
                    // Center(
                    //   child: Obx(
                    //         () => controller.time.isEmpty
                    //         ? Wrap(
                    //       alignment: WrapAlignment.center,
                    //       children: [
                    //         Padding(
                    //           padding: const EdgeInsets.only(top: 5),
                    //           child: Text(
                    //             "don`t get a code?".tr,
                    //             style: TextStyle(fontSize: 16),
                    //           ),
                    //         ),
                    //         InkWell(
                    //           onTap: controller.time.isEmpty
                    //               ? () => controller.resendCode(context)
                    //               : null,
                    //           child: Padding(
                    //             padding: const EdgeInsets.all(5),
                    //             child: Text(
                    //               'click to resend'.tr,
                    //               style: controller.time.isEmpty
                    //                   ? TextStyle(
                    //                   fontWeight: AppFontWeight.bold,
                    //                   fontSize: 16)
                    //                   : null,
                    //             ),
                    //           ),
                    //         ),
                    //       ],
                    //     )
                    //         : Text('Resend : '.tr+controller.time,
                    //         textAlign: TextAlign.center,
                    //         style: Get.textTheme.bodyLarge!
                    //             .copyWith(color: AppStyle.primaryColor)),
                    //   ),
                    // ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
