import 'package:flutter/material.dart';
import 'package:flutter_cached_pdfview/flutter_cached_pdfview.dart';
import 'package:get/get.dart';
import 'package:secure_application/secure_application_controller.dart';
import 'package:secure_application/secure_application_provider.dart';
// import 'package:native_pdf_view/native_pdf_view.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/url_launcher.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/features/course/contollers/pdf_reader_controller.dart';
import 'package:all_in_one/features/widgets/app_bar_widget.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

class PdfReaderPage extends StatefulWidget {
  @override
  _PdfReaderPageState createState() => _PdfReaderPageState();
}

class _PdfReaderPageState extends State<PdfReaderPage> {
  late final SecureApplicationController? lockController;

  @override
  void initState() {
    WakelockPlus.enable();
    lockController = SecureApplicationProvider.of(context, listen: false);
    lockController?.secure();
    super.initState();
  }

  onOrderNowAction(String link) async {
    DialogHelper.showDialog(
        dialogBody: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          "order now",
          style: Get.textTheme.titleMedium,
        ),
        const SizedBox(height: 24),
        AppButton(
            radius: 12,
            text: "order now".tr,
            style:
                Get.textTheme.bodyMedium?.copyWith(color: AppStyle.whiteColor),
            onTap: () async {
              await UrlLauncher.openLauncher(link);
            })
      ],
    ));
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    lockController?.open();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    PdfReaderController controller = Get.put(PdfReaderController());
    return Scaffold(
        floatingActionButton: controller.link != null
            ? GestureDetector(
                onTap: () async => await onOrderNowAction(controller.title),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      color: AppStyle.primaryColor),
                  child: Text(
                    "Order now".tr,
                    style: Get.textTheme.bodyMedium
                        ?.copyWith(color: AppStyle.whiteColor),
                  ),
                ),
              )
            : null,
        body: Column(
          children: [
            AppBarWidget(
              centerWidget: Text(
                controller.title,
                style: Theme.of(context).textTheme.titleLarge!.copyWith(
                    color: AppStyle.whiteColor, fontWeight: AppFontWeight.bold),
              ),
              trailingIcon: SizedBox(
                width: 35,
                height: 35,
              ),
              leadingIcon: Icon(
                Icons.arrow_back,
                color: Colors.white,
              ),
              trailingOnTap: () {},
              leadingOnTap: () {
                Get.back();
              },
            ),
            Expanded(
              child: const PDF().cachedFromUrl(
                controller.url,
                placeholder: (double progress) =>
                    Center(child: Text('$progress %')),
                errorWidget: (dynamic error) =>
                    Center(child: Text(error.toString())),
              ),
            )
          ],
        ));
  }
}
