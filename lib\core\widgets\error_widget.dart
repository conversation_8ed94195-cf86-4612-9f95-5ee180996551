import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';

import 'button.dart';

class AppErrorWidget extends StatelessWidget {
  final String error;
  final VoidCallback onTap;
  final bool withButton;
  const AppErrorWidget({this.error = '',required this.onTap,this.withButton=true});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Assets.dhLogo.image(width: 100,height: 100,fit: BoxFit.fill),
        const SizedBox(height: 10,),
        Text(
          error,
          style: Theme.of(context).textTheme.titleSmall!.copyWith(fontWeight: AppFontWeight.bold),
        ),
        const SizedBox(height: 8,),
        Visibility(
          visible: withB<PERSON>on,
          child: A<PERSON><PERSON><PERSON><PERSON>(
            text: "Try Again".tr,
            onTap: () async {
              onTap();
            },
            margin: EdgeInsets.symmetric(horizontal: Get.width*0.3),
            withLoading: false,
          ),
        ),
      ],
    );
  }
}
