import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:in_app_update/in_app_update.dart';

import 'model.dart';

enum ErrorCode {
  CANCELED,
  NO_INTERNET,
  UN_AUTHORIZED,
  VALIDATION_ERROR,
  SERVER_ERROR,
  NOT_ENOUGH_MONEY,
}

enum SystemCode {
  normal,
  updateAvailable,
  updateRequired;

  static SystemCode fromInt(int val) {
    switch (val) {
      case 0:
        return normal;
      case 1:
        return updateAvailable;
      case 2:
        return updateRequired;
      default:
        return SystemCode.updateRequired;
    }
  }

  codeHandler() {
    switch (this) {
      case normal:
        return;
      case updateAvailable:
        AppController appController = Get.find();
        if (appController.optionalUpdate) {
          return;
        }
        appController.optionalUpdate = true;
        return DialogHelper.showDialog(
            dialogBody: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "update available".tr,
              style: Get.textTheme.titleLarge
                  ?.copyWith(color: AppStyle.primaryColor),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                AppButton(
                  textColor: AppStyle.whiteColor,
                  radius: 15,
                  style: Get.textTheme.bodyMedium!
                      .copyWith(color: AppStyle.whiteColor),
                  text: "Update".tr,
                  onTap: () async {
                    (await InAppUpdate.checkForUpdate());
                  },
                ),
                const Spacer(),
                AppButton(
                  text: "cancel".tr,
                  onTap: () async {
                    Get.back();
                  },
                ),
              ],
            )
          ],
        ));

      case SystemCode.updateRequired:
        return DialogHelper.showDialog(
            dialogBody: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "Sorry this version of the app is no longer supported".tr,
              style: Get.textTheme.titleLarge
                  ?.copyWith(color: AppStyle.primaryColor),
            ),
            const SizedBox(height: 16),
            AppButton(
                text: "Update".tr,
                radius: 15,
                textColor: AppStyle.whiteColor,
                style: Get.textTheme.bodyMedium!
                    .copyWith(color: AppStyle.whiteColor),
                onTap: () async {
                  (await InAppUpdate.checkForUpdate());
                })
          ],
        ));
    }
  }
}

class ResponseModel extends Model {
  var data;
  late int? status;
  late bool success;
  dynamic message;
  dynamic extra;
  Map<String,List>? errors;
  ErrorCode? code;
  SystemCode? systemCode;

  ResponseModel(
      {this.status,
      this.data,
      this.errors,
      this.message,
      this.code,
      this.systemCode,
      required this.success,
      this.extra});

  @override
  ResponseModel.fromJson(Map<String, dynamic> json, {String? extraParam}) {
    status = json['status'] ?? 200;
    success = json['success'] ?? false;
    systemCode = SystemCode.fromInt(json['code']??0);
    message = json['message'] ?? 'IDK';
    data = json['data'];
    extra = json[extraParam];
    errors = json['errors'] != null ?Map.from(json['errors']):null;
    systemCode?.codeHandler();
  }

  ErrorCode? getCode(String? code) {
    if (code != null) {
      return ErrorCode.values
          .firstWhere((element) => element.name == code, orElse: null);
    }
    return null;
  }
  String get errorsAsString {
    List<String> stringErrors = [];
  
    errors?.forEach((key,value){
      stringErrors.add("$key:${value.join(",")}");
    });
    return stringErrors.join("\n");
  }
  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['data'] = this.data;
    data['message'] = this.message;
    return data;
  }
}
