
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/widgets/button.dart';
import 'package:all_in_one/features/nav_bar_pages/offline/offline_controller.dart';



class DeleteLessonDialog extends StatelessWidget {
  final int subjectId;
  final int lessonId;
  final int sectionId;
  final int lessonIndex;
  final int sectionIndex;
  final String lessonName;
  const DeleteLessonDialog({
    required this.lessonName,
    required this.subjectId,
    required this.lessonId,
    required this.sectionId,
    required this.lessonIndex,
    required this.sectionIndex,
  });

  @override
  Widget build(BuildContext context) {
    OfflineController controller = Get.find();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Align(
            alignment: Alignment.topRight,
            child: Padding(
              padding: const EdgeInsets.only(top: 5 , right: 5),
              child: GestureDetector(
                child: Assets.icons.cancelDialog.svg(),
                onTap: ()=>Get.back(),
              ),
            )
        ),
        Text(
          "Confirm delete lesson".tr,
          textAlign: TextAlign.start,
          style: Get.textTheme.titleLarge!.copyWith(
              fontWeight: FontWeight.w700
          ),
        ),
        SizedBox(height: 16),
        Text(
          "When you click the confirm button, the downloaded lesson will be deleted. Are you sure you want to delete the lesson?".tr,
          textAlign: TextAlign.center,
          style: Get.textTheme.titleSmall!.copyWith(
              fontWeight: FontWeight.w500
          ),
        ),
        SizedBox(height: 25),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            children: [
              Expanded(
                child: AppButton(
                  height: 50,
                  radius: 18,
                  withLoading: false,
                  margin: EdgeInsets.zero,
                  fillColor: AppStyle.whiteColor,
                  style: Get.textTheme.bodyMedium!.copyWith(color: AppStyle.primaryColor,fontWeight: FontWeight.w700),
                  text: 'Cancel'.tr,
                  onTap: ()async => Get.back(),
                ),
              ),
              const SizedBox(width: 15,),
              Expanded(
                child: AppButton(
                  height: 50,
                  withLoading: false,
                  radius: 15,
                  margin: EdgeInsets.zero,
                  style: Get.textTheme.bodyMedium!.copyWith(color: AppStyle.whiteColor,fontWeight: FontWeight.w700),
                  text: 'Confirm'.tr,
                  onTap: ()async{
                    controller.deleteLessonFromMemory(
                      subjectId: subjectId,
                      sectionId: sectionId,
                      lessonId: lessonId,
                      lessonName: lessonName,
                      // lessonIndex: lessonIndex,
                      // sectionIndex: sectionIndex
                    );
                    Get.back();
                  },
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 12),
      ],
    );
  }
}