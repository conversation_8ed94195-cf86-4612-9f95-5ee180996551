import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/routes.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/features/nav_bar_pages/home/<USER>/home_controller.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/tasks_page.dart';

class HomePageTasksSection extends StatelessWidget {
  const HomePageTasksSection();

  @override
  Widget build(BuildContext context) {
    HomePageController controller = Get.find();
    return Visibility(
      visible: controller.homeResponse.tasks.isNotEmpty,
      // visible: true,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: ()=> Get.to(TasksPage(fromNavBar: false)),
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Tasks'.tr,
                      style: Theme.of(context).textTheme.titleMedium!.copyWith(
                          color: AppStyle.mediumBlackTextColor,
                          fontWeight: FontWeight.w900
                      ),
                    ),
                    Text(
                      'Show more'.tr,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: AppStyle.mediumBlackTextColor,
                          fontWeight: FontWeight.w900
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 10,),
            Wrap(
              spacing: 10,
              runSpacing: 10,
              crossAxisAlignment: WrapCrossAlignment.start,
              children: [
                for(int index=0;index< controller.homeResponse.tasks.length;index++)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 50,vertical: 10),
                  decoration: BoxDecoration(
                    color: controller.homeResponse.tasks[index].color,
                    borderRadius: BorderRadius.circular(20)
                  ),
                  child: Text(
                    controller.homeResponse.tasks[index].title,
                    style: Theme.of(context).textTheme.titleSmall!.copyWith(
                        color: AppStyle.blackColor,
                        fontWeight: FontWeight.w800
                    ),
                  ),
                )
              ],
            ),

          ],
        ),
      ),
    );
  }
}
