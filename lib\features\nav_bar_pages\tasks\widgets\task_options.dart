import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/models/task_model.dart';
import 'package:all_in_one/features/nav_bar_pages/tasks/widgets/task_list_item_widget.dart';

class TaskOptions extends StatelessWidget {
  final TaskModel task;
  final Function(TaskModel) onEdit;
  final Function(int) onDelete;

  TaskOptions(
      {required this.task, required this.onEdit, required this.onDelete});

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<int>(
      offset: Offset(0, 40),
      icon: Icon(Icons.more_vert, color: Colors.black),
      onSelected: (value) {
        if (value == 0) {
          onEdit(task); 
        } else if (value == 1) {
          onDelete(task.id); 
        }
      },
      itemBuilder: (context) => [
        PopupMenuItem(
          value: 0,
          child: TaskTileButton(
            labael: 'Edit'.tr,
            icon: Assets.icons.edit.path,
            iconColor: AppStyle.blackColor,
          ),
        ),
        PopupMenuItem(
          value: 1,
          child: TaskTileButton(
            labael: 'delete'.tr,
            icon: Assets.icons.delete.path,
            iconColor: AppStyle.redColor,
          ),
        ),
      ],
    );
  }
}
