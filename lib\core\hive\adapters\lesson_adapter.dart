import 'package:hive/hive.dart';
part 'lesson_adapter.g.dart';

@HiveType(typeId: 2)
class LessonAdapter extends HiveObject {
  @HiveField(0)
  int id;
  @HiveField(1)
  String title;
  @HiveField(2)
  String subTitle;

  LessonAdapter({
    required this.id,
    required this.title,
    required this.subTitle,
  });
  factory LessonAdapter.fromJson(Map<String, dynamic> json) => LessonAdapter(
      id: json['id'], title: json['title'], subTitle: json['subTitle']);
  Map<String, dynamic> toJson() =>
      {"id": id, "title": title, "subTitle": subTitle};
}
