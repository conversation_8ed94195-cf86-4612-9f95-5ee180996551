import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Loading {
  static overlayLoading(BuildContext context, {bool showText = true}) =>
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return Dialog(
            backgroundColor: Colors.white,
            child: SizedBox(
              height: 100,
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: CircularProgressIndicator(),
                    ),
                    if (showText)
                      Text(
                        "Loading...".tr,
                        style: Theme.of(context)
                            .textTheme
                            .bodyLarge!
                            .copyWith(fontWeight: FontWeight.bold),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      );
}
