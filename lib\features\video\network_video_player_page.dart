import 'dart:developer';
import 'dart:io';
import 'dart:isolate';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/circular_percent_indicator.dart';
import 'package:secure_application/secure_application.dart';
import 'package:all_in_one/core/controllers/app_controller.dart';
import 'package:all_in_one/core/style/assets.gen.dart';
import 'package:all_in_one/core/style/style.dart';
import 'package:all_in_one/core/utils/encription_helper.dart';
import 'package:all_in_one/core/widgets/dialog_helper.dart';
import 'package:all_in_one/core/widgets/toast.dart';
import 'package:all_in_one/core/widgets/zoom_widget.dart';
import 'package:all_in_one/features/course/contollers/note_controller.dart';
import 'package:all_in_one/features/video/bottom_sheet/notes_bottom_sheet.dart';
import 'package:all_in_one/features/video/controller/network_video_player_controller.dart';
import 'package:all_in_one/features/video/widgets/custom_vedio.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';
import 'widgets/select_video_quality_dialog.dart';
import 'widgets/video_widget.dart';

class NetworkVideoPlayerPage extends StatefulWidget {
  final String youtubeUrl;
  final List<String> videoUrls;
  final String lessonName;
  final String lessonSubTitle;
  final int lessonId;
  final int sectionId;
  final bool isFree;
  final bool isHd;
  const NetworkVideoPlayerPage(
      {required this.youtubeUrl,
      required this.videoUrls,
      required this.lessonName,
      required this.lessonSubTitle,
      required this.lessonId,
      required this.sectionId,
      required this.isFree,
      this.isHd = false});

  @override
  _NetworkVideoPlayerPageState createState() => _NetworkVideoPlayerPageState();
}

class _NetworkVideoPlayerPageState extends State<NetworkVideoPlayerPage> {
  late final SecureApplicationController? lockController;
  late NetworkVideoPlayerController controller;
  late NoteController noteController;
  AppController appController = Get.find();
  late bool isHd = widget.isHd;
  late YoutubePlayerController youtubePlayerController =
      YoutubePlayerController(
    initialVideoId: YoutubePlayer.convertUrlToId(widget.youtubeUrl)!,
    flags: YoutubePlayerFlags(
        disableDragSeek: true,
        hideControls: false,
        forceHD: true,
        enableCaption: false,
        hideThumbnail: true),
  );

  @override
  void initState() {
    WakelockPlus.enable();
    controller =
        Get.put(NetworkVideoPlayerController(), tag: "${widget.lessonId}");
    noteController = Get.put(NoteController())..initialId(widget.lessonId);

    controller.initialControllerVariable(
        lessonName: widget.lessonName,
        lessonSubTitle: widget.lessonSubTitle,
        lessonId: widget.lessonId,
        sectionId: widget.sectionId,
        videoUrls: widget.videoUrls);

    controller.incrementVideoCount('360');
    lockController = SecureApplicationProvider.of(context, listen: false);
    lockController?.secure();
    controller.enterFullScreen();
    super.initState();
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    lockController?.open();
    controller.dispose();
    controller.exitFullScreen();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // return VideoWidget(videoUrls: widget.videoUrls);
    return WillPopScope(
        onWillPop: () async {
          controller.closeVideo();
          return true;
        },
        child: Scaffold(
          backgroundColor: Colors.black,
          body: SafeArea(
            child: Stack(
              alignment: Alignment.center,
              clipBehavior: Clip.none,
              children: [
                GestureZoomBox(
                  maxScale: 4,
                  duration: const Duration(milliseconds: 200),
                  child: FutureBuilder(
                    future: controller.getVideoUrl(widget.lessonId, isHd),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        if (snapshot.data == "") {
                          if (appController.isGuestUser() == false) {
                            appController.showToast(Get.context!,
                                message: 'Something went wrong'.tr,
                                status: ToastStatus.warning);
                            return const SizedBox.shrink();
                          }
                        }
                        log(snapshot.data ?? "", name: "New Link");
                        String? youtubeUrl =
                            YoutubePlayer.convertUrlToId(snapshot.data!);
                        log(youtubeUrl ?? "", name: "Youtube New Link");
                        if (youtubeUrl != null) {
                          youtubePlayerController.load(youtubeUrl);
                        }
                        return (isHd || Platform.isIOS)
                            ? YoutubePlayer(controller: youtubePlayerController)
                            : VideoWidget(
                                youtubeUrl: widget.youtubeUrl,
                                lessonId: widget.lessonId,
                                videoUrls: [snapshot.data!, snapshot.data!],
                              );
                      } else {
                        return Center(child: CircularProgressIndicator());
                      }
                    },
                  ),
                ),
                PositionedDirectional(
                  top: 20,
                  start: 20,
                  child: InkWell(
                    onTap: () async {
                      controller.closeVideo();
                    },
                    child: CircleAvatar(
                      backgroundColor: Colors.white,
                      radius: 15,
                      child: Icon(
                        Icons.arrow_back,
                        color: Colors.black,
                      ),
                    ),
                  ),
                ),
                PositionedDirectional(
                  top: 20,
                  start: 60,
                  child: InkWell(
                    onTap: () async {
                      ShowNotesBottomSheet.showBottomSheet(context);
                    },
                    child: CircleAvatar(
                      backgroundColor: Colors.white,
                      radius: 15,
                      child: Icon(
                        Icons.notes,
                        color: Colors.black,
                      ),
                    ),
                  ),
                ),
                PositionedDirectional(
                  top: 20,
                  end: 20,
                  child: Visibility(
                    visible: !appController.isGuestUser(),
                    child: Obx(() => controller.downloadingComplete
                        ? const SizedBox()
                        : controller.downloadVideoLoading
                            ? Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  CircleAvatar(
                                    backgroundColor: Colors.white,
                                    radius: 15,
                                    child: CircularPercentIndicator(
                                      radius: 15,
                                      lineWidth: 2,
                                      backgroundColor: AppStyle.primaryColor
                                          .withOpacity(0.3),
                                      percent: controller.downloadedValue.value,
                                      center: Text(
                                        (controller.downloadedValue * 100)
                                                .ceil()
                                                .toString() +
                                            '%',
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                            fontSize: 11,
                                            color: AppStyle.primaryColor),
                                      ),
                                      progressColor: AppStyle.primaryColor,
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                  GestureDetector(
                                    onTap: () => controller.cancelDownload(),
                                    child: CircleAvatar(
                                        backgroundColor: Colors.white,
                                        radius: 15,
                                        child: Assets.icons.cancelDialog.svg()),
                                  ),
                                ],
                              )
                            : InkWell(
                                onTap: () async {
                                  DialogHelper.showDialog(
                                      dialogBody: SelectVideoQualityDialog(
                                    videoUrls: widget.videoUrls,
                                    lessonId: widget.lessonId,
                                    lessonName: "${widget.lessonName}",
                                  ));
                                },
                                child: CircleAvatar(
                                  backgroundColor: Colors.white,
                                  radius: 15,
                                  child: Icon(
                                    Icons.download,
                                    color: Colors.black,
                                  ),
                                ),
                              )),
                  ),
                ),
                if (!Platform.isIOS)
                  PositionedDirectional(
                    top: 20,
                    end: 60,
                    child: GestureDetector(
                      onTap: () async {
                        setState(() {
                          isHd = !isHd;
                        });
                      },
                      child: CircleAvatar(
                          backgroundColor: Colors.white,
                          radius: 15,
                          child: isHd
                              ? Assets.icons.sd.svg()
                              : Assets.icons.hd.svg()),
                    ),
                  ),
                PositionedDirectional(
                  top: 20,
                  end: 100,
                  child: GestureDetector(
                    onTap: () async {
                      await controller.refreshVideoLink(
                          null, widget.isHd, widget.youtubeUrl);
                    },
                    child: CircleAvatar(
                        backgroundColor: Colors.white,
                        radius: 15,
                        child: Assets.icons.refresh.svg()),
                  ),
                ),
              ],
            ),
          ),
        ));
  }
}
