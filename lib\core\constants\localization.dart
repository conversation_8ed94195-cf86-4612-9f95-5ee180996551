import 'package:flutter/material.dart';

import 'defaults.dart';

enum AppLocalization { Ar, En ,De }

extension AppLocalizations on AppLocalization {
  String get value {
    switch (this) {
      case AppLocalization.Ar:
        return 'ar';
      case AppLocalization.En:
        return 'en';
      case AppLocalization.De:
        return 'de';
      default:
        return Default.defaultLocale.value;
    }
  }

  Locale get locale {
    switch (this) {
      case AppLocalization.Ar:
        return Locale('ar');
      case AppLocalization.En:
        return Locale('en');
      case AppLocalization.De:
        return Locale('de');
      default:
        return Locale(Default.defaultLocale.value);
    }
  }
}
