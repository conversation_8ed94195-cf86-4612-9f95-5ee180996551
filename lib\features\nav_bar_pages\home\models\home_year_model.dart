// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class HomeYearModel {
  int id;
  String title;
  int price;
  Major major;

  HomeYearModel({
    required this.id,
    required this.title,
    required this.price,
    required this.major,
  });

  factory HomeYearModel.fromJson(Map<String, dynamic> json) => HomeYearModel(
        id: json['id'],
        title: json['title'],
        price: json['price'],
        major: Major.from<PERSON>son(
          json['major'],
        ),
      );
}

class Major {
  int id;
  String title;
  College college;
  String image;

  Major({
    required this.id,
    required this.title,
    required this.college,
    required this.image
  });

  factory Major.fromMap(Map<String, dynamic> map) {
    return Major(
      id: map['id'] as int,
      title: map['title'] as String,
      image:map['banner']["original_url"],
      college: College.fromMap(map['college'] as Map<String, dynamic>),
    );
  }

  factory Major.fromJson(Map<String,dynamic> source) =>
      Major.fromMap(source);
}

class College {
  int id;
  String title;

  College({
    required this.id,
    required this.title,
  });

 

  College copyWith({
    int? id,
    String? title,
  }) {
    return College(
      id: id ?? this.id,
      title: title ?? this.title,
    );
  }



  factory College.fromMap(Map<String, dynamic> map) {
    return College(
      id: map['id'] as int,
      title: map['title'] as String,
    );
  }


  factory College.fromJson(String source) => College.fromMap(json.decode(source) as Map<String, dynamic>);
}
